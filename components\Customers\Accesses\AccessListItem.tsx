"use client";

import { List, Space, Typography } from "antd";

import AccessActionButton from "./AccessActionButton";
import AccessStatusTag from "./AccessStatusTag";
import { LMS_ACCESS_TYPE } from "./CustomerAccesses";

const { Text } = Typography;

const AccessListItem = ({
  item,
  activeSegment,
  onAccessUpdateSuccess,
}: {
  item: any;
  activeSegment: string;
  onAccessUpdateSuccess?: () => void;
}) => (
  <List.Item>
    <Space
      style={{
        width: "100%",
        display: "grid",
        gridTemplateColumns: "4fr 1fr 1fr",
        alignItems: "center",
      }}
    >
      <Text>
        {activeSegment === LMS_ACCESS_TYPE ? (
          <strong>{item.courseName}</strong>
        ) : (
          <>
            <strong>{item.spaceName}</strong> in {item.community} Circle
          </>
        )}
      </Text>

      <AccessStatusTag status={item.status} />
      <div style={{ justifySelf: "end" }}>
        <AccessActionButton
          status={item.status}
          itemId={item.id}
          onAccessUpdateSuccess={onAccessUpdateSuccess}
        />
      </div>
    </Space>
  </List.Item>
);

export default AccessListItem;
