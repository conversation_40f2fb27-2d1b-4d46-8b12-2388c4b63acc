import { Metadata } from "next";

import ProductDetails from "@/components/product-catalog/Products/ProductDetails";

export const metadata: Metadata = {
  title: "Product Details",
};

type ProductIdProps = {
  productId: string;
};

export default async function ProductDetailsPage(
  props: {
    params: Promise<ProductIdProps>;
  }
) {
  const params = await props.params;
  return <ProductDetails productId={params.productId} />;
}
