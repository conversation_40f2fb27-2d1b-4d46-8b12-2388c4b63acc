import {
  getSubscriptionInvoices,
  getSubscriptionCreditNotes,
  getSubscriptionTransactions,
  initChargebee,
} from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the GET request for retrieving subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function GET(req: any) {
  try {
    const urlPart = req.url.split("subscriptions/");
    const id = urlPart[1].split("/history")[0];
    const [invoices, creditNotes, transactions] = await Promise.all([
      getSubscriptionInvoices(id),
      getSubscriptionCreditNotes(id),
      getSubscriptionTransactions(id),
    ]);
    return NextResponse.json({
      invoices: invoices,
      creditNotes: creditNotes,
      transactions: transactions,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RETRIEVE_SUBSCRIPTION_HISTORY", details: error },
      { status: 400 }
    );
  }
}
