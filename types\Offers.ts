import { CommunityAccesses, CommunityType } from "./Community";
import { Course, CourseInfo, LMSAccesses } from "./LMS";
import { ProductFamily } from "./ProductFamilies";
import { PlanDTO } from "./ProductPlans";
import { Product } from "./Products";

export type Offer = {
  id?: number;
  name?: string;
  externalName?: string;
  image?: string;
  currency?: string;
  fiscalEntity?: string;
  status?: string;
  redirectUrl?: string;
  chargebeeId?: string;
  createdAt?: string;
  config?: any;
  updatedAt?: string;
  redirectIfDisabled?: string;
  version?: string;
  deletedAt?: string;
  isForever?: boolean;
  trialDaysMonthly?: number;
  description?: string;
  cardImage?: string;
  usp?: string;
  trialDaysYearly?: number;
  slug?: string;
  paymentGateway?: string;
  bannerImage?: string;
  withProductDelivery?: boolean;
  suspendable?: boolean;
  delayPeriod?: number;
  lmsIds?: LMSAccesses;
  communityIds?: CommunityAccesses;
  checkoutPage?: {
    id?: number;
    bannerImage?: string;
    mainColor?: string;
    isLive?: boolean;
    url?: string;
    createdAt?: string;
    updatedAt?: string;
    deletedAt?: string;
    warrantySections?: Warranty[];
    template: string;
    testimony: Testimonial;
    cancellationPolicy: string;
    abandonedCartFlow: boolean;
  };
};

export type OfferListResponse = {
  items: Offer[];
  total: number;
  currentPage: number;
};

export type FilterOffer = {
  name?: string;
  productLine?: string | null;
  productFamily?: string | null;
  productPlan?: string | null;
  product?: string | null;
  status?: string | null;
} & {
  productFamilies?: ProductFamily[];
  productPlans?: PlanDTO[];
};

export type GetOffersParams = {
  name?: string;
  line?: string;
  family?: string;
  plan?: string;
  product?: string;
  status?: string;
};

export type CreateOfferProducts = {
  id: string;
  values: any;
};

export type UpdateOfferProducts = {
  id: string;
  values: any;
};

export type Warranty = {
  id?: number;
  icon?: string;
  message?: string;
  checkoutPageId?: number;
};

export type WarrantyListResponse = {
  items: Warranty[];
  total: number;
  currentPage: number;
};

export interface OfferConfig {
  products: {
    [key: string]: Array<{
      [key: string]: string;
    }>;
  };
  recommended: {
    [key: string]: Array<{
      [key: string]: string;
    }>;
  };
  quantityInfo: {
    recommended: {
      [key: string]: boolean;
    };
    products: {
      [key: string]: boolean;
    };
  };
  display: string;
  defaultBillingCycle: string;
}

export interface ForeverOfferConfig {
  isForever?: boolean;
  monthly?: string[];
  yearly?: string[];
  display: string;
}

export type Testimonial = {
  name: string;
  title: string;
  comment: string;
  url: string;
  rating: number;
};

export type UpdateOfferAccessConfig = {
  id: number;
  suspendable: boolean;
  delayPeriod: number;
  lmsIds?: LMSAccesses;
  communityIds?: CommunityAccesses;
};

/**
 * Offer accesses
 */
export type OfferAccesses = {
  communityAccesses: CommunityAccesses;
  lmsAccesses: LMSAccesses;
};

/**
 * Configure offer access form props
 */
export type ConfigureOfferAccessFormProps = {
  isModalOpen: boolean;
  handleCancel: Function;
  offerId: number;
  refreshOffer: Function;
  suspendable: boolean;
  delayPeriod: number;
  offerProducts: Product[];
  offerRecommendedProducts: Product[];
  currentAccesses: OfferAccesses;
};

export type ConfigureOfferAccessProps = {
  type: "product" | "recommended";
  courses: Course[];
  selectedCommunities: CommunityType[];
  selectedCourses: CourseInfo[];
  spaceGroups: any[];
  spaces: any[];
  selectedSpaces: any[];
  offerProducts: Product[];
  onRemoveSpace: (value: number[]) => void;
  onSelectCommunity: (value: string[]) => void;
  onSelectCourse: (value: string[]) => void;
  onSelectSpace: (value: number[]) => void;
  onUpdateCourseList: () => void;
  onUpdateCommunityList: () => void;
};

/**
 * Circle space group
 */
export type CircleSpaceGroup = {
  id: number;
  name: string;
  is_hidden_from_non_members: boolean;
  allow_members_to_create_spaces: boolean;
  community_id: number;
  space_order_array: number[];
  created_at: string;
  updated_at: string;
  automatically_add_members_to_new_spaces: boolean;
  add_members_to_space_group_on_space_join: boolean | null;
  slug: string;
  hide_non_member_spaces_from_sidebar: boolean | null;
  space_group_members_count: number;
  active_space_group_members_count: number;
  hide_members_count: boolean | null;
};
