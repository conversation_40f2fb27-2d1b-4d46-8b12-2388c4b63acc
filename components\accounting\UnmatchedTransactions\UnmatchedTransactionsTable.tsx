"use client";

import { Table, Form, Divider, Drawer, Typography } from "antd";
import { useEffect } from "react";

import UnmatchedTransactionDetails from "@/components/accounting/UnmatchedTransactions/details/UnmatchedTransactionDetails";
import paginationConfig from "@/components/form/paginationConfig";
import { getUnmatchedTransactionsTableColumns } from "./UnmatchedTransactionsTableColumns";
import UnmatchedTransactionsHeader from "./UnmatchedTransactionsHeader";
import { useUnmatchedTransactionsTable } from "@/hooks/accounting/bankTransfers";
import { UnMatchedTransactions } from "@/types";

const { Title } = Typography;

const UnmatchedTransactionsTable = () => {
  const [filterForm] = Form.useForm();
  const {
    pageState,
    transactions,
    isLoading,
    openMatchingModal,
    openSpecificTransaction,
    handleSkipTransaction,
    handleUpdateTransaction,
    handleFilterChange,
    resetFilters,
    setPageState,
  } = useUnmatchedTransactionsTable();

  const columns = getUnmatchedTransactionsTableColumns({
    openSpecificTransaction,
  });

  useEffect(() => {
    if (transactions) {
      setPageState({
        ...pageState,
        unmatchedTrx: transactions.items.filter(
          (item: UnMatchedTransactions) => {
            return item.status === "unmatched";
          }
        ),
      });
    }
  }, [transactions]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Unmatched Transactions</Title>
      <UnmatchedTransactionsHeader
        filterForm={filterForm}
        handleFilterChange={handleFilterChange}
        resetFilters={resetFilters}
        openMatchingModal={openMatchingModal}
        pageState={pageState}
      />
      <Divider />
      <Table
        dataSource={transactions?.items}
        columns={columns}
        scroll={{ x: 2000 }}
        size="small"
        pagination={paginationConfig}
        loading={isLoading}
        rowClassName={() => {
          return "paradox-table-row";
        }}
        rowKey="id"
      />
      {pageState.selectedTransaction && (
        <Drawer
          destroyOnClose
          width={"100vw"}
          closeIcon={false}
          open={pageState.isMatchingModalOpen}
          onClose={() => {
            setPageState({
              ...pageState,
              isMatchingModalOpen: false,
            });
          }}
        >
          <UnmatchedTransactionDetails
            transaction={pageState.selectedTransaction}
            id="1"
            handleClose={() => {
              setPageState({
                ...pageState,
                isMatchingModalOpen: false,
              });
            }}
            skipTransaction={handleSkipTransaction}
            updateTransactionStatus={handleUpdateTransaction}
          />
        </Drawer>
      )}
    </div>
  );
};

export default UnmatchedTransactionsTable;
