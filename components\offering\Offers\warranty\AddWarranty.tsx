"use client";

import {
  BoldOutlined,
  ItalicOutlined,
  LinkOutlined,
  UnderlineOutlined,
} from "@ant-design/icons";
import { useEditor, EditorContent, BubbleMenu } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Button, Divider, Input, Select, Typography } from "antd";
import Underline from "@tiptap/extension-underline";
import Link from "@tiptap/extension-link";
import { useEffect, useState } from "react";
import CharacterCount from "@tiptap/extension-character-count";
import * as allIcons from "@ant-design/icons";
import Icon from "@ant-design/icons/lib/components/Icon";
import { Warranty } from "@/types";

type WarrantyProps = {
  handleAdd: Function;
  checkoutPageId: number | undefined;
};

const { Text } = Typography;

const AddWarranty = ({ handleAdd, checkoutPageId }: WarrantyProps) => {
  const [currentLink, setCurrentLink] = useState("");
  const [currentIcon, setCurrentIcon] = useState<string | undefined>();
  const [icons, setIcons] = useState<(string | undefined)[]>();

  const editor = useEditor({
    editable: true,
    extensions: [
      Underline,
      Link.configure({
        openOnClick: false,
      }),
      CharacterCount.configure({
        limit: 70,
      }),
      StarterKit,
    ],
  });

  var showdown = require("showdown");
  var converter = new showdown.Converter();

  useEffect(() => {
    setIcons(
      Object.keys(allIcons)
        .filter((name: string) => {
          return name.endsWith("Outlined");
        })
        .map((name: string) => {
          if (
            // @ts-ignore
            allIcons[name] &&
            // @ts-ignore
            typeof allIcons[name] === "object" &&
            // @ts-ignore
            allIcons[name].$$typeof
          ) {
            // @ts-ignore
            return name;
          }
        })
    );
  }, []);

  useEffect(() => {
    if (editor && editor?.getAttributes("link")?.href) {
      setCurrentLink(editor.getAttributes("link").href);
    } else {
      setCurrentLink("");
    }
  }, [editor && editor.getAttributes("link").href]);

  const handleLinkChange = (val: any) => {
    setCurrentLink(val);
    if (val && val !== "") {
      editor &&
        editor.chain().extendMarkRange("link").setLink({ href: val }).run();
    } else {
      editor && editor.chain().extendMarkRange("link").unsetLink().run();
    }
  };

  const handleIconChanged = (val: any) => {
    setCurrentIcon(val);
  };

  const handleWarrantyAdd = async () => {
    let warranty: Warranty = {
      message: converter
        .makeMarkdown(editor?.getHTML())
        .replace(/(\r\n|\n|\r)/gm, "")
        .replaceAll('"', "'"),
      icon: currentIcon,
      checkoutPageId: checkoutPageId,
    };
    handleAdd(warranty);
    setCurrentIcon(undefined);
    editor?.commands.setContent("");
  };

  return (
    <>
      <div>
        {editor && (
          <BubbleMenu editor={editor} tippyOptions={{ duration: 100 }}>
            <Button
              icon={<BoldOutlined />}
              onClick={() => editor.chain().focus().toggleBold().run()}
              type={editor.isActive("bold") ? "primary" : "default"}
            />
            <Button
              icon={<ItalicOutlined />}
              onClick={() => editor.chain().focus().toggleItalic().run()}
              type={editor.isActive("italic") ? "primary" : "default"}
            />
            <Button
              icon={<UnderlineOutlined />}
              onClick={() => {
                editor.commands.focus();
                editor.commands.toggleUnderline();
              }}
              type={editor.isActive("underline") ? "primary" : "default"}
            />
            <Input
              value={currentLink}
              prefix={<LinkOutlined />}
              style={{ width: "150px" }}
              onChange={(e) => {
                handleLinkChange(e.target.value);
              }}
            />
          </BubbleMenu>
        )}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Select
            style={{ width: 80, height: 40 }}
            onChange={handleIconChanged}
            value={currentIcon}
            showSearch
            className="paradox-warranty-select"
            options={
              icons &&
              icons.map((item, index) => {
                return {
                  value: item,
                  label: (
                    <Icon
                      // @ts-ignore
                      component={allIcons[item]}
                      style={{ fontSize: 20 }}
                    />
                  ),
                };
              })
            }
          ></Select>
          <div style={{ width: "100%" }}>
            <EditorContent
              editor={editor}
              placeholder="Add new warranty"
              className="paradox-richtext-editor-input"
            />
          </div>
          <Divider type="vertical" />
          <Button
            type="primary"
            icon={<allIcons.PlusOutlined />}
            onClick={handleWarrantyAdd}
          >
            Add Warranty
          </Button>
        </div>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          You are limited to to 70 characters
        </Text>
      </div>
    </>
  );
};

export default AddWarranty;
