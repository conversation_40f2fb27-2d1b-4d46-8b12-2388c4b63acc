"use client";

import { App, Button } from "antd";
import { useState } from "react";

import { updateCustomerAccess } from "@/lib/services/customers";

function computeActionVariant(status: string) {
  return status === "granted"
    ? { action: "revoke", text: "Revoke" }
    : status === "suspended" || status === "revoked"
      ? { action: "grant", text: "Grant" }
      : { action: "retry", text: "Retry" };
}

const AccessActionButton = ({
  status,
  itemId,
  onAccessUpdateSuccess,
}: {
  status: string;
  itemId: number;
  onAccessUpdateSuccess?: () => void;
}) => {
  const { message } = App.useApp();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    setIsLoading(true);
    const { action } = computeActionVariant(status);
    try {
      const res = await updateCustomerAccess({
        id: itemId.toString(),
        action,
      });
      if (res.success) {
        message.success("Access updated successfully");
        onAccessUpdateSuccess?.();
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error("Failed to update access");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      style={{ justifySelf: "end" }}
      size="small"
      type={status === "granted" ? "primary" : "default"}
      danger={status === "granted"}
      onClick={handleClick}
      loading={isLoading}
    >
      {computeActionVariant(status).text}
    </Button>
  );
};

export default AccessActionButton;
