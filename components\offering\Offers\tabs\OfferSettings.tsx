"use client";

import { Descriptions, Divider, Image, Row } from "antd";
import { useMemo, type JSX } from "react";

import ConfigureOfferProductsForm from "../forms/ConfigureOfferProductsForm";
import ConfigureForeverOfferProductsForm from "../forms/ConfigureForeverOfferProductsForm";
import ConfigureOfferAccessForm from "../forms/ConfigureOfferAccessForm";
import OfferSettingsDisabledURL from "../details/OfferSettingsDisabledURL";
import OfferSettingsProducts from "../details/OfferSettingsProducts";
import OfferSettingsAccess from "../details/OfferSettingsAccess";
import OfferSettingsDescriptions from "../details/OfferSettingsDescriptions";
import { useOfferSettings } from "@/hooks/offering/offers-settings";
import {
  CommunityAccesses,
  LMSAccesses,
  Offer,
  PlanDTO,
  Product,
} from "@/types";

type OfferSettingsProps = {
  offer: Offer;
  products: Product[];
  refreshOffer: Function;
};

export const OfferSettings = (params: OfferSettingsProps): JSX.Element => {
  const {
    modals,
    updateRedirectUrlForm,
    setModals,
    updateRedirectIfDisabledForm,
    updateRedirectIfDisabledUrl,
  } = useOfferSettings(params.offer, params.refreshOffer);

  const attachedForeverProduct = useMemo(() => {
    if (params.offer.isForever) {
      const monthlyPlanId = params.offer.config?.monthly
        ? params.offer.config?.monthly[0]
        : undefined;
      const yearlyPlanId = params.offer.config?.yearly
        ? params.offer.config?.yearly[0]
        : undefined;
      const foreverProducts = params.products.filter(
        (x: Product) => x.isForever === true
      );
      let product = foreverProducts.find((prod: Product) => {
        return (
          prod.plans.some(
            (plan: PlanDTO) => plan.chargebeeId === monthlyPlanId
          ) ||
          prod.plans.some((plan: PlanDTO) => plan.chargebeeId === yearlyPlanId)
        );
      });
      return product;
    }
  }, [params.offer.config, params.products]);

  return (
    <div>
      <Row>
        {params.offer &&
        params.offer.status === "disabled" &&
        params.offer.redirectIfDisabled ? (
          <OfferSettingsDisabledURL
            offer={params.offer}
            editingRedirectIfDisableUrl={modals.editingRedirectIfDisableUrl}
            updateRedirectUrlForm={updateRedirectUrlForm}
            updateRedirectIfDisabledForm={updateRedirectIfDisabledForm}
            updateRedirectIfDisabledUrl={updateRedirectIfDisabledUrl}
          />
        ) : null}
      </Row>
      <Row>
        <OfferSettingsDescriptions offer={params.offer} />
      </Row>
      <Divider />
      <Row>
        <Descriptions
          size="small"
          title="Offer images"
          bordered
          column={3}
          layout="vertical"
        >
          <Descriptions.Item
            label={
              <>
                <span>Offer Image</span>
                <br />
                <small style={{ color: "darkgray" }}>
                  This image will be displayed as a product image on the
                  customer portal when customers view their purchases.
                </small>
              </>
            }
          >
            <Image src={params.offer.image} width={64} height={64} />
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <span>Card Image</span>
                <br />
                <small style={{ color: "darkgray" }}>
                  This image will be displayed as a product image on the
                  customer portal when customers view their purchases.
                </small>
              </>
            }
          >
            <Image src={params.offer.cardImage} width={64} height={64} />
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <span>Banner Image</span>
                <br />
                <small style={{ color: "darkgray" }}>
                  This image will be displayed as the banner image on the
                  customer portal when customers view the details of their
                  purchases.
                </small>
              </>
            }
          >
            <Image src={params.offer.bannerImage} width={64} height={64} />
          </Descriptions.Item>
        </Descriptions>
      </Row>
      <Divider />
      <OfferSettingsProducts
        offer={params.offer}
        products={params.products}
        attachedForeverProduct={attachedForeverProduct}
        setEditingOfferProducts={(state: boolean) =>
          setModals({ ...modals, editingOfferProducts: state })
        }
      />
      <Divider />
      <OfferSettingsAccess
        offer={params.offer}
        setEditingAccess={(state: boolean) => {
          setModals({ ...modals, editingAccess: state });
        }}
      />
      <br />
      <br />
      {/* Modals */}
      {params.offer?.isForever ? (
        params.offer.id && modals.editingOfferProducts ? (
          <ConfigureForeverOfferProductsForm
            isModalOpen={modals.editingOfferProducts}
            handleCancel={() =>
              setModals({ ...modals, editingOfferProducts: false })
            }
            offerID={params.offer.id.toString()}
            initialValues={params.offer.config}
            refreshOffer={params.refreshOffer}
          />
        ) : null
      ) : params.offer.id && modals.editingOfferProducts ? (
        <ConfigureOfferProductsForm
          isModalOpen={modals.editingOfferProducts}
          handleCancel={() =>
            setModals({ ...modals, editingOfferProducts: false })
          }
          offerID={params.offer.id.toString()}
          initialValues={params.offer.config}
          refreshOffer={params.refreshOffer}
        />
      ) : null}
      {params.offer.id && modals.editingAccess ? (
        <ConfigureOfferAccessForm
          isModalOpen={modals.editingAccess}
          handleCancel={() => setModals({ ...modals, editingAccess: false })}
          offerId={params.offer.id}
          refreshOffer={params.refreshOffer}
          offerRecommendedProducts={
            params.offer.config?.recommended &&
            (Object.keys(params.offer.config?.recommended)
              .map((key) => {
                return params.products.find(
                  (prod) => prod.id.toString() === key
                );
              })
              .filter((prod) => prod !== undefined) as Product[])
          }
          offerProducts={
            params.offer.config?.products &&
            (Object.keys(params.offer.config?.products)
              .map((key) => {
                return params.products.find(
                  (prod) => prod.id.toString() === key
                );
              })
              .filter((prod) => prod !== undefined) as Product[])
          }
          currentAccesses={{
            lmsAccesses: params.offer.lmsIds as LMSAccesses,
            communityAccesses: params.offer.communityIds as CommunityAccesses,
          }}
          suspendable={params.offer?.suspendable || false}
          delayPeriod={params.offer?.delayPeriod || 365}
        />
      ) : null}
    </div>
  );
};
