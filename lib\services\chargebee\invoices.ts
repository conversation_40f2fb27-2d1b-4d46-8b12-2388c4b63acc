import { Invoice } from "chargebee";
import { requestChargebee } from "./index";
import {
  CollectPaymentParams,
  RecordExcessPaymentInputParams,
  RecordOfflinePaymentInputParams,
  RecordOfflineRefundParams,
  RefundInvoiceParams,
  UpdateInvoiceAddressParams,
  WriteOffInvoiceParams,
} from "@/types";

/**
 * Retrieve Chargebee Invoices
 *
 * @returns A Promise that resolves to the response data from Chargebee.
 */
export async function getInvoices() {
  return requestChargebee(`/api/invoices`, "GET");
}

/**
 * Retrieve Chargebee Invoice
 *
 * @param id - The ID of the invoice to retrieve.
 * @returns A Promise that resolves to the response data from Chargebee.
 */
export async function getInvoice(id: string) {
  return requestChargebee(`/api/invoices/${id}`, "GET");
}

export async function refundInvoice(params: RefundInvoiceParams): Promise<any> {
  return requestChargebee(`/api/invoices/${params.id}/refund`, "POST", params);
}

export async function writeOffInvoice(params: WriteOffInvoiceParams) {
  return requestChargebee(
    `/api/invoices/${params.id}/write-off`,
    "POST",
    params
  );
}

/**
 * Collects payment for an invoice.
 *
 * @param params - The parameters for collecting payment.
 * @returns A promise that resolves to the result of the payment collection.
 */
export async function collectPayment(params: CollectPaymentParams) {
  return requestChargebee(
    `/api/invoices/${params.id}/collect-payment`,
    "POST",
    params
  );
}

export async function recordOfflineRefund(
  params: RecordOfflineRefundParams
): Promise<any> {
  return requestChargebee(
    `/api/invoices/${params.id}/record-offline-refund`,
    "POST",
    params
  );
}

/**
 * Get Invoice PDF URL
 *
 * @param id - The ID of the invoice to retrieve the PDF for.
 * @returns A Promise that resolves to the response data from Chargebee.
 */
export async function getInvoicePDF(
  invoiceId: string
): Promise<Invoice.PdfResponse> {
  return requestChargebee(`/api/invoices/${invoiceId}/pdf`, "POST", {
    id: invoiceId,
  });
}

export async function recordOfflinePaymentForInvoice(
  params: RecordOfflinePaymentInputParams
) {
  return requestChargebee(
    `/api/invoices/${params.invoiceId}/record-offline-payment`,
    "POST",
    params
  );
}

/**
 * Records an excess payment for a customer.
 * @param params - The input parameters for recording the excess payment.
 * @returns A Promise that resolves to the result of the API request.
 */
export async function recordExcessPaymentForCustomer(
  params: RecordExcessPaymentInputParams
) {
  return requestChargebee(
    `/api/customers/${params.customerId}/record-excess-payment`,
    "POST",
    {
      params,
    }
  );
}

/**
 * Update invoice shipping and billing address
 *
 * @param id - The ID of the invoice to update
 * @param shippingAddress - The new shipping address
 * @param billingAddress - The new billing address
 * @returns A Promise that resolves to the response data from Chargebee.
 */
export async function updateInvoiceAddress(params: UpdateInvoiceAddressParams) {
  return requestChargebee(
    `/api/invoices/${params.invoiceId}/update-address`,
    "POST",
    params
  );
}

/**
 * Get all payments for a specific invoice.
 *
 * @param id - The ID of the invoice.
 * @returns A Promise that resolves to the response data from Chargebee.
 */
export async function getInvoicePayments(id: string) {
  return requestChargebee(`/api/invoices/${id}/payments`, "POST", { id });
}
