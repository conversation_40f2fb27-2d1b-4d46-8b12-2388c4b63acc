"use client";

import { Tooltip, Typography } from "antd";

import { useUserRole } from "@/hooks/roles";
import { AccessControlEntry } from "@/types/ACL";

type SideMenuActionItemProps = {
  title: string | React.ReactNode;
  description: string;
  onClick: () => void;
  permission: AccessControlEntry["key"] | boolean;
  disabled?: boolean;
  tooltipTitle?: string;
  color?: "default" | "danger";
};

const { Text } = Typography;

export default function SideMenuActionItem({
  title,
  description,
  onClick,
  permission,
  disabled = false,
  tooltipTitle,
  color = "default",
}: SideMenuActionItemProps) {
  const { canAccess } = useUserRole();

  const handleClick = () => {
    if (
      typeof permission === "string" &&
      canAccess(permission as AccessControlEntry["key"])
    ) {
      onClick();
    } else if (typeof permission === "boolean" && permission === false) {
      onClick();
    }
  };

  const content = (
    <div style={{ marginBottom: "10px" }}>
      <a
        style={{
          color: color === "danger" ? "red" : "",
          fontSize: 14,
          textAlign: "justify",
          ...(disabled && {
            opacity: 0.8,
            color: "gray",
            cursor: "not-allowed",
          }),
        }}
        onClick={disabled ? undefined : handleClick}
      >
        {title}
      </a>
      <br />
      <Text
        style={{
          color: "gray",
          fontSize: 12,
          fontStyle: "italic",
        }}
      >
        {description}
      </Text>
    </div>
  );

  return tooltipTitle ? (
    <Tooltip title={tooltipTitle}>{content}</Tooltip>
  ) : (
    content
  );
}
