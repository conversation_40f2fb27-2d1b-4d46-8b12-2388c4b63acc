import { initChargebee, writeOffInvoice } from "@/server/chargebee";
import { WriteOffInvoiceParams } from "@/types";
import { NextApiRequest } from "next";
import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the POST request for writing off an invoice.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the returned response or an error message.
 */
export async function POST(req: Request) {
  try {
    const body = (await req.json()) as WriteOffInvoiceParams;
    if (body?.id) {
      const response = await writeOffInvoice(body);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_WRITE_OFF_INVOICE", details: error },
      { status: 400 }
    );
  }
}
