"use client";

import { EditOutlined } from "@ant-design/icons";
import { <PERSON>ton, Divider, Typography } from "antd";

import { CommunityType, SpaceInfo } from "@/types";

const { Text } = Typography;

type SelectedCommunitesAndSpacesProps = {
  selectedCommunities: CommunityType[];
  selectedSpaces: SpaceInfo[];
  onOpenModal: () => void;
};

export default function SelectedCommunitesAndSpaces({
  selectedCommunities,
  selectedSpaces,
  onOpenModal,
}: SelectedCommunitesAndSpacesProps) {
  return (
    <div className="paradox-dotted-card" style={{ height: "100%" }}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-evenly",
          flexDirection: "row",
          width: "100%",
        }}
      >
        <Text
          style={{
            paddingTop: "15px",
            fontWeight: "bold",
          }}
        >
          Communities and spaces (Circle)
        </Text>
        <Button
          onClick={onOpenModal}
          style={{ marginTop: "15px" }}
          icon={<EditOutlined />}
        >
          Edit
        </Button>
        <br />
      </div>
      <Text
        style={{
          fontWeight: "bold",
        }}
      >
        Communities
      </Text>
      {!selectedCommunities || selectedCommunities.length === 0 ? (
        <div className="`paradox-display-card`">
          <span style={{ width: "100%" }}>
            <Text>No community selected</Text>
          </span>
        </div>
      ) : (
        <div style={{
          display: "flex",
          flexDirection: "row",
          flexWrap: "wrap",
          gap: "5px",
          marginTop: "8px"
        }}>
          {selectedCommunities.map((community) => (
            <div
              key={community}
              style={{
                display: "inline-block",
                backgroundColor: "#f5f5f5",
                borderRadius: "4px",
                padding: "4px 12px",
                margin: "0",
                border: "1px solid #d9d9d9",
              }}
            >
              <Text>{community}</Text>
            </div>
          ))}
        </div>
      )}
      <Divider />
      <Text
        style={{
          fontWeight: "bold",
        }}
      >
        Spaces
      </Text>
      {!selectedSpaces || selectedSpaces.length === 0 ? (
        <div className="paradox-display-card">
          <span style={{ width: "100%" }}>
            <Text>No space selected</Text>
          </span>
        </div>
      ) : (
        <div style={{
          display: "flex",
          flexDirection: "row",
          flexWrap: "wrap",
          gap: "5px",
          marginTop: "8px"
        }}>
          {selectedSpaces.map((space) => (
            <div
              key={space.id}
              style={{
                display: "inline-block",
                backgroundColor: "#f5f5f5",
                borderRadius: "4px",
                padding: "4px 12px",
                margin: "0",
                border: "1px solid #d9d9d9",
              }}
            >
              <Text>{space.name}</Text>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
