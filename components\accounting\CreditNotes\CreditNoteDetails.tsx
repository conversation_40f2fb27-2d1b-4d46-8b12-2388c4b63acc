"use client";

import { Divider } from "antd";
import React, { useEffect, useState } from "react";

import { useFetchCreditNote } from "@/hooks/accounting/creditNotes";
import { useProtectPage } from "@/hooks/roles";
import CreditNoteHeader from "./details/CreditNoteHeader";
import CreditNoteInfo from "./details/CreditNoteInfo";
import CreditNoteRefundsAndAllocations from "./details/CreditNoteRefundsAndAllocations";
import { getCreditNotePDF_URL } from "@/lib/services/chargebee";

type CreditNoteIdProps = {
  creditNoteId: string;
};

const CreditNoteDetails = (params: CreditNoteIdProps) => {
  if (!params.creditNoteId) {
    throw new Error("Invalid CreditNote ID");
  }

  useProtectPage("credit-note-details");
  const [creditNoteInfo, setCreditNoteInfo] = useState<any>();
  // Queries
  const { data: creditNote, isLoading } = useFetchCreditNote(
    params.creditNoteId
  );

  useEffect(() => {
    if (creditNote) {
      // Initialize totals for VAT calculations
      let totalExclVatAllLineItems = 0;
      let allVat = 0;
      let totalInclVat = 0;
      let amount_available = creditNote.amountAvailable;
      const allocationCreditNote = creditNote.amountAllocated;
      const refundCreditNote = creditNote.amountRefunded;

      // Determine amount to display based on credit note type
      let amountToBeDisplay =
        creditNote.type === "refundable"
          ? refundCreditNote
          : allocationCreditNote;

      creditNote.lineItems &&
        creditNote.lineItems.forEach((record) => {
          // Calculate discount amount excluding VAT
          let discountAmountTaxExcluded = 0;
          if (
            creditNote.priceType === "tax_inclusive" &&
            record.discount_amount &&
            record.tax_rate
          ) {
            // For tax-inclusive prices, remove VAT from discount
            discountAmountTaxExcluded =
              record.discount_amount / (1 + record.tax_rate / 100);
          } else if (record.discount_amount) {
            // For tax-exclusive prices, use discount amount as is
            discountAmountTaxExcluded = record.discount_amount;
          }

          // Calculate unit amount excluding VAT
          let unitAmountTaxExcluded = 0;
          if (
            creditNote?.priceType === "tax_inclusive" &&
            record.amount &&
            record.tax_rate
          ) {
            // For tax-inclusive prices, remove VAT from unit amount
            unitAmountTaxExcluded = record.amount / (1 + record.tax_rate / 100);
          } else if (record.amount) {
            // For tax-exclusive prices, use unit amount as is
            unitAmountTaxExcluded = record.amount;
          }

          // Accumulate total VAT
          if (record.tax_amount) {
            allVat += record.tax_amount;
          }

          // Calculate net amount (excluding VAT) for this line item
          totalExclVatAllLineItems +=
            unitAmountTaxExcluded - discountAmountTaxExcluded;
        });

      // Calculate total including VAT
      totalInclVat = totalExclVatAllLineItems + allVat;

      // Update credit note information state
      let creditNoteInf = {
        totalExclVatAllLineItems,
        allVat,
        totalInclVat,
        amount_available,
        amountToBeDisplay,
      };
      setCreditNoteInfo(creditNoteInf);
    }
  }, [creditNote]);

  const downloadCreditNotePDF = async () => {
    if (creditNote?.chargebeeId) {
      const response = await getCreditNotePDF_URL(
        creditNote?.chargebeeId as string
      );
      if (response && response?.download.download_url) {
        window.open(response.download.download_url, "_blank");
      }
    }
  };

  return isLoading && creditNote && creditNoteInfo ? null : (
    <div>
      <CreditNoteHeader
        creditNote={creditNote}
        downloadCreditNotePDF={downloadCreditNotePDF}
      />
      <Divider />
      <CreditNoteInfo creditNote={creditNote} creditNoteInfo={creditNoteInfo} />
      <Divider />
      <CreditNoteRefundsAndAllocations creditNote={creditNote} />
    </div>
  );
};

export default CreditNoteDetails;
