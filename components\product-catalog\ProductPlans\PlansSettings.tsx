"use client";

import { useMemo } from "react";
import { Table, Tag, Image } from "antd";
import { useRouter } from "next/navigation";
import Link from "next/link";

import LinkChargebee from "@/components/core/LinkChargebee";
import { getAddonLink } from "@/lib/chargebee";
import { formatter } from "@/lib/currency";
import { getProductLink } from "@/lib/hubspot";
import { PlanDTO } from "@/types";

const HubspotLink = ({ crmSku }: { crmSku: string | undefined }) => (
  <Link
    target="_blank"
    href={getProductLink(crmSku)}
    onClick={(e) => e.stopPropagation()}
  >
    <Image
      src="/images/logo/hubspot.svg"
      preview={false}
      width={28}
      height={18}
      alt="Hubspot"
    />
  </Link>
);

export const PlanSettings = ({ plans }: { plans: PlanDTO[] | undefined }) => {
  const router = useRouter();

  const columns = useMemo(
    () => [
      {
        title: "Chargebee ID",
        key: "chargebeeId",
        render: (data: PlanDTO) => (
          <LinkChargebee url={getAddonLink(data.chargebeeId as string)}>
            {data.chargebeeId}
          </LinkChargebee>
        ),
        onCell: () => ({
          onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
            e.stopPropagation();
          },
        }),
      },
      {
        title: "Name",
        key: "internalName",
        render: (data: PlanDTO) => (
          <>
            <span>{data.internalName}</span>
            {data?.crmId && <HubspotLink crmSku={data.crmSku} />}
          </>
        ),
      },
      {
        title: "# Installments",
        key: "externalName",
        render: ({ prices }: PlanDTO) => (
          <span>{prices[0]?.totalBillingCycles ?? "?"}</span>
        ),
      },
      {
        title: "Amount / Billing cycle",
        key: "externalName",
        render: ({ prices }: PlanDTO) => (
          <span>
            {prices[0]?.amountPerBillingCycle
              ? formatter.format(prices[0].amountPerBillingCycle)
              : "?"}
          </span>
        ),
      },
      {
        title: "Price",
        key: "externalName",
        render: ({ prices }: PlanDTO) => (
          <span>
            {prices[0]?.amount ? formatter.format(prices[0].amount) : "?"}
          </span>
        ),
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        render: (status: string) => (
          <Tag color={status === "active" ? "green" : "orange"}>
            {status === "active" ? "Active" : "Draft"}
          </Tag>
        ),
      },
    ],
    []
  );

  return (
    <Table
      dataSource={plans}
      columns={columns}
      rowKey="id"
      rowClassName="paradox-table-row"
      onRow={(data) => ({
        onClick: () => router.push(`/dashboard/products/plan/${data.id}`),
      })}
    />
  );
};
