"use client";

import { useProtectPage } from "@/hooks/roles";
import { OfferListResponse } from "@/types";
import { Product } from "@/types/Products";
import {
  PlusOutlined,
  SearchOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import {
  Button,
  Space,
  Col,
  Form,
  Input,
  Row,
  Select,
  FormInstance,
} from "antd";

type SubscriptionsFilterToolProps = {
  filterForm: FormInstance;
  pageState: any;
  products: any;
  offers: OfferListResponse | undefined;
  onClearFilters: () => void;
  handleFilterChange: (changedValues: any, allValues: any) => void;
  setPageState: (pageState: any) => void;
};

export default function SubscriptionsFilterTool({
  filterForm,
  pageState,
  products,
  offers,
  handleFilterChange,
  setPageState,
  onClearFilters,
}: SubscriptionsFilterToolProps) {
  const { canAccess } = useProtectPage("subscriptions-list");

  return (
    <Row style={{ marginBottom: 10 }}>
      <Col xs={12} sm={12} md={16} lg={14} xl={15} xxl={17}>
        <Form form={filterForm} onValuesChange={handleFilterChange}>
          <Form.Item name="searchTerm">
            <Input
              prefix={<SearchOutlined />}
              onChange={(e) =>
                setPageState({
                  ...pageState,
                  search: e.target.value,
                })
              }
              placeholder="Search in Subscriptions"
              value={pageState.search}
            />
          </Form.Item>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <Form.Item name="product" style={{ width: "25%" }}>
              <Select
                allowClear
                placeholder="Product"
                options={
                  products &&
                  products.items &&
                  products.items.map((item: Product) => {
                    return {
                      value: item.id,
                      label: <span>{item.externalName}</span>,
                    };
                  })
                }
              />
            </Form.Item>
            <Form.Item name="offer" style={{ width: "25%" }}>
              <Select
                allowClear
                placeholder="Offer"
                options={
                  offers &&
                  offers.items &&
                  offers.items.map((item) => {
                    return {
                      value: item.id,
                      label: <span>{item.name}</span>,
                    };
                  })
                }
              />
            </Form.Item>
            <Form.Item name="orderStatus" style={{ width: "25%" }}>
              <Select
                allowClear
                placeholder="Status"
                options={[
                  { value: "active", label: <span>Active</span> },
                  { value: "paid", label: <span>Paid</span> },
                  { value: "refunded", label: <span>Refunded</span> },
                  { value: "stopped", label: <span>Stopped</span> },
                  { value: "overdue", label: <span>Overdue</span> },
                ]}
              />
            </Form.Item>
            <Button type="link" onClick={onClearFilters}>
              Clear Filters
            </Button>
          </div>
        </Form>
      </Col>
      <Col xs={11} sm={11} md={7} lg={9} xl={8} xxl={6} offset={1}>
        <Space direction="vertical">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              if (canAccess("subscription-create")) {
                setPageState({
                  ...pageState,
                  manualAddModal: true,
                });
              }
            }}
          >
            Create subscription
          </Button>
          <Button
            type="default"
            icon={<PlusOutlined />}
            onClick={() => {
              if (canAccess("subscription-export")) {
                setPageState({
                  ...pageState,
                  exportModal: true,
                });
              }
            }}
          >
            Export Subscriptions
          </Button>
          {process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT !== "production" && (
            <Button
              type="default"
              icon={<UploadOutlined />}
              onClick={() => {
                if (canAccess("subscription-debug-export")) {
                  setPageState({ ...pageState, importJsonModal: true });
                }
              }}
            >
              Import Subscription data
            </Button>
          )}
        </Space>
      </Col>
    </Row>
  );
}
