"use client";

import useSWR from "swr";

import { getStats } from "@/lib/services";

type Stats = {
  subscriptionsCount: {
    orderStatus: string;
    subscriptions_count: number;
    total_transaction_amount: number;
  }[];
};

/**
 * Retrieves statistics from the API.
 * @returns A Promise that resolves to the JSON response containing the statistics.
 */
export const useStats = () => {
  return useSWR<Stats>("stats", getStats);
};
