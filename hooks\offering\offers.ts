"use client";

import useS<PERSON>, { SWRConfiguration } from "swr";

import { getOffer, getOfferWarranties, getOffers } from "@/lib/services";
import {
  FilterOffer,
  GetOffersParams,
  Offer,
  OfferListResponse,
  WarrantyListResponse,
} from "@/types";

type UseFetchOffersParams = {
  options?: SWRConfiguration;
  filters?: FilterOffer;
};

/**
 * Prepare fetch offers params
 * @param filters - filters
 * @returns fetch offers params
 */
function prepareFetchOffers(filters?: FilterOffer) {
  let options: GetOffersParams = { name: "", status: "active" };
  if (filters) {
    if (filters.name) options.name = filters.name as string;
    if (filters.productLine) options.line = filters.productLine as string;
    if (filters.productFamily) options.family = filters.productFamily as string;
    if (filters.productPlan) options.plan = filters.productPlan as string;
    if (filters.product) options.product = filters.product as string;
    if (filters.status) options.status = filters.status as string;
  }
  return options;
}

/**
 * Get offers (supports pagination and filtering)
 * @param params - offers params
 * @returns offers
 */
export const useFetchOffers = (params: UseFetchOffersParams) => {
  const options: GetOffersParams = prepareFetchOffers(params?.filters);

  return useSWR<OfferListResponse>(
    "useFetchOffers",
    () => getOffers(options),
    params?.options
  );
};

/**
 * Get offer by chargebee id
 * @param chargebeeId - chargebee id
 * @returns offer
 */
export const useFetchOffer = (chargebeeId: string) => {
  return useSWR<Offer | null>(`offer-${chargebeeId}`, () =>
    getOffer(chargebeeId)
  );
};

/**
 * Get offer warranties by offer id
 * @param id - offer id
 * @param params - offer warranties params
 * @returns offer warranties
 */
export const useFetchOfferWarranties = (
  id: number,
  params?: UseFetchOffersParams
) => {
  return useSWR<WarrantyListResponse>(
    "useFetchOfferWarranties",
    () => getOfferWarranties(id),
    params?.options
  );
};
