"use client";

import { Typography, Segmented, Image } from "antd";

const { Text } = Typography;

type DebugAccessesTabsProps = {
  currentTab: string;
  onTabChange: (value: string) => void;
};

export default function DebugAccessesTabs({
  currentTab,
  onTabChange,
}: DebugAccessesTabsProps) {
  return (
    <>
      <Text>
        The aim is to enable debugging of client status on the various
        third-party tools.
      </Text>
      <Segmented
        options={[
          {
            label: (
              <>
                <Image
                  src="/images/logo/lw.jpeg"
                  width={16}
                  height={16}
                  preview={false}
                />
                &nbsp;
                <span>LW</span>
              </>
            ),
            value: "lw",
          },
          {
            label: (
              <>
                <Image
                  src="/images/logo/circle.png"
                  width={16}
                  height={16}
                  preview={false}
                />
                &nbsp;
                <span>Circle</span>
              </>
            ),
            value: "circle",
          },
        ]}
        onChange={onTabChange}
        value={currentTab}
      />
    </>
  );
}
