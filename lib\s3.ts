"use server";

import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";

const s3Client = new S3Client({
  region: process.env.AWS_REGION as string,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
  },
});

const createCommand = ({
  filename,
  image,
  contentType,
}: {
  filename: string;
  image: string;
  contentType: any;
}) => {
  const command = {
    Bucket: process.env.AWS_BUCKET_NAME as string,
    Key: process.env.AWS_FOLDER_NAME + "/" + filename,
    Body: image,
    ContentType: contentType,
  };
  return command;
};

/**
 * This function is used to upload a file to S3.
 * It is used to handle the upload of a file to S3.
 *
 * @param command - The command to upload the file to S3.
 * @returns The response from the upload of the file to S3.
 */
export const uploadFile = async (command: {
  filename: string;
  image: any;
  contentType: any;
}) => {
  try {
    var buffer = Buffer.from(
      command.image.replace(/^data:image\/\w+;base64,/, ""),
      "base64"
    );
    command.image = buffer;
    const cmd = new PutObjectCommand(createCommand({ ...command }));
    const response = await s3Client.send(cmd);
    return response;
  } catch (error) {
    console.error("cannot upload file", error);
  }
};
