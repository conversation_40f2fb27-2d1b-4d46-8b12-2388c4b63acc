"use client";

import { InfoCircleTwoTone } from "@ant-design/icons";
import {
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Typography,
} from "antd";
import { Discount } from "chargebee";
import { useState } from "react";

const { Text } = Typography;

type FieldType = {
  type?: string;
  amount?: number;
  apply_on?: "total" | "product";
};

type AddManualDiscountProps = {
  billingCycle: number;
  isOpen: boolean;
  handleAddDiscount: (values: any) => void;
  handleCancel: (shouldReload: boolean) => void;
};

export default function AddManualDiscountModal({
  billingCycle,
  isOpen,
  handleAddDiscount,
  handleCancel,
}: AddManualDiscountProps) {
  const [form] = Form.useForm<FieldType>();
  const [type, setType] = useState("");

  const resetForm = () => {
    form.resetFields();
  };

  const onFormSubmit = async (values: any) => {
    let response;
    let payload: Partial<Discount> = {
      apply_on: "invoice_amount",
      duration_type: "forever",
    };
    if (values.type === "fixed-amount") {
      payload.type = "fixed_amount";
      payload.amount = parseFloat(String(values.amount * 100));
    } else {
      payload.type = "percentage";
      payload.percentage = parseFloat(String(values.amount));
    }

    try {
      response = await handleAddDiscount(payload);
    } catch (error) {
      console.error(error);
    } finally {
      resetForm();
      handleCancel(true);
    }
  };

  return (
    <Modal
      open={isOpen}
      closeIcon={null}
      width="40%"
      cancelText="Dismiss"
      cancelButtonProps={{ type: "link" }}
      okText="Proceed"
      onCancel={() => {
        handleCancel(false);
      }}
      onOk={() => {
        form.submit();
      }}
    >
      <Text strong>Add manual discount</Text>
      <Card
        style={{
          padding: "12px",
          boxShadow: "5px 8px 24px 5px rgba(255, 255, 255, 0.6)",
        }}
      >
        <Row>
          <Col span={1}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <InfoCircleTwoTone style={{ fontSize: 24 }} />
            </div>
          </Col>
          <Col span={22} offset={1}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              Adding a manual discount will decrease the overall order amount
              according to your configuration.
            </div>
          </Col>
        </Row>
      </Card>
      <Form
        form={form}
        layout="vertical"
        initialValues={{ apply_on: "total" }}
        style={{ marginTop: "20px", marginBottom: "20px" }}
        onFinish={onFormSubmit}
      >
        <Row>
          <Col span={12}>
            <Form.Item<FieldType>
              name="type"
              label="Discount type"
              rules={[
                {
                  required: true,
                  message: "Please input the discount type",
                },
              ]}
            >
              <Select
                onChange={(value) => {
                  setType(value);
                }}
                placeholder="Choose"
                options={[
                  { value: "percent", label: <span>Percentage</span> },
                  { value: "fixed-amount", label: <span>Fixed amount</span> },
                ]}
              />
            </Form.Item>
            {type ? (
              <Text type="secondary">
                The specified{" "}
                {type === "percent" ? "percentage" : "fixed amount"} will be
                deducted
              </Text>
            ) : null}
          </Col>
          <Col span={12}>
            <Form.Item<FieldType>
              name="amount"
              label="Discount amount"
              rules={[
                {
                  required: true,
                  message: "Please input the discount amount",
                },
              ]}
              shouldUpdate={true}
            >
              <InputNumber
                controls={false}
                type="number"
                placeholder="Amount"
                suffix={type === "percent" ? "%" : "€"}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row style={{ marginTop: 20 }}>
          <Form.Item<FieldType> name="apply_on" label="Apply on">
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={"total"}>Each invoice</Radio>
                <Radio value={"order"} disabled>
                  Order (not available yet)
                </Radio>
                <Radio value={"product"} disabled>
                  Order&apos;s specific product (not available yet)
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
        </Row>
      </Form>
    </Modal>
  );
}
