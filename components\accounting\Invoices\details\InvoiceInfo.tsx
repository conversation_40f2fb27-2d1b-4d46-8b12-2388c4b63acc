"use client";

import { Descriptions, Tag, Typography } from "antd";
import { ColumnType } from "antd/es/table";
import { Divider } from "antd";
import { Invoice as ChargebeeInvoice } from "chargebee";

import { formatCents } from "@/lib/currency";
import { formatDate, formatDateFromUnix, getDaysSinceDate } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { InvoiceTable } from "./InvoiceTable";
import { Invoice, PX_Subscription } from "@/types";

type InvoiceIssuedCreditNote = ChargebeeInvoice.IssuedCreditNote;
type InvoiceLineItem = ChargebeeInvoice.LineItem;

type InvoiceInfoProps = {
  invoice: Invoice;
  subscription: PX_Subscription | undefined;
  invoiceInfo: any;
  columns: ColumnType<InvoiceLineItem>[];
};

const { Text } = Typography;

const generateMainStatusTag = (status?: string, date?: number) => {
  const daysSinceDate = getDaysSinceDate(date);
  switch (status) {
    case "paid":
      return (
        <Tag color="green">
          <b style={{ fontSize: 14 }}>Paid</b>&nbsp;
          {daysSinceDate ? `${daysSinceDate} day(s) ago` : ""}
        </Tag>
      );
    case "not_paid":
      return (
        <Tag color="orange">
          <b>Not Paid</b>
        </Tag>
      );
    case "payment_due":
      return (
        <Tag color="red">
          <b style={{ fontSize: 14 }}>Payment due</b>&nbsp;
          {daysSinceDate ? `${daysSinceDate} day(s) ago` : ""}
        </Tag>
      );
    default:
      return <Tag>{capitalizeWord(status)}</Tag>;
  }
};

export const InvoiceInfo = ({
  invoice,
  subscription,
  invoiceInfo,
  columns,
}: InvoiceInfoProps) => {
  const computeCreditNoteSum = (notes: InvoiceIssuedCreditNote[]): number => {
    let sum = 0;
    notes.forEach((note) => {
      sum += note?.cn_total ? note.cn_total : 0;
    });
    return sum;
  };

  return (
    <>
      <Descriptions title="" column={2} bordered size="small">
        <Descriptions.Item label="Invoice summary">
          <Text strong style={{ fontSize: 17 }}>
            {invoice.total && formatCents(invoice.total)}
          </Text>
          &nbsp;|&nbsp;
          {generateMainStatusTag(
            invoice.status,
            invoice.status === "paid" ? invoice.paidAt : invoice.dueDate
          )}
        </Descriptions.Item>
        <Descriptions.Item label="Customer name">
          {invoice?.customerName}
        </Descriptions.Item>
        <Descriptions.Item label="Business entity">
          {invoice?.businessEntityId}
        </Descriptions.Item>
        {invoice.status === "paid" && invoice.paidAt ? (
          <Descriptions.Item label="Payment date">
            {formatDate(invoice.paidAt * 1000)}
          </Descriptions.Item>
        ) : null}
        {invoice.amountAdjusted !== undefined && invoice.amountAdjusted > 0 && (
          <Descriptions.Item label="Adjusted">
            <Tag color="red">{formatCents(invoice.amountAdjusted)}</Tag>
          </Descriptions.Item>
        )}
        {invoice.issuedCreditNotes !== undefined &&
          invoice.issuedCreditNotes.length > 0 && (
            <Descriptions.Item label="Credits issued">
              <Tag color="red">
                {formatCents(computeCreditNoteSum(invoice.issuedCreditNotes))}
              </Tag>
            </Descriptions.Item>
          )}
        <Descriptions.Item label="Invoiced on">
          {invoice?.generatedAt && formatDateFromUnix(invoice?.generatedAt)}
        </Descriptions.Item>
        <Descriptions.Item label="Due date">
          {invoice?.dueDate ? formatDateFromUnix(invoice?.dueDate) : "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Amount due">
          {formatCents(invoice.amountDue as number)}&nbsp;
          {invoice?.currencyCode}
        </Descriptions.Item>
        <Descriptions.Item label="Subscription next billing date">
          {subscription && subscription.nextBillingAt
            ? formatDate(subscription.nextBillingAt * 1000)
            : "N/A"}
        </Descriptions.Item>
      </Descriptions>
      <Divider />
      <InvoiceTable
        invoice={invoice}
        invoiceInfo={invoiceInfo}
        columns={columns}
      />
    </>
  );
};
