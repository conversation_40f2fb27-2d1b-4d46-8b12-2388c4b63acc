"use client";

import { <PERSON><PERSON>, Divider, Row, Descriptions } from "antd";

import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import {
  ChangeSubscriptionPageState,
  SubscriptionHistoricalData,
} from "@/types";
import ProductInfo from "../forms/ProductInfo";

type EditSubscriptionOrderSummaryProps = {
  isImportedSubscription: boolean;
  nextBillingDate: string;
  history: SubscriptionHistoricalData;
  pageState: ChangeSubscriptionPageState;
};

export default function EditSubscriptionOrderSummary({
  history,
  isImportedSubscription,
  nextBillingDate,
  pageState,
}: EditSubscriptionOrderSummaryProps) {
  const newOderTotal = pageState.newProdConfig.reduce((acc, item) => {
    if (item.pricePerBillingCycle && item.billingCycles && item.quantity) {
      acc += item.billingCycles * item.pricePerBillingCycle * item.quantity;
    }
    return acc;
  }, 0);

  return (
    <>
      {!isImportedSubscription ? (
        <div
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.05)",
            padding: 10,
            borderRadius: 10,
          }}
        >
          <Descriptions
            title={"(Current) Order summary"}
            size="small"
            column={1}
            bordered
          >
            <Descriptions.Item label="Next billing date">
              {formatDateFromUnix(Number(nextBillingDate))}
            </Descriptions.Item>
            <Descriptions.Item label="Number of invoices">
              {history.invoices.length} / {pageState.oldBillingCycle}
            </Descriptions.Item>
            <Descriptions.Item label="Amount remaining to be invoiced">
              {formatCents(pageState.details.remaining)}
            </Descriptions.Item>
            <Descriptions.Item label="Total order">
              {formatCents(pageState.details.total)}
            </Descriptions.Item>
          </Descriptions>
          <Divider />
          {pageState.oldProdConfig.map((prod) => {
            return (
              <ProductInfo
                key={prod.id}
                image={prod.image}
                name={prod.name}
                pricePerBillingCycle={prod.pricePerBillingCycle}
                billingCycles={prod?.billingCycles || 0}
              />
            );
          })}
        </div>
      ) : null}
      <Divider />
      <div
        style={{
          backgroundColor: "rgba(0, 128, 0, 0.05)",
          padding: 10,
          borderRadius: 10,
        }}
      >
        <Descriptions
          title={"(New) Order summary"}
          size="small"
          column={1}
          bordered
        >
          <Descriptions.Item label="Next billing date">
            {formatDateFromUnix(Number(nextBillingDate))}
          </Descriptions.Item>
          <Descriptions.Item label="Number of invoices">
            {history?.invoices?.length} /&nbsp;
            {history?.invoices?.length + pageState.selectedBillingCycle}
          </Descriptions.Item>
          <Descriptions.Item label="Total order">
            {formatCents(newOderTotal + pageState.details.amountPaid)}
          </Descriptions.Item>
        </Descriptions>
        <Row>
          {isImportedSubscription ? null : (
            <>
              {newOderTotal + pageState.details.amountPaid >
              pageState.details.total ? (
                <Alert
                  style={{ width: "100%" }}
                  message="New order total is higher than the current order total"
                  type="warning"
                  showIcon
                />
              ) : null}
              {newOderTotal + pageState.details.amountPaid <
              pageState.details.total ? (
                <Alert
                  style={{ width: "100%" }}
                  message="New order total is less than the current order total"
                  type="warning"
                  showIcon
                />
              ) : null}
            </>
          )}
        </Row>
        <Divider />
        {pageState.newProdConfig.map((prod) => {
          return (
            <ProductInfo
              key={prod.id}
              image={prod.image}
              name={prod.name}
              pricePerBillingCycle={prod.pricePerBillingCycle}
              billingCycles={prod.billingCycles}
            />
          );
        })}
      </div>
    </>
  );
}
