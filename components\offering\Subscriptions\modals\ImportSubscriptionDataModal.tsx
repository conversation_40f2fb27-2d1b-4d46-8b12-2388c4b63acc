"use client";

import { importSubscriptionData } from "@/lib/services";
import { UploadOutlined } from "@ant-design/icons";
import { App, Button, Form, Modal, Upload } from "antd";
import { useEffect, useState } from "react";

type ImportSubscriptionDataModalProps = {
  isOpen: boolean;
  handleClose: (value: boolean) => void;
};

export default function ImportSubscriptionDataModal({
  isOpen,
  handleClose,
}: ImportSubscriptionDataModalProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [fileInfo, setFileInfo] = useState<any>(null);

  const onFormSubmit = async (values: { data: { file: any } }) => {
    const data = values.data;
    if (!data) return;
    if (data.file && data.file.status === "done") {
      const reader = new FileReader();
      reader.onload = () => {
        setFileInfo({ content: reader.result });
      };
      reader.readAsText(data.file.originFileObj);
    }
  };

  useEffect(() => {
    if (!fileInfo) return;
    setIsLoading(true);
    importSubscriptionData(fileInfo.content)
      .then((res) => {
        if (res.success) {
          message.success("Subscriptions imported successfully");
          handleClose(true);
        } else {
          message.error(`Failed to import subscription data: ${res.message}`);
        }
      })
      .catch((err) => {
        console.error(err);
        message.error("Failed to import subscription data");
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [fileInfo]);

  return (
    <Modal
      open={isOpen}
      closeIcon={null}
      width="40%"
      cancelText="Dismiss"
      cancelButtonProps={{ type: "link" }}
      okText="Update"
      okButtonProps={{
        type: "primary",
      }}
      onCancel={() => {
        handleClose(false);
      }}
      onOk={() => {
        form.submit();
      }}
      confirmLoading={isLoading}
    >
      <Form
        form={form}
        layout="vertical"
        style={{ marginTop: "20px", marginBottom: "20px" }}
        onFinish={onFormSubmit}
      >
        <Form.Item label="Subscription File" name="data">
          <Upload accept=".json" maxCount={1}>
            <Button icon={<UploadOutlined />}>Upload</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
}
