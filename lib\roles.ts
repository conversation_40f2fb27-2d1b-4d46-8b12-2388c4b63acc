import { Role } from "@/types";

/*
 ** This file contains all the roles that are used in the application
 */

export const roles: Role[] = [
  {
    value: "admin",
    label: "Admin",
  },
  {
    value: "dev",
    label: "Dev",
  },
  {
    value: "manager",
    label: "Manager",
  },
  {
    value: "finance",
    label: "Finance",
  },
  {
    value: "sales",
    label: "Sales",
  },
  {
    value: "cs",
    label: "Customer Support",
  },
  {
    value: "ic",
    label: "Individual Contributor",
  },
  {
    value: "",
    label: "No role",
  },
];

export const getRoleLabel = (slug: Role["value"]) => {
  const role = roles.find((role) => role.value === slug);
  return role?.label || "❌ No role";
};

export const getRoleColor = (slug: Role["value"]) => {
  let color = "gray";
  switch (slug) {
    case "admin":
      color = "red";
      break;
    case "dev":
      color = "blue";
      break;
    case "manager":
      color = "orange";
      break;
    case "sales":
      color = "yellow";
      break;
    case "cs":
      color = "purple";
      break;
    case "ic":
      color = "green";
      break;
  }
  return color;
};
