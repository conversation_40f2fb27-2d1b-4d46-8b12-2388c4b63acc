"use client";

import { Descriptions, Row, Tooltip, Typography } from "antd";
import { useEffect, useState } from "react";

import ProgressLine from "./ProgressLine";
import { computeTotal } from "@/core";
import { formatCents } from "@/lib/currency";
import {
  CreditNote,
  Invoice,
  InvoiceLineItemPX,
  PX_Subscription,
  SubscriptionHistoricalData,
} from "@/types";

const { Text } = Typography;

const colors = {
  green: "#a0d913",
  red: "#f6222d",
  grey: "#d9d9d9",
};

type SubscriptionCashAnalysisProps = {
  subscription: PX_Subscription;
  lineItems: InvoiceLineItemPX[];
  history: SubscriptionHistoricalData;
};

type Details = {
  amountPerBillingCycle: number;
  amountDue: number;
  amountPaid: number;
  refund: number;
  remainingToBePaid: number;
  total: number;
  cashBalance: number;
};

export default function SubscriptionCashAnalysis({
  lineItems,
  subscription,
  history,
}: SubscriptionCashAnalysisProps) {
  const [details, setDetails] = useState<Details>({
    amountPerBillingCycle: 0,
    amountDue: 0,
    amountPaid: 0,
    refund: 0,
    remainingToBePaid: 0,
    total: 0,
    cashBalance: 0,
  });
  const [stepsPercentages, setStepsPercentages] = useState<any[]>([]);

  const fillSteps = () => {
    const percentPaid = (details.amountPaid / details.total) * 100;
    const percentDue = (details.amountDue / details.total) * 100;
    const percentRemaining = (details.remainingToBePaid / details.total) * 100;

    setStepsPercentages([
      {
        percentage: `${Math.floor(percentPaid)}%`,
        color: colors.green,
      },
      {
        percentage: `${Math.floor(percentDue)}%`,
        color: colors.red,
      },
      {
        percentage: `${Math.floor(percentRemaining)}%`,
        color: colors.grey,
      },
    ]);
  };

  useEffect(() => {
    (async () => {
      if (!lineItems || !lineItems.length) return;
      const totalBillingCycles = subscription.totalBillingCycles || 1;

      const amountPerBillingCycle = lineItems.reduce(
        (acc: number, item: InvoiceLineItemPX) => {
          let tmp = item?.amount as number;
          if (item?.discount_amount) {
            tmp -= item?.discount_amount;
          }
          acc += tmp;
          return acc;
        },
        0
      );
      const paid = history.invoices.reduce((acc: number, item: Invoice) => {
        // @ts-ignore
        acc += item?.amountPaid;
        return acc;
      }, 0);
      const due = history.invoices.reduce((acc: number, item: Invoice) => {
        // @ts-ignore
        acc += item?.amountDue;
        return acc;
      }, 0);
      const refunded = history.creditNotes.reduce(
        (acc: number, item: CreditNote) => {
          // @ts-ignore
          acc += item.amountRefunded;
          return acc;
        },
        0
      );
      const currentPeriod = history.invoices.length;
      let remainingPeriod = totalBillingCycles - currentPeriod;
      const subscriptionItems = subscription.subscriptionItems;
      // This is always updated from chargebee and is most current
      // will prioritize this over our own calculations if present
      if (subscriptionItems && subscriptionItems.length > 0) {
        remainingPeriod =
          subscriptionItems[0].billing_cycles || remainingPeriod;
      }
      const remainingToBePaid = amountPerBillingCycle * remainingPeriod;
      const total = computeTotal(lineItems, totalBillingCycles);

      setDetails({
        amountPerBillingCycle,
        amountDue: due,
        amountPaid: paid,
        refund: refunded * -1,
        remainingToBePaid,
        total: total,
        cashBalance: remainingToBePaid + refunded,
      });
    })();
  }, [
    lineItems?.length && subscription?.id,
    history && history?.invoices?.length,
  ]);

  useEffect(() => {
    // Fill steps with colors based on invoice status
    // Green for paid, red for unpaid, grey for not yet invoiced
    if (details.total) {
      fillSteps();
    }
  }, [details]);

  if (!history) return null;

  return (
    <Descriptions
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            flexDirection: "row",
            justifyContent: "space-between",
          }}
        >
          <div>
            Cash Analysis
            <br />
            <span style={{ fontSize: 12, color: "#5B5B5D" }}>
              (calculated from invoices and credit notes)
            </span>
          </div>
          <Tooltip
            styles={{ body: { width: 275 } }}
            title={
              <>
                <Row className="centered">
                  <div
                    style={{
                      width: 12,
                      height: 12,
                      marginRight: 6,
                      backgroundColor: colors.green,
                    }}
                  ></div>
                  {formatCents(details.amountPaid)} Amount paid
                </Row>
                <Row className="centered">
                  <div
                    style={{
                      width: 12,
                      height: 12,
                      marginRight: 6,
                      backgroundColor: colors.red,
                    }}
                  ></div>
                  {formatCents(details.amountDue)} Amount due
                </Row>
                <Row className="centered">
                  <div
                    style={{
                      width: 12,
                      height: 12,
                      marginRight: 6,
                      backgroundColor: colors.grey,
                    }}
                  ></div>
                  {formatCents(details.remainingToBePaid)} remaining to be
                  invoiced
                </Row>
              </>
            }
          >
            <span>
              <ProgressLine visualParts={stepsPercentages} />
            </span>
          </Tooltip>
        </div>
      }
      size="small"
      bordered
      column={1}
    >
      <Descriptions.Item label="Amount per billing cycle">
        <Text strong>{formatCents(details.amountPerBillingCycle)}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Amount paid">
        <Text strong>{formatCents(details.amountPaid)}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Amount due">
        <Text strong>{formatCents(details.amountDue)}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Remaining to be invoiced">
        <Text strong>{formatCents(details.remainingToBePaid)}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Refund">
        <Text strong>{formatCents(details.refund)}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Cash Balance">
        <Tooltip
          title={
            <>
              {formatCents(details.total)} -{formatCents(details.amountPaid)} +
              {formatCents(details.refund)}= {formatCents(details.cashBalance)}
            </>
          }
        >
          <Text strong>{formatCents(details.cashBalance)}</Text>
        </Tooltip>
      </Descriptions.Item>
    </Descriptions>
  );
}
