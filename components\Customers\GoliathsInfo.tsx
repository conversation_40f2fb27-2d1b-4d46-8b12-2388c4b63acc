"use client";

import { Card, Descriptions, Typography } from "antd";

const { Text } = Typography;

interface GoliathsInfoProps {
  discountCode: {
    code: string;
    discount: number;
    expirationDate: Date;
    createdAt: Date;
  } | null;
}

export default function GoliathsInfo({ discountCode }: GoliathsInfoProps) {
  return (
    <Card title="Goliaths Coupon">
      {discountCode ? (
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item label="Code promo">
            <Text strong>{discountCode.code}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Réduction">
            <Text>{discountCode.discount}%</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Créé le">
            <Text>{new Date(discountCode.createdAt).toLocaleDateString()}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Expiration">
            <Text>
              {new Date(discountCode.expirationDate).toLocaleDateString()}
            </Text>
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <Text type="secondary">
          {`Aucun coupon Goliaths n'est associé à ce client.`}
        </Text>
      )}
    </Card>
  );
}
