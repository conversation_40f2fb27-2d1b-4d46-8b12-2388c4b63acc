"use client";

import {
  But<PERSON>,
  Checkbox,
  Col,
  Input,
  Popover,
  Row,
  Select,
  Space,
  Typography,
} from "antd";
import { PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons";

import { FilterOfferProps } from "./ConfigureOfferProductsForm";
import { SelectedProductItem } from "../SelectedProductItem";

const { Text } = Typography;

type Props = {
  billingCyclesChanged: (value: string[]) => void;
  filters: FilterOfferProps;
  setFilters: (value: any) => void;
  setShowAddProductModal: (value: boolean) => void;
  setShowAddRecommendedModal: (value: boolean) => void;
  totalBillingCycles: number[];
};

export const ConfigureOfferProducts = ({
  billingCyclesChanged,
  filters,
  totalBillingCycles,
  setFilters,
  setShowAddProductModal,
  setShowAddRecommendedModal,
}: Props) => {
  const handlePlanChanged = (
    productID: number,
    cycle: string,
    planId: string
  ) => {
    const tempMap = new Map(filters.selectedProductPlans.get(productID));
    tempMap.set(cycle, planId);
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      selectedProductPlans: new Map(
        filters.selectedProductPlans.set(productID, tempMap)
      ),
    }));
  };

  const handleRecommendedPlanChanged = (
    productID: number,
    cycle: string,
    planId: string
  ) => {
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      selectedRecommendedPlans: new Map(
        filters.selectedRecommendedPlans.set(cycle, planId)
      ),
    }));
  };

  const handleDefaultBillingCycleChange = (value: string) => {
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      defaultBC: value,
    }));
  };

  const handleProductQuantityChange = (values: any) => {
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      quantity: values,
    }));
  };

  const handleRecommendedQuantityChange = (value: any) => {
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      recommendedQty: !!value[0],
    }));
  };

  const handleDisplayChange = (value: any) => {
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      display: value,
      quantity: [],
    }));
  };

  return (
    <div
      style={{
        width: "100vw",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <div className="paradox-card" style={{ width: "80%", padding: "10px" }}>
        <Text strong>Configure the products available in your offer</Text>
        <br />
        <br />
        <Row style={{ width: "100%" }}>
          <Col
            span={
              filters.selectedProducts && filters.selectedProducts?.length > 1
                ? 4
                : 5
            }
          >
            <div className="paradox-dotted-card" style={{ height: "100%" }}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-start",
                  flexDirection: "column",
                  height: "135px",
                  width: "100%",
                }}
              >
                <Text style={{ paddingTop: "20px" }}>Billing cycles</Text>
                <br />
                <Select
                  mode="multiple"
                  style={{ width: "100%", minWidth: "100px" }}
                  value={filters.selectedBC}
                  onChange={billingCyclesChanged}
                  options={
                    totalBillingCycles?.length
                      ? totalBillingCycles.map((item) => {
                          return {
                            label: item,
                            value: item.toString(),
                            disabled: !filters.availableBC.includes(
                              item.toString()
                            ),
                          };
                        })
                      : []
                  }
                />
              </div>
              <div
                style={{
                  marginBottom: "10px",
                  display: "flex",
                  width: "100%",
                  justifyContent: "end",
                  alignItems: "center",
                }}
              >
                <span style={{ fontSize: 12 }}>
                  <Popover title="This billing cycle will be selected by default on the checkout page">
                    <Text style={{ fontSize: 12 }}>Default </Text>{" "}
                    <QuestionCircleOutlined />
                  </Popover>
                </span>
              </div>
              <div style={{ width: "100%" }}>
                {filters.selectedBC.map((item, index) => {
                  return (
                    <div
                      style={{
                        width: "100%",
                        marginTop: index === 0 ? "0px" : "30px",
                      }}
                      key={item}
                    >
                      <Input
                        value={item}
                        disabled
                        style={{ width: "80%" }}
                        className="paradox-disabled-input"
                      />
                      &nbsp;&nbsp;&nbsp;&nbsp;
                      <Checkbox
                        checked={filters.defaultBC === item}
                        onChange={() => {
                          handleDefaultBillingCycleChange(item);
                        }}
                      />
                      <br />
                      <br />
                    </div>
                  );
                })}
              </div>
            </div>
          </Col>
          <Col
            span={
              filters.selectedProducts && filters.selectedProducts?.length > 1
                ? 12
                : 10
            }
            offset={1}
          >
            <div className="paradox-dotted-card" style={{ height: "100%" }}>
              <div
                style={{
                  width: "100%",
                  paddingTop: "20px",
                }}
              >
                <Text>Products</Text>
                <Button
                  size="small"
                  icon={<PlusOutlined />}
                  style={{ float: "right" }}
                  onClick={() => setShowAddProductModal(true)}
                >
                  {filters.selectedProducts &&
                  filters.selectedProducts.length > 0
                    ? "Edit"
                    : "Add"}
                </Button>
                <br />
                <br />
                <Space
                  style={{
                    overflowX: "scroll",
                    display: "flex",
                    alignItems: "center",
                    justifyContent:
                      filters.selectedProducts &&
                      filters.selectedProducts?.length > 0
                        ? "start"
                        : "center",
                  }}
                >
                  {filters.selectedProducts &&
                    filters.selectedProducts?.length === 0 && (
                      <div
                        style={{
                          width: "100%",
                          backgroundColor: "#FAFAFA",
                          marginTop: "30px",
                          padding: "10px",
                          borderRadius: "5px",
                          border: "1px solid lightgray",
                        }}
                      >
                        <span style={{ width: "100%" }}>
                          <Text>No product selected</Text>
                        </span>
                      </div>
                    )}
                  {filters.selectedProducts &&
                    filters.selectedProducts.map((item, index) => {
                      return (
                        <SelectedProductItem
                          product={item}
                          selectedBillingCycles={filters.selectedBC}
                          key={item.id}
                          passedPlans={filters.selectedProductPlans.get(
                            item.id
                          )}
                          handlePlanChanged={handlePlanChanged}
                          index={index}
                        />
                      );
                    })}
                </Space>
              </div>
              <hr />
            </div>
          </Col>
          <Col
            span={
              filters.selectedProducts && filters.selectedProducts?.length > 1
                ? 6
                : 7
            }
            offset={1}
          >
            <div className="paradox-dotted-card" style={{ height: "100%" }}>
              <div
                style={{
                  paddingTop: "20px",
                  width: "100%",
                }}
              >
                <Text>
                  Recommended{" ("}
                  {filters.selectedRecommended === undefined ? "0" : "1"}
                  {"/1)"}
                </Text>
                <Button
                  size="small"
                  icon={<PlusOutlined />}
                  style={{ float: "right" }}
                  onClick={() => setShowAddRecommendedModal(true)}
                >
                  {filters.selectedRecommended !== undefined ? "Edit" : "Add"}
                </Button>
                <br />
                <br />
                <div style={{ display: "flex" }}>
                  {filters.selectedRecommended === undefined && (
                    <div
                      style={{
                        width: "100%",
                        backgroundColor: "#FAFAFA",
                        marginTop: "30px",
                        padding: "10px",
                        borderRadius: "5px",
                        border: "1px solid lightgray",
                      }}
                    >
                      <span style={{ width: "100%" }}>
                        <Text>No recommended product selected</Text>
                      </span>
                    </div>
                  )}
                  {filters.selectedRecommended && (
                    <SelectedProductItem
                      product={filters.selectedRecommended}
                      selectedBillingCycles={filters.selectedBC}
                      passedPlans={filters.selectedRecommendedPlans}
                      handlePlanChanged={handleRecommendedPlanChanged}
                      index={0}
                    />
                  )}
                </div>
              </div>
              <hr />
            </div>
          </Col>
        </Row>
      </div>
      <div
        style={{
          width: "82%",
          marginTop: "20px",
        }}
      >
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "start",
          }}
        >
          <div
            style={{ width: "45%", marginLeft: "5px" }}
            className="paradox-card"
          >
            <Text strong>
              How do you want to display this offer in your checkout page?
            </Text>{" "}
            <br />
            <Text style={{ fontSize: 13, color: "gray" }}>
              Based on your selection, it will change the behavior of the
              checkout page
            </Text>
            <br />
            <br />
            <Text>Product display</Text> <br />
            <Select
              style={{ width: "100%" }}
              value={filters.display}
              onChange={handleDisplayChange}
              options={[
                { label: "Display offer", value: "offer" },
                { label: "Display products", value: "products" },
              ]}
              optionRender={(option) => (
                <div>
                  <Text>
                    {option.value === "offer"
                      ? "Display offer"
                      : "Display products"}
                  </Text>{" "}
                  <br />
                  <Text
                    style={{
                      fontSize: 13,
                      color: "gray",
                    }}
                  >
                    {option.value === "offer"
                      ? `Instead of displaying all the products in 
                            the checkout page, only the offer will be display`
                      : `All the products attached to this offer
                             will be display in the checkout page`}
                  </Text>
                </div>
              )}
            />
            <br />
            <Text style={{ fontSize: 13, color: "gray" }}>
              Based on this configuration, we will display the offer or all the
              products.
            </Text>
          </div>
          <div
            style={{ width: "45%", marginRight: "5px" }}
            className="paradox-card"
          >
            <Text strong>
              Do we allow our customer to update the quantity for a product?
            </Text>{" "}
            <br />
            <Text style={{ fontSize: 13, color: "gray" }}>
              Based on your selection, it will change the behavior of the
              checkout page regarding quantity selection
            </Text>
            <br />
            <br />
            <Text>Products</Text> <br />
            <div
              style={{
                marginLeft: "15px",
                display: "flex",
                flexDirection: "column",
              }}
              className="paradox-checkbox-group"
            >
              {filters.selectedProducts === undefined ||
              filters.selectedProducts?.length === 0 ? (
                <Text style={{ color: "gray" }}>No product selected</Text>
              ) : (
                ""
              )}
              <Checkbox.Group
                value={filters.quantity}
                options={filters.selectedProducts?.map((item) => {
                  return {
                    label: item.externalName,
                    value: item.id,
                    disabled: filters.display === "offer" ? true : false,
                  };
                })}
                onChange={handleProductQuantityChange}
              />
            </div>
            <br />
            <Text>Recommended products</Text> <br />
            <div style={{ marginLeft: "15px" }}>
              {filters.selectedRecommended === undefined && (
                <Text style={{ color: "gray" }}>
                  No recommended product selected
                </Text>
              )}
              {filters.selectedRecommended && (
                <Checkbox.Group
                  value={filters.recommendedQty ? [true] : []}
                  options={[
                    {
                      value: true,
                      label: filters.selectedRecommended.externalName,
                    },
                  ]}
                  onChange={handleRecommendedQuantityChange}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
