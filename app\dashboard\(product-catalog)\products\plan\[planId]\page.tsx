export const dynamic = "force-dynamic";

import { Metadata } from "next";

import PlanDetails from "@/components/product-catalog/ProductPlans/PlanDetails";

export const metadata: Metadata = {
  title: "Plan details",
};

export default async function PlanDetailsPage(
  props: {
    params: Promise<{ planId: string }>;
  }
) {
  const params = await props.params;
  const { planId } = params;
  return <PlanDetails planId={planId} />;
}
