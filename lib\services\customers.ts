"use server";

import { UpdateCustomerAccessesParams } from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/customer-accesses`;

/**
 * Retrieves the accesses of a customer by their email.
 * @param email - The email of the customer.
 * @returns A Promise that resolves to the customer's accesses.
 */
export async function fetchCustomerAccesses(email: string) {
  const apiURL = `${baseUrl}/customer/email?email=${email}`;
  return osRequest(apiURL, "GET");
}

/**
 * Updates the access of a customer by their ID.
 * @param id - The ID of the customer.
 * @param action - The action to perform (grant, revoke, retry).
 * @returns A Promise that resolves to the updated customer access.
 */
export async function updateCustomerAccess({
  id,
  action,
}: UpdateCustomerAccessesParams): Promise<any> {
  const apiURL = `${baseUrl}/${id}/${action}`;
  return osRequest(apiURL, "POST");
}
