"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Col,
  Divider,
  Form,
  Image,
  Input,
  Row,
  Select,
  Typography,
} from "antd";
import { useMemo, useRef, useState } from "react";
import countryList from "react-select-country-list";

import UploadImage from "@/components/core/UploadImage";
import { getTaxProfiles } from "@/lib/taxProfiles";
import { CommunityInfoDTO, Course, ProductLine, SpaceInfo } from "@/types";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { useProductForm } from "@/hooks/product-catalog/products";

type CreateProductForm = {
  isOpen: boolean;
  productLines: any;
  onCreateProduct: Function;
  onClose: Function;
};

const { Title, Text } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

export default function CreateProductForm({
  isOpen,
  productLines,
  onCreateProduct,
  onClose,
}: CreateProductForm) {
  const codeRef = useRef(null);
  const countryDropdownOptions = useMemo(() => countryList().getData(), []);
  const { modal } = App.useApp();

  const {
    form,
    formData,
    updateCourses,
    updateCommunity,
    filterCommunity,
    filterSpaceGroups,
  } = useProductForm();
  const [selectedLine, setSelectedLine] = useState<ProductLine>();
  const [productCode, setProductCode] = useState("");
  const [image, setImage] = useState<string | undefined>();

  const handleCreate = () => {
    const values = form.getFieldsValue();
    values.withProductDelivery = true;
    if (values.courses) {
      values.lmsIds = values.courses.map((id: string) => {
        const course = formData.courses.find(
          (course: Course) => course.id === id
        );
        if (course) {
          return {
            id: course.id,
            name: course.title,
          };
        }
        return {
          id: id,
          name: id,
        };
      });
    }
    if (values.community && values.spaces) {
      let communityInfo: CommunityInfoDTO[] = [
        {
          community: values.community,
          spaces: [],
        },
      ];
      values.spaces.forEach((spaceId: string) => {
        const space = formData.spaces.find(
          (space: SpaceInfo) => space.id === Number(spaceId)
        );
        if (space) {
          communityInfo[0].spaces.push({
            id: space.id,
            slug: space.slug,
            name: space.name,
          });
        }
      });
      values.communityIds = communityInfo;
    }
    // remove the spaces and courses from the values
    delete values.spaces;
    delete values.courses;
    delete values.community;
    delete values.spaceGroup;
    let payload = { ...values };
    if (image) {
      payload.image = image;
    }
    onCreateProduct({ ...payload, code: productCode + payload.code });
  };

  const handleCancel = async () => {
    modal.warning({
      title: "Are you sure you want to leave this page?",
      content: "You will lose all unsaved changes",
      afterClose: () => {
        onClose();
      },
      okText: "Confirm",
      okButtonProps: { danger: true },
      cancelButtonProps: { ghost: true },
      cancelText: "Cancel",
    });
  };

  const onProductFamilySelected = (value: number) => {
    form.setFieldValue("productFamilyId", undefined);
    setSelectedLine(productLines?.items.find((x: any) => x.id === value));
    generateProductCode();
  };

  const generateProductCode = () => {
    const family = form.getFieldValue("productFamilyId");
    const line = form.getFieldValue("productLineId");
    if (family && line) {
      const lineObj = productLines?.items.find((item: any) => item.id === line);
      const familyObj = lineObj?.families?.find((x: any) => x.id === family);
      const lineSlug = lineObj?.name.replace(/\s+/g, "-").toUpperCase();
      const familySlug = familyObj?.name.replace(/\s+/g, "-").toUpperCase();

      setProductCode(`${lineSlug}-${familySlug}-`);
    }
  };

  return (
    <DrawerFormContainer
      isOpen={isOpen}
      onClose={handleCancel}
      title="Add a product"
      submitText="Create"
      onCancel={handleCancel}
      onSubmit={handleCreate}
    >
      <Form form={form} layout="vertical" onFinish={handleCreate}>
        <Title level={5}>Product classification</Title>
        <Form.Item
          label="Product line name"
          name="productLineId"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            placeholder="PXS"
            options={productLines?.items.map((item: any) => {
              return {
                value: item.id,
                label: item.name,
              };
            })}
            onChange={onProductFamilySelected}
            data-testid="create-product-line-select"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product line attached to this product
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product family"
          name="productFamilyId"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            onChange={generateProductCode}
            placeholder="EDEC"
            options={
              selectedLine?.families &&
              selectedLine.families.map((item) => {
                return {
                  value: item.id,
                  label: item.name,
                };
              })
            }
            data-testid="create-product-family-select"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product family attached to this product
        </Text>
        <br />
        <br />
        <Form.Item
          label="Is Forever?"
          name="isForever"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            placeholder="Yes/No"
            options={[
              {
                label: "Yes",
                value: true,
                disabled:
                  process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT ===
                  "production",
              },
              { label: "No", value: false },
            ]}
            data-testid="create-product-is-forever-select"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Is the product for a forever subscription?
        </Text>
        <br />
        <br />
        <Form.Item
          label="Event"
          name="isEvent"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            placeholder="Yes/No"
            options={[
              { label: "Yes", value: true, disabled: true },
              { label: "No", value: false },
            ]}
            data-testid="create-product-is-event-select"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Is the product an event?
        </Text>
        <br />
        <br />
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.isEvent !== currentValues.isEvent
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("isEvent") === true ? (
              <Row>
                <Col span={11}>
                  <Form.Item
                    label="Year"
                    name="eventYear"
                    style={{ marginBottom: "0px" }}
                    rules={[
                      ...rules,
                      () => ({
                        // custom validator to check if year is a number and in the future
                        validator(_, value) {
                          if (isNaN(value)) {
                            return Promise.reject(
                              new Error("Year must be a number")
                            );
                          }
                          // year has to be current year or in the future, cannot be in the past
                          if (value < new Date().getFullYear()) {
                            return Promise.reject(
                              new Error("Year must be in the future")
                            );
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <Input placeholder="2024" type="number" />
                  </Form.Item>
                  <Text style={{ fontSize: 13, color: "darkgray" }}>
                    Event year
                  </Text>
                </Col>
                <Col span={11} offset={2}>
                  <Form.Item
                    label="Location"
                    name="eventLocation"
                    style={{ marginBottom: "0px" }}
                    rules={rules}
                  >
                    <Select
                      placeholder="Country"
                      options={countryDropdownOptions}
                      allowClear
                      showSearch
                      optionFilterProp="label"
                    />
                  </Form.Item>
                  <Text style={{ fontSize: 13, color: "darkgray" }}>
                    Event location
                  </Text>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>
        <Divider />
        <Title level={5}>Product settings</Title>
        <br />
        <Form.Item
          label="Product internal name"
          name="internalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="EDEC-CERTIFICATE" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this product internally.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product external name"
          name="externalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input
            placeholder="EDEC-CERTIFICATE"
            onInput={(e) => {
              form.setFieldsValue({
                code: e.currentTarget.value
                  .replace(/[^-a-zA-Z0-9 ]/g, "")
                  .toUpperCase()
                  .replace(/\s+/g, "-"),
              });
            }}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This will be used in invoices, Checkout & Self-Serve Portal.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product description"
          name="description"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="Product description" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This will be used in invoices, Checkout & Self-Serve Portal.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product code"
          name="code"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input
            ref={codeRef}
            addonBefore={productCode}
            placeholder="EDEC-CERTIFICATE"
            onInput={(e) =>
              (e.currentTarget.value = e.currentTarget.value
                .replace(/[^-a-zA-Z0-9 ]/g, "")
                .toUpperCase()
                .replace(/\s+/g, "-"))
            }
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This is the unique product code identifier for this product
        </Text>
        <Divider />
        <Title level={5}>Fiscality</Title>
        <br />
        <Form.Item
          label="Tax Profile (Product type)"
          name="taxProfile"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            placeholder="Select a tax profile"
            options={getTaxProfiles().map((item) => {
              return {
                value: item.value,
                label: <span>{item.label}</span>,
                description: item.description,
              };
            })}
            optionRender={(option) => (
              <div>
                <Text>{option.label}</Text> <br />
                <Text
                  style={{
                    fontSize: 13,
                    color: "gray",
                    whiteSpace: "normal",
                  }}
                >
                  {option.data.description}
                </Text>
              </div>
            )}
          />
        </Form.Item>
        <Divider />
        <Title level={5}>Learnworld</Title>
        <br />
        <Form.Item
          label={
            <div
              style={{
                width: "100vw",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text>Courses</Text>
              <Button size="small" type="primary" onClick={updateCourses}>
                Update course list
              </Button>
            </div>
          }
          name="courses"
          style={{ marginBottom: "0px" }}
        >
          <Select
            placeholder="Select all the applicable courses"
            allowClear
            mode="multiple"
            options={formData.courses.map((item: Course) => {
              return {
                value: item.id,
                label: <span>{item.title}</span>,
                description: item.description,
                image: item.courseImage,
              };
            })}
            optionRender={(option) => (
              <Row>
                <Col span={2}>
                  <Image
                    preview={false}
                    src={option.data.image}
                    width={60}
                    style={{ borderRadius: "5px" }}
                  />
                </Col>
                <Col span={20} offset={2}>
                  <Text>{option.label}</Text> <br />
                  <Text
                    style={{
                      fontSize: 13,
                      color: "gray",
                      whiteSpace: "normal",
                    }}
                  >
                    {option.data.description}
                  </Text>
                </Col>
              </Row>
            )}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Please select which courses on Learnworld this product gives access
          to.
        </Text>
        <Divider />
        <Title level={5}>Circle</Title>
        <br />
        <Form.Item
          label={
            <div
              style={{
                width: "100vw",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text>Community</Text>
              <Button size="small" type="primary" onClick={updateCommunity}>
                Update community info
              </Button>
            </div>
          }
          name="community"
          style={{ marginBottom: "0px" }}
        >
          <Select
            placeholder="Select the applicable community"
            allowClear
            options={[
              { value: "PXL", label: "PXL" },
              { value: "PXS", label: "PXS" },
            ]}
            onChange={filterCommunity}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Select the applicable community
        </Text>
        <br />
        <br />
        <Row>
          <Col span={11}>
            <Form.Item
              label="Space Group"
              name="spaceGroup"
              style={{ marginBottom: "0px" }}
            >
              <Select
                placeholder="Select all the applicable space groups"
                allowClear
                mode="multiple"
                options={formData.filteredSpaceGroups.map((item: any) => {
                  return {
                    value: item.id,
                    label: <span>{item.name}</span>,
                  };
                })}
                onChange={filterSpaceGroups}
              />
            </Form.Item>
          </Col>
          <Col span={11} offset={2}>
            <Form.Item
              label="Spaces"
              name="spaces"
              style={{ marginBottom: "0px" }}
            >
              <Select
                placeholder="Select all the applicable spaces"
                allowClear
                mode="multiple"
                options={formData.filteredSpaces.map((item: any) => {
                  return {
                    value: item.id,
                    label: <span>{item.name}</span>,
                  };
                })}
              />
            </Form.Item>
          </Col>
        </Row>
        <Divider />
        <Title level={5}>Other Settings</Title>
        <UploadImage name="image" label="Product image" setImage={setImage} />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This image will be display on the checkout, user portal & invoices
        </Text>
      </Form>
    </DrawerFormContainer>
  );
}
