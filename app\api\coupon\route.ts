import { initChargebee, retrieveCoupon } from "@/server/chargebee";
import { ApplyDiscountCodeParams } from "@/types";
import { NextResponse } from "next/server";

initChargebee();

export async function POST(req: any, res: any) {
  const body = (await req.json()) as ApplyDiscountCodeParams;

  if (!body.coupon) {
    return NextResponse.json({ error: "NO_COUPON" }, { status: 400 });
  }

  try {
    const response = await retrieveCoupon(body.coupon);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json({ error: "COUPON_NOT_FOUND" }, { status: 400 });
  }
}
