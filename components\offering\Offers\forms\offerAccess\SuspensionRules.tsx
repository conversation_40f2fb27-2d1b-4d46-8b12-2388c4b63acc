"use client";

import { Col, Input, Row, Select, Typography } from "antd";

const { Text } = Typography;

type SuspensionRulesProps = {
  data: {
    suspensionRules: boolean;
    suspensionDelay: number;
  };
  onSetSuspensionRules: (value: boolean) => void;
  onSetSuspensionDelay: (value: number) => void;
};

export default function SuspensionRules({
  data,
  onSetSuspensionRules,
  onSetSuspensionDelay,
}: SuspensionRulesProps) {
  return (
    <div className="paradox-card" style={{ width: "80%", padding: "10px" }}>
      <Text strong>Suspension Rule</Text>
      <br />
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          When the suspension rule is set to yes, the suspension delay when a
          subscription is on overdue is by default at 2 days
        </Text>
      </div>
      <br />
      <br />
      <Row>
        <Col span={11}>
          <Select
            style={{ width: "100%" }}
            placeholder="Yes"
            options={[
              { value: true, label: "Yes" },
              { value: false, label: "No" },
            ]}
            onChange={(value) => {
              onSetSuspensionRules(Boolean(value));
            }}
            value={data.suspensionRules}
          />
        </Col>
        <Col span={11} offset={2}>
          <Input
            style={{ width: "300px" }}
            placeholder="Suspension delay"
            value={data.suspensionDelay}
            type="number"
            onChange={(e) => {
              onSetSuspensionDelay(Number(e.target.value));
            }}
            addonAfter="Days"
          />
        </Col>
      </Row>
    </div>
  );
}
