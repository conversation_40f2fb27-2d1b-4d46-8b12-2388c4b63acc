"use client";

import {
  BorderBottomOutlined,
  EditOutlined,
  EllipsisOutlined,
} from "@ant-design/icons";
import { Dropdown, MenuProps, Space, Table, Tag, Typography } from "antd";
import { useRouter } from "next/navigation";

const { Text } = Typography;

const PlanSettings = () => {
  const router = useRouter();

  const columns = [
    {
      title: "Plan Name",
      dataIndex: "product_name",
      key: "product_name",
    },
    {
      title: "# Installments",
      dataIndex: "product_code",
      key: "product_code",
    },
    {
      title: "Amount per Billing cycle",
      dataIndex: "product_line",
      key: "product_line",
    },
    {
      title: "Plan price",
      dataIndex: "product_family",
      key: "product_family",
    },
    {
      title: "Entities",
      dataIndex: "fiscal_entities",
      key: "fiscal_entities",
      render: (data: string) => <Tag>{data}</Tag>,
    },
    {
      title: "",
      key: "offers",
      render: (data: any) => {
        return (
          <Dropdown menu={{ items }}>
            <EllipsisOutlined style={{ fontSize: 25 }} />
          </Dropdown>
        );
      },
    },
  ];

  const handleRowClick = (data: any) => {
    router.push("/dashboard/products/plan");
  };

  const dataSource = [
    {
      key: "1",
      product_name: "EDEC Certificate x12",
      product_code: "12",
      product_line: "1000",
      product_family: "12000",
      fiscal_entities: "PG",
    },
  ];

  const items: MenuProps["items"] = [
    {
      label: (
        <Space>
          <EditOutlined />
          <Text>Edit</Text>
        </Space>
      ),
      key: "0",
    },
    {
      type: "divider",
    },
    {
      label: (
        <Space>
          <BorderBottomOutlined />
          <Text>Archive</Text>
        </Space>
      ),
      key: "1",
    },
  ];

  return (
    <div>
      <Table
        dataSource={dataSource}
        columns={columns}
        onRow={(data, index) => {
          return {
            onClick: () => {
              handleRowClick(data);
            },
          };
        }}
      />
    </div>
  );
};

export default PlanSettings;
