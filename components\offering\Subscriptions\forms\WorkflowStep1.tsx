"use client";

import {
  <PERSON>pp,
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Descriptions,
  Divider,
  Form,
  FormProps,
  Input,
  Radio,
  Row,
  Select,
  Space,
  Tag,
  Typography,
} from "antd";
import {
  CloseOutlined,
  DollarOutlined,
  PercentageOutlined,
  TagOutlined,
} from "@ant-design/icons";
import { Discount } from "chargebee";
import dayjs from "dayjs";
import { useEffect, useState } from "react";

import ProductConfig from "./ProductConfig";
import AddManualDiscountModal from "../modals/AddManualDiscountModal";
import { useFetchOffers } from "@/hooks/offering/offers";
import { formatCents } from "@/lib/currency";
import { WorkflowStep1Props } from "@/types";
import { applyCoupon } from "@/lib/services/chargebee";

type FieldType = {
  offer?: any;
  billingCycle?: number;
  products?: any;
  discounts?: any;
  startDate?: string;
};

const { Text } = Typography;

const WorkflowStep1 = ({
  billingCycle,
  billingCycles,
  coupons,
  discounts,
  setCoupons,
  setDiscounts,
  offer,
  products,
  handleOfferChange,
  handleBillingCycleChange,
  handleProductSelection,
  handleProductQtyChange,
  handleStartDateChanged,
}: WorkflowStep1Props) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  // modals
  const [modalOpen, setModalOpen] = useState(false);
  const [currentCoupon, setCurrentCoupon] = useState<string | null>(null);

  // Queries
  const { data: offers } = useFetchOffers({});

  const onValuesChange: FormProps<FieldType>["onValuesChange"] = (
    valuesChanged: any,
    values: any
  ) => {
    console.log("ℹ️ onValuesChange", valuesChanged, values);
    if (valuesChanged.billingCycle) {
      handleBillingCycleChange(valuesChanged.billingCycle);
    }
    if (valuesChanged.offer) {
      const o: any = offers?.items.find(
        (offer: any) => offer.id === valuesChanged.offer
      );
      handleOfferChange(o);
      if (o.config && o.config.defaultBillingCycle) {
        const newBc = Number(o.config.defaultBillingCycle);
        form.setFieldValue("billingCycle", newBc);
      }
    }
    if (valuesChanged.startDate) {
      handleStartDateChanged(valuesChanged.startDate.toISOString());
    }
  };

  const handleRemoveCoupon = (coupon: any) => {
    return () => {
      setCoupons(coupons.filter((c) => c !== coupon));
    };
  };

  const handleRemoveDiscount = (discount: any) => {
    return () => {
      setDiscounts(discounts.filter((d) => d !== discount));
    };
  };

  const applyCouponCode = async () => {
    if (!currentCoupon) return;
    const res = await applyCoupon({ coupon: currentCoupon });
    if (res.error) {
      return message.open({
        type: "error",
        content: "An error occurred while applying the coupon",
      });
    }
    setCoupons([...coupons, res]);
    message.open({
      type: "success",
      content: "Coupon applied successfully",
    });
  };

  const handleAddDiscount = async (discount: any) => {
    if (!discount) return;
    setDiscounts([...discounts, discount]);
  };

  useEffect(() => {
    if (offer) {
      form.setFieldsValue({
        offer: offer.id,
        billingCycle,
        products: products.map((p: any) => p.id),
      });
    }
  }, [offer?.id, billingCycle, products?.length]);

  useEffect(() => {
    setCurrentCoupon("");
  }, [coupons]);

  return (
    <>
      <Form form={form} onValuesChange={onValuesChange} autoComplete="off">
        <Descriptions title="" bordered column={1} size="small">
          <Descriptions.Item
            label={
              <>
                <Text strong>Offer selection</Text>
                <br />
                <small style={{ color: "gray" }}>
                  &nbsp;You can select the offer the customer will subscribe to
                </small>
              </>
            }
          >
            <Form.Item<FieldType>
              name="offer"
              rules={[{ required: true, message: "Please input your offer!" }]}
            >
              <Select
                placeholder="Select offer"
                style={{ width: "350px" }}
                showSearch
                optionFilterProp="label"
                options={offers?.items.map((offer) => ({
                  value: offer.id,
                  label: offer.name,
                  offer: offer,
                  disabled: offer.config?.products?.length === 0,
                }))}
                optionRender={(option) => (
                  <div style={{ display: "flex", flexDirection: "row" }}>
                    <span
                      role="img"
                      aria-label={option.data.offer.image}
                      style={{ marginRight: 10 }}
                    >
                      <img
                        src={option.data.offer.image}
                        width={32}
                        height={32}
                      />
                    </span>
                    <Space direction="vertical">
                      <Text>{option.data.label}</Text>
                      <span>
                        {option?.data?.offer?.config?.products
                          ? Object.entries(
                              option?.data?.offer?.config?.products[
                                Object.keys(
                                  option?.data?.offer?.config?.products
                                )[0]
                              ]
                            ).map((kv: any) => {
                              return Object.entries(kv[1]).map(
                                (p: any, index) => {
                                  return (
                                    <Tag key={index} color="purple">
                                      x{p[0]}
                                    </Tag>
                                  );
                                }
                              );
                            })
                          : null}
                      </span>
                    </Space>
                  </div>
                )}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <Text strong>Billing cycle</Text>
                <br />
                <small style={{ color: "gray" }}>
                  &nbsp;On which number of billing cycles do you agree with the
                </small>
              </>
            }
          >
            <Form.Item<FieldType>
              name="billingCycle"
              rules={[
                { required: true, message: "Please input your billing cycle" },
              ]}
            >
              <Radio.Group>
                <Space direction="vertical">
                  {billingCycles.length ? (
                    billingCycles.map((option) => (
                      <Radio key={option} value={option}>
                        {option} billing cycles
                      </Radio>
                    ))
                  ) : (
                    <Text type="secondary">No billing cycles available</Text>
                  )}
                </Space>
              </Radio.Group>
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <Text strong>Subscription start date</Text>
                <br />
                <small style={{ color: "gray" }}>
                  &nbsp;When do you want this subscription to start. Leave empty
                  to start the subscription immediately.
                </small>
              </>
            }
          >
            <Form.Item<FieldType> name="startDate">
              <DatePicker
                placeholder="Start date"
                style={{ width: "100%" }}
                disabledDate={(current: any) => {
                  return current < dayjs().endOf("day");
                }}
              />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
        <Divider />
        <Card
          style={{ marginBottom: 20 }}
          title={
            <>
              <Text strong>Products configuration</Text>
              <Text type="secondary">
                &nbsp;Based on the offer selection, which products are part of
                the subscription?
              </Text>
            </>
          }
        >
          <Form.Item<FieldType>
            name="products"
            rules={[{ required: true, message: "Please select your products" }]}
          >
            {products.length ? (
              products.map((p: any, index: number) => (
                <ProductConfig
                  key={index}
                  itemData={p}
                  isMandatory={p.isMandatory}
                  isChecked={p.isChecked}
                  handleProductSelection={handleProductSelection}
                  handleProductQtyChange={handleProductQtyChange}
                />
              ))
            ) : (
              <Text type="secondary">No products available</Text>
            )}
          </Form.Item>
        </Card>
        <Card
          style={{ marginBottom: 20 }}
          title={
            <>
              <Text strong>Discount</Text>
              <Text type="secondary">
                &nbsp;Do you want to add a discount to this subscription
              </Text>
            </>
          }
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Input
                disabled={!offer && !billingCycle}
                placeholder="Coupon code"
                onChange={(e) => setCurrentCoupon(e.target.value)}
              />
            </Col>
            <Col span={6}>
              <Button onClick={applyCouponCode} type="primary">
                Apply discount code
              </Button>
            </Col>
            <Col span={6}>
              <Button
                disabled={true}
                onClick={() => setModalOpen(true)}
                // disabled={!offer && !billingCycle}
                // type="dashed"
              >
                Add manual discount
              </Button>
            </Col>
          </Row>
          <Row style={{ marginTop: 20 }}>
            <Text type="secondary">Coupons:</Text>
          </Row>
          <Row style={{ marginTop: 5 }}>
            {coupons.length ? (
              coupons.map((coupon, index) => (
                <Row key={index}>
                  <Tag icon={<TagOutlined />}>
                    {coupon.name.toUpperCase()} (-
                    {coupon.discount_type === "percentage"
                      ? (coupon.discount_percentage as number) + "%"
                      : formatCents(coupon?.discount_amount as number)}
                    ) &nbsp;
                    <CloseOutlined onClick={handleRemoveCoupon(coupon)} />
                  </Tag>
                </Row>
              ))
            ) : (
              <Text type="secondary">No coupons applied</Text>
            )}
          </Row>
          <Row style={{ marginTop: 20 }}>
            <Text type="secondary">Discounts:</Text>
          </Row>
          <Row style={{ marginTop: 5 }}>
            {discounts.length ? (
              discounts.map((discount: Discount, index) => (
                <Row key={index}>
                  <Tag
                    color="blue"
                    icon={
                      discount.type === "percentage" ? (
                        <PercentageOutlined />
                      ) : (
                        <DollarOutlined />
                      )
                    }
                  >
                    Apply on:&nbsp;
                    {discount.apply_on === "invoice_amount"
                      ? "Each invoice"
                      : "For specific items"}
                    &nbsp; -&nbsp;
                    {discount.type === "percentage"
                      ? (discount.percentage as number) + "%"
                      : formatCents(discount.amount as number)}
                    &nbsp;
                    <CloseOutlined onClick={handleRemoveDiscount(discount)} />
                  </Tag>
                </Row>
              ))
            ) : (
              <Text type="secondary">No discounts applied</Text>
            )}
          </Row>
        </Card>
      </Form>
      {modalOpen && (
        <AddManualDiscountModal
          billingCycle={billingCycle}
          isOpen={modalOpen}
          handleAddDiscount={handleAddDiscount}
          handleCancel={() => setModalOpen(false)}
        />
      )}
    </>
  );
};

export default WorkflowStep1;
