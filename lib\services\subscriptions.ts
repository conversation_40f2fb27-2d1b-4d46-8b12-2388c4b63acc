"use server";

import {
  DownsellSubscriptionParams,
  GetSubscriptionsParadoxApi,
  PX_Subscription,
  SubscriptionListResponse,
  UpdateBillingCyclesParams,
} from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/subscriptions`;

/**
 * Retrieves the customer information associated with a given deal ID.
 * @param dealId - The ID of the deal.
 * @returns A Promise that resolves to the customer information.
 */
export async function getCustomerByDealID(dealId: string) {
  if (!dealId) {
    return undefined;
  }
  const res = await osRequest(`${baseUrl}/dealCustomer/${dealId}`, "GET");
  return res;
}

/**
 * Gets all the subscriptions in Paradox api
 * @param searchQuery Search query
 * @param status Status of the subscription
 * @param productId Product ID of the subscription
 * @param OfferId Offer ID of the subscription
 * @returns List of subscriptions
 */
export const getSubscriptionsParadoxApi = async (
  params?: GetSubscriptionsParadoxApi
): Promise<SubscriptionListResponse> => {
  let requestQuery = new URL(`${baseUrl}?`);
  const defaultParams = {
    page: "1",
    limit: "1000",
    orderBy: "DESC",
  };
  const queryParams = {
    ...defaultParams,
    ...(params?.searchQuery && { query: params.searchQuery }),
    ...(params?.status && { orderStatus: params.status }),
    ...(params?.productId && { product: params.productId }),
    ...(params?.offerId && { offer: params.offerId }),
    ...(params?.isForever && { isForever: params.isForever }),
  };
  requestQuery.search = new URLSearchParams(queryParams).toString();

  const res = await osRequest(requestQuery.href, "GET");

  return res;
};

/**
 * Retrieves subscription data from Paradox API for a specific subscription ID.
 * Makes three parallel API calls to fetch:
 * 1. Basic subscription details
 * 2. Historical subscription data
 * 3. Line items associated with the subscription
 *
 * For line items, it filters based on the subscription type:
 * - PX_MIG subscriptions: keeps only "plan_item_price" items
 * - Other subscriptions: keeps only "addon_item_price" items
 *
 * @param subscriptionId - The ID of the subscription to retrieve
 * @returns Promise containing the subscription details, history, and filtered line items
 */
export const getSubscriptionDataFromPxApi = async (
  subscriptionId: string
): Promise<{
  subscription: PX_Subscription | undefined;
  history: any;
  lineItems: any;
  events: any;
}> => {
  try {
    const [sub, history, lineItems, events] = await Promise.all([
      osRequest(`${baseUrl}/${subscriptionId}`, "GET"),
      osRequest(`${baseUrl}/historical-data/${subscriptionId}`, "GET"),
      osRequest(`${baseUrl}/line-items/${subscriptionId}`, "GET"),
      osRequest(`${baseUrl}/events/${subscriptionId}`, "GET"),
    ]);

    // Return early if no line items exist
    if (!lineItems.lineItems)
      return {
        subscription: sub,
        history: history,
        lineItems,
        events,
      };

    // Determine entity type based on subscription ID format
    const entityType = subscriptionId.includes("PX_MIG")
      ? "plan_item_price"
      : "addon_item_price";

    // Filter line items to keep only relevant entity types
    lineItems.lineItems = lineItems.lineItems.filter(
      (x: any) => x.entity_type === entityType
    );

    return {
      subscription: sub,
      history: history,
      lineItems: lineItems,
      events: events,
    };
  } catch (error) {
    console.error(error);
    return {
      subscription: undefined,
      history: undefined,
      lineItems: undefined,
      events: undefined,
    };
  }
};

/**
 * Retrieves subscription details from Paradox API for a specific subscription ID
 * @param subscriptionId - The ID of the subscription to retrieve
 * @returns Promise containing the subscription details
 */
export const getSubscriptionParadoxApi = async (
  subscriptionId: string
): Promise<PX_Subscription> => {
  const res = await osRequest(`${baseUrl}/${subscriptionId}`, "GET");
  return res;
};

/**
 * Marks a subscription as refunded and cancels it
 * @param subscriptionId - The ID of the subscription to refund and cancel
 * @returns Promise containing the operation result
 */
export const markAsRefundedAndCancel = async (subscriptionId: string) => {
  const res = await osRequest(`${baseUrl}/${subscriptionId}`, "PATCH");
  return res;
};

/**
 * Retrieves products attached to a specific subscription
 * @param subscriptionId - The ID of the subscription
 * @returns Promise containing the attached products
 */
export const getSubscriptionProducts = async (subscriptionId: string) => {
  const res = await osRequest(
    `${baseUrl}/getAttachedProducts/${subscriptionId}`,
    "GET"
  );
  return res;
};

/**
 * Retrieves offer details for a specific subscription
 * @param subscriptionId - The ID of the subscription
 * @returns Promise containing the offer details
 */
export const getSubscriptionOffer = async (subscriptionId: string) => {
  try {
    const res = await osRequest(
      `${baseUrl}/getOfferDetails/${subscriptionId}`,
      "GET"
    );
    return res;
  } catch (error) {
    console.error(error);
    return undefined;
  }
};

/**
 * Updates the billing cycles for a subscription
 * @param params - Parameters containing subscription ID and billing cycle updates
 * @returns Promise containing the update operation result
 */
export const updateSubscriptionBillingCycles = async (
  params: UpdateBillingCyclesParams
) => {
  const res = await osRequest(
    `${baseUrl}/${params.subscriptionId}`,
    "POST",
    params
  );
  return res;
};

/**
 * Performs a downsell operation on a subscription
 * @param params - Parameters for the downsell operation
 * @returns Promise containing the downsell operation result
 */
export const downsellSubscription = async (
  params: DownsellSubscriptionParams
) => {
  const res = await osRequest(
    `${baseUrl}/downsell/${params.subscription_id}`,
    "POST",
    params
  );
  return res;
};

/**
 * Exports subscription data
 * @param params - Export parameters
 * @returns Promise containing the exported data as a base64 string
 */
export const exportSubscriptions = async (params: any) => {
  const res: Response = await osRequest(
    `${baseUrl}/export`,
    "POST",
    params,
    undefined,
    false
  );
  // Convert the returned file to a base64 string
  const blob = await res.blob();
  let buffer = Buffer.from(await blob.arrayBuffer());
  const data = "data:" + blob.type + ";base64," + buffer.toString("base64");
  return data;
};

/**
 * Resyncs a subscription
 * @param subscriptionId - The ID of the subscription to resync
 * @returns Promise containing the resynced subscription
 */
export const resyncSubscription = async (
  subscriptionId: string
): Promise<PX_Subscription> => {
  const res = await osRequest(`${baseUrl}/resync/${subscriptionId}`, "POST");
  return res;
};

/**
 * Exports subscription data
 * @param subscriptionId - The ID of the subscription to export
 * @returns Promise containing the exported data as a base64 string
 */
export const exportSubscriptionData = async (subscriptionId: string) => {
  let res = {};
  const [sub, history, lineItems] = await Promise.all([
    osRequest(`${baseUrl}/${subscriptionId}`, "GET"),
    osRequest(`${baseUrl}/historical-data/${subscriptionId}`, "GET"),
    osRequest(`${baseUrl}/line-items/${subscriptionId}`, "GET"),
  ]);
  // Return early if no line items exist
  if (!lineItems.lineItems) {
    res = {
      subscription: sub,
      history: history,
      lineItems,
    };
  } else {
    res = {
      subscription: sub,
      history: history,
      lineItems: lineItems.lineItems,
    };
  }
  // Conver the json to a blob
  const str = JSON.stringify(res);
  const bytes = new TextEncoder().encode(str);
  const blob = new Blob([bytes], {
    type: "application/json;charset=utf-8",
  });
  // Convert the returned file to a base64 string
  let buffer = Buffer.from(await blob.arrayBuffer());
  const data = "data:" + blob.type + ";base64," + buffer.toString("base64");
  return data;
};

/**
 * Imports subscription data
 * @param data - The data to import
 * @returns Promise containing the import operation result
 */
export const importSubscriptionData = async (data: string) => {
  const res = await osRequest(`${baseUrl}/import-json`, "POST", {
    jsonData: data,
  });
  return res;
};
