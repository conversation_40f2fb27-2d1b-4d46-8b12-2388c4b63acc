"use client";

import { Descriptions, Typography } from "antd";

import { PX_Subscription } from "@/types";

const { Text } = Typography;

type SubscriptionCustomFieldsProps = {
  subscription: PX_Subscription;
};

export default function SubscriptionCustomFields({
  subscription,
}: SubscriptionCustomFieldsProps) {
  return (
    <Descriptions
      title="Custom fields"
      style={{ width: "100%" }}
      bordered
      column={1}
      size="small"
    >
      {subscription.customFields ? (
        Object.keys(subscription.customFields).map((key) => (
          <Descriptions.Item key={key} label={key}>
            <Text
              copyable={{
                text: subscription.customFields[key],
              }}
            >
              {subscription.customFields[key]}
            </Text>
          </Descriptions.Item>
        ))
      ) : (
        <Text>No custom fields</Text>
      )}
    </Descriptions>
  );
}
