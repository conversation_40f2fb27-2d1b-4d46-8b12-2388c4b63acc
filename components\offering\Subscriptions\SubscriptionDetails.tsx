"use client";

import { <PERSON>, Divider, Row, Skeleton } from "antd";
import { Card, Customer, Subscription } from "chargebee";

import SubscriptionSummary from "./details/SubscriptionSummary";
import SubscriptionCashAnalysis from "./details/SubscriptionCashAnalysis";
import SubscriptionPaymentMethod from "./details/SubscriptionPaymentMethod";
import SubscriptionHistory from "./details/SubscriptionHistory";
import SubscriptionCustomer from "./details/SubscriptionCustomer";
import SubscriptionMetadata from "./details/SubscriptionMetadata";
import CancelSubscriptionModal from "./modals/CancelSubscription";
import EditNextBillingDateModal from "./modals/EditNextBillingDate";
import SubscriptionComments from "./details/SubscriptionComments";
import ChangePaymentMethodModal from "./modals/ChangePaymentMethod";
import SubscriptionCustomFields from "./details/SubscriptionCustomFields";
import EditBillingCycles from "./forms/EditBillingCycles";
import Downsell from "./forms/Downsell";
import SubscriptionSideMenu from "./details/SubscriptionSideMenu";
import SubscriptionDetailsHeader from "./details/SubscriptionDetailsHeader";
import { useSubscriptionDetails } from "@/hooks/offering/subscriptions";
import { SubscriptionHistoricalData } from "@/types";
import SubscriptionEvents from "./details/SubscriptionEvents";

const cardStyle: React.CSSProperties = {
  width: "100%",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  justifyContent: "space-between",
};

type SubscriptionDetailsProps = {
  id: string;
};

export default function SubscriptionDetails({ id }: SubscriptionDetailsProps) {
  const {
    subscriptionCBInfo,
    subscriptionPX,
    historicalDataPX,
    subscriptionLineItems,
    subscriptionEvents,
    modals,
    handleChangePaymentMethod,
    handleEditNextBillingCycle,
    handleCancelSubscription,
    handleEditSubscription,
    handleDownsellSubscription,
    handleRefreshSubscription,
    handleExportSubscriptionData,
    setModals,
  } = useSubscriptionDetails(id);

  if (!subscriptionPX) return null;

  return (
    <div style={{ overflow: "auto" }}>
      <SubscriptionDetailsHeader
        subscriptionCBInfo={subscriptionCBInfo}
        subscriptionPX={subscriptionPX}
      />
      <Divider />
      <div>
        <Row>
          <Col span={19} style={{ paddingRight: "0px" }}>
            <div style={cardStyle}>
              {historicalDataPX && subscriptionLineItems?.lineItems ? (
                <SubscriptionSummary
                  lineItems={subscriptionLineItems.lineItems}
                  subscription={subscriptionPX}
                  history={historicalDataPX as SubscriptionHistoricalData}
                  billingCycle={subscriptionLineItems.billingCycles}
                  billingPeriod={
                    subscriptionCBInfo?.subscription?.billing_period_unit
                  }
                  isForever={subscriptionPX.isForever || false}
                />
              ) : (
                <Skeleton />
              )}
              <Row style={{ width: "100%", marginTop: 20 }} gutter={[8, 8]}>
                <Col span={12}>
                  {subscriptionLineItems && historicalDataPX ? (
                    <SubscriptionCashAnalysis
                      lineItems={subscriptionLineItems.lineItems}
                      subscription={subscriptionPX}
                      history={historicalDataPX as SubscriptionHistoricalData}
                    />
                  ) : (
                    <Skeleton />
                  )}
                </Col>
                <Col span={12}>
                  <SubscriptionPaymentMethod
                    card={subscriptionCBInfo?.card as Card}
                    customer={subscriptionCBInfo?.customer as Customer}
                    autoCollection={
                      subscriptionCBInfo?.subscription
                        ?.auto_collection as string
                    }
                    handleChange={handleChangePaymentMethod}
                  />
                </Col>
              </Row>
              <Row style={{ marginTop: 20, width: "100%" }}>
                <SubscriptionHistory
                  data={historicalDataPX as SubscriptionHistoricalData}
                />
              </Row>
              <Row style={{ width: "100%", margin: "20px 0px" }}>
                <Col span={12}>
                  <SubscriptionCustomer
                    customer={subscriptionCBInfo?.customer as Customer}
                    subscription={subscriptionCBInfo?.subscription}
                  />
                </Col>
                <Col span={12} style={{ paddingLeft: 20 }}>
                  {subscriptionPX && (
                    <>
                      <Row>
                        <SubscriptionMetadata subscription={subscriptionPX} />
                      </Row>
                      <Row style={{ marginTop: 20 }}>
                        <SubscriptionCustomFields
                          subscription={subscriptionPX}
                        />
                      </Row>
                    </>
                  )}
                  <Row style={{ marginTop: 20 }}>
                    {subscriptionCBInfo?.subscription ? (
                      <SubscriptionComments
                        subscription={
                          subscriptionCBInfo?.subscription as Subscription
                        }
                      />
                    ) : (
                      <Skeleton />
                    )}
                  </Row>
                </Col>
              </Row>
              {subscriptionEvents ? (
                <Row style={{ marginTop: 20 }}>
                  <SubscriptionEvents subscriptionEvents={subscriptionEvents} />
                </Row>
              ) : null}
            </div>
          </Col>
          <Col span={4} offset={1}>
            <SubscriptionSideMenu
              subscriptionCBInfo={subscriptionCBInfo}
              subscriptionPX={subscriptionPX}
              historicalDataPX={historicalDataPX}
              onRefreshSubscription={() => handleRefreshSubscription()}
              onChangePaymentMethod={handleChangePaymentMethod}
              onEditNextBillingCycle={handleEditNextBillingCycle}
              onEditSubscription={handleEditSubscription}
              onDownsellSubscription={handleDownsellSubscription}
              onCancelSubscription={handleCancelSubscription}
              onExportSubscriptionData={handleExportSubscriptionData}
            />
          </Col>
        </Row>
        {modals.cancelSubIsOpen && (
          <CancelSubscriptionModal
            subscriptionId={id}
            isOpen={modals.cancelSubIsOpen}
            handleCancel={(shouldReload) => {
              if (shouldReload) {
                window.location.reload();
              } else {
                setModals({ ...modals, cancelSubIsOpen: false });
              }
            }}
          />
        )}
        {modals.editNextBillingDateIsOpen && (
          <EditNextBillingDateModal
            isOpen={modals.editNextBillingDateIsOpen}
            nextBillingAt={
              subscriptionCBInfo?.subscription?.next_billing_at as number
            }
            subscriptionId={id}
            handleEdit={(shouldReload) => {
              if (shouldReload) {
                window.location.reload();
              } else {
                setModals({ ...modals, editNextBillingDateIsOpen: false });
              }
            }}
          />
        )}
        {modals.changePaymentMethodIsOpen && (
          <ChangePaymentMethodModal
            isOpen={modals.changePaymentMethodIsOpen}
            defaultValue={
              subscriptionCBInfo?.subscription?.auto_collection as string
            }
            subscriptionId={subscriptionCBInfo?.subscription?.id as string}
            handleClose={() => {
              setModals({ ...modals, changePaymentMethodIsOpen: false });
              window.location.reload();
            }}
            customerAutoCollect={subscriptionCBInfo?.customer?.auto_collection}
          />
        )}
        {modals.editSubscriptionIsOpen && (
          <EditBillingCycles
            isModalOpen={modals.editSubscriptionIsOpen}
            closeModal={() => {
              setModals({ ...modals, editSubscriptionIsOpen: false });
            }}
            subscriptionId={subscriptionPX?.chargebeeId}
            history={historicalDataPX as SubscriptionHistoricalData}
            nextBillingDate={subscriptionCBInfo?.subscription?.next_billing_at}
            onUpdate={() => {
              window.location.reload();
            }}
          />
        )}
        {modals.downsellIsOpen && (
          <Downsell
            isModalOpen={modals.downsellIsOpen}
            closeModal={() => {
              setModals({ ...modals, downsellIsOpen: false });
            }}
            subscriptionId={subscriptionPX?.chargebeeId}
            history={historicalDataPX as SubscriptionHistoricalData}
            nextBillingDate={subscriptionCBInfo?.subscription?.next_billing_at}
          />
        )}
      </div>
    </div>
  );
}
