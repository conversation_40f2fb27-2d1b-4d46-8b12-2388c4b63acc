import { Form, Input, Modal, Typography } from "antd";

import { archiveOffer } from "@/lib/services";

type DisableOfferFormProps = {
  isOpen: boolean;
  handleCancel: Function;
  offerId: number;
  refetchOffer: Function;
};

const { Text } = Typography;

const DisableOfferForm = ({
  isOpen,
  handleCancel,
  offerId,
  refetchOffer,
}: DisableOfferFormProps) => {
  const [form] = Form.useForm();

  const disableOffer = async (values: any) => {
    await archiveOffer(offerId, values.redirectIfDisabled);
    form.resetFields();
    refetchOffer();
    handleCancel();
  };

  return (
    <Modal
      title="Are you sure you want to disable the offer?"
      open={isOpen}
      okText="Disable"
      okType="danger"
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        handleCancel();
      }}
      cancelText="Cancel"
    >
      <p>
        By disabling the offer, our customer won&lsquo;t be able to purchase
        this offer anymore and the checkout page will be desactivated.
      </p>
      <Form form={form} layout="vertical" onFinish={disableOffer}>
        <Form.Item
          name="redirectIfDisabled"
          label="Redirect URL"
          style={{ marginBottom: "0px" }}
          rules={[
            {
              required: true,
              message: "A redirect url is required to disable an offer",
            },
            {
              type: "url",
              message: "Please enter a valid URL",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Our customers will be redirected to this page if this checkout page is
          &lsquo;Off&lsquo;.
        </Text>
        <br />
        <br />
      </Form>
    </Modal>
  );
};

export default DisableOfferForm;
