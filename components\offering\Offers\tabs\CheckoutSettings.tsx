"use client";

import { EditOutlined } from "@ant-design/icons";
import Icon from "@ant-design/icons/lib/components/Icon";
import * as allIcons from "@ant-design/icons";
import {
  App,
  Button,
  Col,
  ColorPicker,
  Descriptions,
  Divider,
  Image,
  Rate,
  Row,
  Space,
  Typography,
} from "antd";
import { useState, type JSX } from "react";
import Markdown from "react-markdown";
import rehypeRaw from "rehype-raw";

import AddWarrantyForm from "../warranty/AddOfferWarranty";
import { useUserRole } from "@/hooks/roles";
import { useFetchOfferWarranties } from "@/hooks/offering/offers";
import { Offer, Warranty } from "@/types";
import { createWarranty } from "@/lib/services";

const { Text } = Typography;

const WarrantyList = ({ items }: { items: Warranty[] | undefined }) => {
  if (!items) return null;
  return items.map((item: any, index: any) => {
    return (
      <span key={index}>
        <Space>
          <Icon
            style={{ fontSize: 20 }}
            // @ts-ignore
            component={allIcons[item.icon]}
          />
          <Markdown
            rehypePlugins={[rehypeRaw]}
            remarkRehypeOptions={{ allowDangerousHtml: false }}
            components={{
              a: (props) => {
                return (
                  <a href={props.href} className="underline" target="_blank">
                    {props.children}
                  </a>
                );
              },
            }}
          >
            {item.message}
          </Markdown>
        </Space>
        <br />
      </span>
    );
  });
};

export const CheckoutSettings = (params: {
  offer: Offer;
  updateCheckout: Function;
}): JSX.Element => {
  const { canAccess } = useUserRole();
  const { message } = App.useApp();
  const [isAddWarrantyModalOpen, setAddWarrantyModalOpen] = useState(false);

  const { data: warranties, mutate: refetchOfferWarranties } =
    useFetchOfferWarranties(params.offer.checkoutPage?.id as number);

  const hideAddWarrantyModal = () => {
    params.updateCheckout();
    setAddWarrantyModalOpen(false);
  };

  const handleWarrantyAdded = async (values: any) => {
    const res = await createWarranty(values);
    if (res.id) {
      message.open({
        type: "success",
        content: "Warranty Added!",
      });
      await refetchOfferWarranties();
    }
  };

  return (
    <div>
      <Descriptions title="Checkout settings" bordered column={1} size="small">
        <Descriptions.Item
          label={
            <>
              <span>Template</span>
              <br />
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                What template do you want to use for this offer?
              </Text>
            </>
          }
        >
          <Text>{params.offer.checkoutPage?.template}</Text>
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Banner image</span>
              <br />
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                Based on the offer configuration, this image will be display in
                the Checkout page.
              </Text>
            </>
          }
        >
          <Image src={params.offer.checkoutPage?.bannerImage} width="100px" />
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Main color</span>
              <br />
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                Will affect colors of buttons / clickable elements
              </Text>
            </>
          }
        >
          <ColorPicker
            showText
            defaultValue={params.offer.checkoutPage?.mainColor}
            disabled
            size="large"
          />
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Cancellation policy</span>
              <br />
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                The cancellation policy for this offer.
              </Text>
            </>
          }
        >
          <Text>{params.offer.checkoutPage?.cancellationPolicy}</Text>
        </Descriptions.Item>
      </Descriptions>
      <Divider />
      <Row>
        <Col span={12}>
          <Descriptions
            size="small"
            title={
              <div
                style={{ width: "100%", display: "flex", alignItems: "center" }}
              >
                Warranty ({params.offer.checkoutPage?.warrantySections?.length})
                <br />
                <Button
                  icon={<EditOutlined />}
                  onClick={() => {
                    if (canAccess("offer-update")) {
                      setAddWarrantyModalOpen(true);
                    }
                  }}
                >
                  Edit
                </Button>
              </div>
            }
            bordered
            column={1}
          >
            <Descriptions.Item label="Warranty section">
              <WarrantyList
                items={params.offer.checkoutPage?.warrantySections}
              />
            </Descriptions.Item>
          </Descriptions>
        </Col>
        {params.offer.checkoutPage?.testimony &&
        Object.keys(params.offer.checkoutPage?.testimony).length > 0 &&
        params.offer.checkoutPage?.template === "B" ? (
          <Col span={12} style={{ paddingLeft: 10 }}>
            <>
              <Descriptions title="Testimony" bordered column={2} size="small">
                <Descriptions.Item label="Testimony URL">
                  <Text copyable>
                    {params.offer.checkoutPage?.testimony?.url}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="Testimony User name">
                  <Text>{params.offer.checkoutPage?.testimony?.name}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Testimony User job">
                  <Text>{params.offer.checkoutPage?.testimony?.title}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Testimony User quote">
                  <Text>{params.offer.checkoutPage?.testimony?.comment}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Testimony User rating">
                  <Rate
                    disabled
                    value={params.offer.checkoutPage?.testimony?.rating}
                  />
                </Descriptions.Item>
              </Descriptions>
            </>
          </Col>
        ) : null}
      </Row>
      <br />
      <br />
      {params.offer.checkoutPage?.warrantySections && (
        <AddWarrantyForm
          isModalOpen={isAddWarrantyModalOpen}
          handleCancel={hideAddWarrantyModal}
          checkoutPageId={params.offer.checkoutPage?.id}
          handleAdd={handleWarrantyAdded}
          updateWarranties={refetchOfferWarranties}
          warranties={warranties?.items}
        />
      )}
    </div>
  );
};
