"use server";

import { osRequest } from "./utils";
import { CronLogsFilter, CronLogsListResponse } from "@/types";

const baseUrl = `${process.env.API_URL}/cron-logs`;

/**
 * Retrieves cron logs from the Paradox API.
 * @param params - The parameters for the cron logs.
 * @returns A Promise that resolves to the cron logs.
 */
export const getCronLogs = async (
  params: CronLogsFilter
): Promise<CronLogsListResponse> => {
  let requestQuery = new URL(`${baseUrl}?`);
  const query = new URLSearchParams();
  query.append("type", params.type);
  query.append("limit", String(params.limit) || "10");
  query.append("page", String(params.page) || "1");
  if (params.status) {
    query.append("status", params.status);
  }
  query.append("orderBy", "DESC");
  requestQuery.search = query.toString();
  const res = await osRequest(requestQuery.toString(), "GET");

  return res;
};

/**
 * Reprocesses a cron log.
 * @param params - The parameters for the cron log.
 * @returns A Promise that resolves to the cron log.
 */
export const reprocessCronLog = async (params: any) => {
  const paramsWithoutId = { ...params };
  delete paramsWithoutId.id;
  return osRequest(
    `${baseUrl}/${params.id}/reprocess`,
    "POST",
    paramsWithoutId
  );
};
