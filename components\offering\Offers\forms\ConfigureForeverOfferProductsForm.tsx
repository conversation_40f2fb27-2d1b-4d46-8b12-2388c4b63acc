"use client";

import {
  ArrowLeftOutlined,
  CloseOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { App, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Row, Space, Typography } from "antd";
import React, { useEffect, useState } from "react";

import ConfigureForeverOfferProducts from "./ConfigureForeverOfferProducts";
import AddForeverProductModal from "../modals/AddForeverProductModal";
import { EmptyPlanItem } from "../type";
import { useFetchProducts } from "@/hooks/product-catalog/products";
import { ForeverOfferConfig, Product, PlanDTO } from "@/types";
import { createOfferProducts } from "@/lib/services/offers";

const { Text } = Typography;

type AddProps = {
  isModalOpen: boolean;
  handleCancel: Function;
  offerID: string;
  initialValues?: ForeverOfferConfig;
  refreshOffer: Function;
};

export type FilterForeverOfferProps = {
  display: string;
  selectedMonthlyPlan?: PlanDTO | undefined;
  selectedYearlyPlan?: PlanDTO | undefined;
  selectedProduct: Product | undefined;
};

const initialFilters: FilterForeverOfferProps = {
  display: "offer",
  selectedMonthlyPlan: {} as PlanDTO,
  selectedYearlyPlan: {} as PlanDTO,
  selectedProduct: undefined,
};

type SubmitPayload = {
  isForever: boolean;
  display: string;
  monthly: string[];
  yearly: string[];
};

const ConfigureForeverOfferProductsForm = ({
  isModalOpen,
  handleCancel,
  offerID,
  initialValues,
  refreshOffer,
}: AddProps) => {
  const { message } = App.useApp();
  // Modal states
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [filters, setFilters] =
    useState<FilterForeverOfferProps>(initialFilters);

  // Queries
  const { data: products } = useFetchProducts({
    filters: {
      page: 1,
      status: "active",
      isForever: true,
    },
  });

  useEffect(() => {
    if (initialValues) {
      const allPlans = products?.items.reduce(
        (acc: PlanDTO[], product: Product) => {
          return acc.concat(product.plans);
        },
        []
      );
      if (!allPlans || allPlans?.length === 0) return;
      let monthlyPlan: PlanDTO | undefined, yearlyPlan: PlanDTO | undefined;
      if (initialValues?.monthly && initialValues?.monthly.length > 0) {
        let monthlyPlanId = initialValues?.monthly[0];
        monthlyPlan = allPlans.find((x) => x.chargebeeId === monthlyPlanId);
      } else {
        monthlyPlan = EmptyPlanItem;
      }
      if (initialValues?.yearly && initialValues?.yearly.length > 0) {
        let yearlyPlanId = initialValues?.yearly[0];
        yearlyPlan = allPlans.find((x) => x.chargebeeId === yearlyPlanId);
      } else {
        yearlyPlan = EmptyPlanItem;
      }
      // find the product from the list of products where the plan is present
      let product = products?.items.find((x: Product) => {
        return x.plans.find((y: PlanDTO) => {
          return (
            y.chargebeeId === monthlyPlan?.chargebeeId ||
            y.chargebeeId === yearlyPlan?.chargebeeId
          );
        });
      });
      setFilters((prev: FilterForeverOfferProps) => ({
        ...prev,
        selectedProduct: product,
        display: initialValues.display,
        selectedMonthlyPlan: monthlyPlan,
        selectedYearlyPlan: yearlyPlan,
      }));
    }
  }, [products]);

  const attachProductsToOffer = async () => {
    if (!filters.selectedProduct) {
      return;
    }
    if (
      (!filters.selectedMonthlyPlan?.id ||
        filters.selectedMonthlyPlan?.id === 0) &&
      (!filters.selectedYearlyPlan?.id || filters.selectedYearlyPlan?.id === 0)
    ) {
      message.open({
        type: "error",
        content: "Please select at least one plan",
      });
      return;
    }
    let payload: SubmitPayload = {
      isForever: true,
      display: filters.display,
      monthly: [],
      yearly: [],
    };
    if (filters.selectedMonthlyPlan && filters.selectedMonthlyPlan.id !== 0) {
      payload.monthly.push(filters?.selectedMonthlyPlan.chargebeeId as string);
    }
    if (filters.selectedYearlyPlan && filters.selectedYearlyPlan.id !== 0) {
      payload.yearly.push(filters?.selectedYearlyPlan.chargebeeId as string);
    }
    const res = await createOfferProducts({
      values: payload,
      id: offerID,
    });
    if (res === 200 || res === 201) {
      message.open({
        type: "success",
        content: "Offer products added successfully!",
      });
      handleCancel();
      await refreshOffer();
    }
  };

  if (!isModalOpen) return;

  return (
    <Drawer
      width={"100vw"}
      open={isModalOpen}
      onClose={() => {
        handleCancel;
      }}
      closeIcon={false}
      destroyOnClose={true}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          paddingBottom: "40px",
        }}
      >
        <div style={{ width: "100vw" }}>
          <Row
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              paddingRight: "30px",
              paddingLeft: "30px",
            }}
          >
            <div
              style={{
                display: "flex",
                width: "60%",
                justifyContent: "start",
                alignItems: "center",
              }}
            >
              <Space>
                <Button
                  icon={<ArrowLeftOutlined style={{ color: "#5B5B5D" }} />}
                  onClick={() => {
                    handleCancel();
                  }}
                />
                <Text
                  style={{ fontSize: 24 }}
                >{`Configure offer's products`}</Text>
              </Space>
            </div>
            <Space>
              <Button
                icon={<CloseOutlined />}
                onClick={() => {
                  handleCancel();
                }}
              >
                <Text strong>Dismiss</Text>
              </Button>
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={attachProductsToOffer}
              >
                <Text style={{ color: "white" }} strong>
                  {initialValues ? "Update" : "Save"}
                </Text>
              </Button>
            </Space>
          </Row>
          <Divider />

          <ConfigureForeverOfferProducts
            filters={filters}
            setFilters={setFilters}
            setShowAddProductModal={setShowAddProductModal}
          />

          {showAddProductModal && (
            <AddForeverProductModal
              isOpen={showAddProductModal}
              filters={filters}
              products={products?.items as Product[]}
              setIsOpen={setShowAddProductModal}
              setFilters={setFilters}
            />
          )}
        </div>
      </div>
    </Drawer>
  );
};

export default ConfigureForeverOfferProductsForm;
