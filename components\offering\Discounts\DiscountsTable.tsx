"use client";

import { App, Divider, Table, Typography } from "antd";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import AddDiscountForm from "@/components/offering/Discounts/forms/AddDiscountForm";
import DiscountsFilterTool from "./DiscountsFilterTool";
import paginationConfig from "../../form/paginationConfig";
import CreateQuickDiscountModal from "./modals/QuickDiscount";
import { convertDateToISO } from "@/lib/date";
import { useProtectPage } from "@/hooks/roles";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useSearchDiscounts } from "@/hooks/offering/discounts";
import { DiscountsFilter } from "@/types";
import { createDiscount } from "@/lib/services";
import { getDiscountsTableColumns } from "./DiscountsTableColumns";

const { Title } = Typography;

export const initialFilter = {
  name: "",
  productLine: null,
  productFamily: null,
  product: null,
  productPlan: null,
  status: "active",
};

const DiscountsTable = () => {
  const router = useRouter();
  const { message } = App.useApp();
  const [filters, setFilters] = useState<DiscountsFilter>(initialFilter);
  const searchTerm = useDebounce(filters.name);
  const { canAccess } = useProtectPage("discounts-list");

  // Modals
  const [creatingNewCoupon, setCreatingNewCoupon] = useState(false);
  const [showQuickCouponModal, setQuickCouponModal] = useState(false);

  const columns = getDiscountsTableColumns();

  // Queries
  const {
    data: discounts,
    isLoading,
    mutate: refetchDiscounts,
  } = useSearchDiscounts({
    filters: {
      ...filters,
      name: searchTerm,
    },
  });

  const handleDiscountAdd = async (values: any) => {
    if (values.validUntil) {
      values.validUntil = convertDateToISO(values.validUntil);
    }
    let res = await createDiscount(values);
    if (res && res.id) {
      message.open({
        type: "success",
        content: "Discount added!",
      });
      router.push(`/dashboard/discounts/${res.id}`);
    }
    setCreatingNewCoupon(false);
  };

  const handleRowClick = (data: any) => {
    router.push(`/dashboard/discounts/${data.id}`);
  };

  useEffect(() => {
    refetchDiscounts();
  }, [
    searchTerm,
    filters.productLine,
    filters.productFamily,
    filters.product,
    filters.status,
  ]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>All Discounts</Title>
      <DiscountsFilterTool
        filters={filters}
        setFilters={setFilters}
        onCreateDiscount={() => {
          if (canAccess("discount-create")) {
            setCreatingNewCoupon(true);
          }
        }}
        onCreateQuickDiscount={() => {
          if (canAccess("quick-discount-create")) {
            setQuickCouponModal(true);
          }
        }}
      />
      <Divider />
      <Table
        dataSource={discounts?.items}
        loading={isLoading}
        columns={columns}
        pagination={paginationConfig}
        rowClassName={() => {
          return "paradox-table-row";
        }}
        rowKey="code"
        onRow={(data) => {
          return {
            onClick: () => {
              handleRowClick(data);
            },
          };
        }}
      />
      {creatingNewCoupon ? (
        <AddDiscountForm
          isModalOpen={creatingNewCoupon}
          handleCancel={() => setCreatingNewCoupon(false)}
          handleAdd={handleDiscountAdd}
        />
      ) : null}
      <CreateQuickDiscountModal
        isModalOpen={showQuickCouponModal}
        handleCancel={() => setQuickCouponModal(false)}
      />
    </div>
  );
};

export default DiscountsTable;
