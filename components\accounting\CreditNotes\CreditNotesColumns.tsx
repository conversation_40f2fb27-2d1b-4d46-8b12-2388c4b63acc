import { Typography, Tag } from "antd";

import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import type { CreditNote } from "@/types";

const { Text } = Typography;

export const getCreditNotesColumns = (router: any) => [
  {
    title: "Id",
    dataIndex: "chargebeeId",
    key: "id",
  },
  {
    title: "Customer Email",
    dataIndex: "customerEmail",
    key: "id",
    render(val: string) {
      return (
        <>
          <Text copyable>{val}</Text>
        </>
      );
    },
  },
  {
    title: "Customer Name",
    dataIndex: "customerName",
    key: "id",
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "id",
    render: (status: string) => {
      return (
        <Tag color={status === "refunded" ? "green" : "orange"}>
          {capitalizeWord(status)}
        </Tag>
      );
    },
  },
  {
    title: "Type",
    dataIndex: "type",
    key: "id",
    render: (value: string) => {
      return capitalizeWord(value);
    },
  },
  {
    title: "Reason Code",
    dataIndex: "createReason",
    key: "id",
  },
  {
    title: "Credit Note Date",
    dataIndex: "date",
    render: (value: number) => {
      return formatDateFromUnix(value);
    },
    key: "id",
  },
  {
    title: "Total",
    dataIndex: "total",
    render: (value: number) => {
      return formatCents(value);
    },
    key: "id",
  },
  {
    title: "Amount allocated",
    dataIndex: "amountAllocated",
    render: (value: number) => {
      return formatCents(value);
    },
    key: "id",
  },
  {
    title: "Amount refunded",
    dataIndex: "amountRefunded",
    render: (value: number) => {
      return formatCents(value);
    },
    key: "id",
  },
  {
    title: "Amount available",
    dataIndex: "amountAvailable",
    render: (value: number) => {
      return formatCents(value);
    },
    key: "id",
  },
  {
    title: "Currency",
    dataIndex: "currencyCode",
    key: "id",
  },
  {
    title: "Customer",
    dataIndex: "customerId",
    render: (value: string) => {
      return <span>{value}</span>;
    },
    key: "id",
  },
  {
    title: "Invoice",
    dataIndex: "referenceInvoiceId",
    key: "id",
    render: (value: string) => {
      return (
        <a
          onClick={(e) => {
            e.stopPropagation();
            router.push(`/dashboard/invoices/${value}`);
          }}
        >
          {value}
        </a>
      );
    },
  },
  {
    title: "Subscription",
    dataIndex: "subscriptionId",
    key: "id",
    render: (id: string) => {
      return (
        <a
          onClick={(e) => {
            e.stopPropagation();
            router.push(`/dashboard/subscriptions/${id}`);
          }}
        >
          {id}
        </a>
      );
    },
  },
  {
    title: "Create reason code",
    dataIndex: "createReasonCode",
    key: "id",
  },
  {
    title: "Linked refunds",
    key: "id",
    render: (cn: CreditNote) => {
      return cn.linkedRefunds?.map((ref) => {
        return (
          <a
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/dashboard/transactions/${ref.txn_id}`);
            }}
            key={ref.txn_id}
          >
            {ref.txn_id}
          </a>
        );
      });
    },
  },
  {
    title: "Business entity",
    dataIndex: "businessEntityId",
    key: "id",
  },
];
