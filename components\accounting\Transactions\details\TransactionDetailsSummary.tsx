"use client";

import { Space, Tag } from "antd";

import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { Transaction } from "@/types";

type TransactionDetailsSummaryProps = {
  transaction: Transaction | undefined;
  onSubscriptionClick: (subscriptionId: string) => void;
  onInvoiceClick: (invoiceId: string) => void;
  onCreditNoteClick: (creditNoteId: string) => void;
};

export const getTransactionDetailsSummary = ({
  transaction,
  onSubscriptionClick,
  onInvoiceClick,
  onCreditNoteClick,
}: TransactionDetailsSummaryProps) => [
  {
    key: "1",
    label: "Transaction type",
    children: (
      <p style={{ margin: "0px" }}>
        {transaction?.type &&
          capitalizeWord(transaction.type.replace(/_/g, " "))}
      </p>
    ),
  },
  {
    key: "2",
    label: "Status",
    children: (
      <p style={{ margin: "0px" }}>{capitalizeWord(transaction?.status)}</p>
    ),
  },
  {
    key: "3",
    label: "Date",
    children: (
      <p style={{ margin: "0px", padding: "0px" }}>
        {transaction?.date && formatDateFromUnix(transaction.date)}
      </p>
    ),
  },
  {
    key: "4",
    label: "Amount",
    children: (
      <p style={{ margin: 0 }}>
        {formatCents(transaction?.amount as number)} &nbsp;
        {transaction?.currency}
      </p>
    ),
  },
  {
    key: "5",
    label: "Amount unsued",
    children: (
      <p style={{ margin: 0 }}>
        {formatCents(transaction?.amountUnused as number)} &nbsp;
        {transaction?.currency}
      </p>
    ),
  },
  {
    key: "6",
    label: "Subscription",
    children: (
      <Space>
        {transaction?.subscriptionId ? (
          <a
            onClick={(e) => {
              e.stopPropagation();
              onSubscriptionClick(transaction.subscriptionId as string);
            }}
            style={{ margin: 0 }}
          >
            {transaction?.subscriptionId && transaction.subscriptionId}
          </a>
        ) : (
          "(Unknown)"
        )}
      </Space>
    ),
  },
  {
    key: "7",
    label: "Invoice",
    children: (
      <Space direction="vertical">
        {transaction?.linkedInvoices
          ? transaction?.linkedInvoices.map((invoice) => {
              return (
                <a
                  onClick={(e) => {
                    e.stopPropagation();
                    onInvoiceClick(invoice.invoice_id);
                  }}
                  key={invoice.invoice_id}
                >
                  <Tag color="blue">{invoice.invoice_id}</Tag>
                </a>
              );
            })
          : "(Unknown)"}
      </Space>
    ),
  },
  {
    key: "8",
    label: "Credit note",
    children: (
      <Space>
        {transaction?.linkedCreditNotes &&
        transaction?.linkedCreditNotes.length > 0
          ? transaction?.linkedCreditNotes.map((cn) => {
              return (
                <a
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreditNoteClick(cn.cn_id);
                  }}
                  key={cn.cn_id}
                >
                  {cn.cn_id}
                </a>
              );
            })
          : "(Unknown)"}
      </Space>
    ),
  },
  {
    key: "9",
    label: "Customer",
    children: (
      <p style={{ margin: 0 }}>
        {transaction?.customerEmail ? transaction.customerEmail : "(Unknown)"}
      </p>
    ),
  },
  ...(transaction?.status === "failure"
    ? [
        {
          key: "10",
          label: "Error code",
          children: (
            <span>
              <Tag color="red">{transaction?.errorCode}</Tag>
            </span>
          ),
        },
        {
          key: "11",
          label: "Error text",
          children: <span>{transaction?.errorText}</span>,
        },
      ]
    : []),
];
