"use client";

import { App } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import useSWR, { SWRConfiguration } from "swr";

import { getBankTransfers, updateBankTransferStatus } from "@/lib/services";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useProtectPage } from "@/hooks/roles";
import {
  BankTransferListResponse,
  BankTransfersFilters,
  UnMatchedTransactions,
  UpdateBankTransferParams,
} from "@/types";

type UseFetchBankTransfersParams = {
  options?: SWRConfiguration;
  filters?: BankTransfersFilters;
};

/**
 * This function is used to prepare the filters for the fetch bank transfers query.
 * It is used to handle the preparation of the filters for the fetch bank transfers query.
 *
 * @returns The prepared filters for the fetch bank transfers query.
 */
function prepareFetchBankTransfers(filters?: BankTransfersFilters) {
  const options: BankTransfersFilters = { ...filters };
  return options;
}

/**
 * This hook is used to fetch bank transfers.
 * It is used to handle the fetching of bank transfers.
 *
 * @returns The query for fetching bank transfers.
 */
export const useFetchBankTransfers = (params?: UseFetchBankTransfersParams) => {
  const options: BankTransfersFilters = prepareFetchBankTransfers(
    params?.filters
  );

  return useSWR<BankTransferListResponse>(
    "useFetchBankTransfers",
    () => getBankTransfers(options),
    params?.options
  );
};

export type UnmatchedTransactionsTableState = {
  filters: BankTransfersFilters;
  isMatchingModalOpen: boolean;
  searchQuery: string;
  customerFilter: string;
  selectedTransaction: UnMatchedTransactions | undefined;
  unmatchedTrx: UnMatchedTransactions[];
};

const initialFilter: BankTransfersFilters = {};

/**
 * This hook is used to manage the state of the unmatched transactions table.
 * It is used to handle the state of the table, the filters, the search query,
 * the customer filter, the selected transaction, and the unmatched transactions.
 * It also handles the open matching modal, the open specific transaction,
 * the handle skip transaction, the handle update transaction, the handle filter change,
 * and the reset filters.
 *
 * @returns The state of the unmatched transactions table.
 */
export const useUnmatchedTransactionsTable = () => {
  const { message } = App.useApp();
  const { canAccess } = useProtectPage("unmatched-transactions-list");

  const [pageState, setPageState] = useState<UnmatchedTransactionsTableState>({
    filters: initialFilter,
    isMatchingModalOpen: false,
    searchQuery: "",
    customerFilter: "",
    selectedTransaction: undefined,
    unmatchedTrx: [],
  });
  const debouncedSearchTerm = useDebounce(pageState.searchQuery);
  const debouncedCustomerName = useDebounce(pageState.customerFilter);

  // Queries
  const {
    data: transactions,
    isLoading,
    mutate: refresh,
  } = useFetchBankTransfers({
    filters: {
      ...pageState.filters,
      searchQuery: debouncedSearchTerm,
      senderName: debouncedCustomerName,
    },
  });

  const openMatchingModal = () => {
    if (canAccess("unmatched-transaction-attach")) {
      if (pageState.unmatchedTrx.length > 0) {
        setPageState((prev) => ({
          ...prev,
          selectedTransaction: prev.unmatchedTrx[0],
          isMatchingModalOpen: true,
        }));
      } else {
        message.open({
          type: "warning",
          content: "No unmatched transactions to match",
        });
      }
    }
  };

  const openSpecificTransaction = (id: number) => {
    if (canAccess("unmatched-transaction-attach")) {
      const transaction = pageState.unmatchedTrx.find(
        (transaction) => transaction.id === id
      );
      if (transaction) {
        setPageState((prev) => ({
          ...prev,
          selectedTransaction: transaction,
          isMatchingModalOpen: true,
        }));
      } else {
        message.open({
          type: "warning",
          content: "Transaction not found",
        });
      }
    }
  };

  const handleSkipTransaction = (id: number) => {
    let index = pageState.unmatchedTrx.findIndex(
      (transaction) => transaction.id === id
    );
    if (index === pageState.unmatchedTrx.length - 1) {
      index = -1;
    }
    const nextTransaction = pageState.unmatchedTrx[index + 1];
    setPageState((prev) => ({
      ...prev,
      selectedTransaction: nextTransaction
        ? nextTransaction
        : prev.unmatchedTrx[0],
    }));
  };

  const handleUpdateTransaction = async (
    id: number,
    status: "matched" | "unmatched" | "ignored"
  ) => {
    const res = await updateBankTransferStatus({
      id: id.toString(),
      status: status,
    });
    if (res.itemUpdated) {
      if (pageState.unmatchedTrx.length === 1) {
        setPageState((prev) => ({
          ...prev,
          isMatchingModalOpen: false,
          unmatchedTrx: [],
          selectedTransaction: undefined,
        }));
      }
      let index = pageState.unmatchedTrx.findIndex(
        (transaction) => transaction.id === id
      );
      if (index === pageState.unmatchedTrx.length - 1) {
        index = -1;
      }
      const nextTransaction = pageState.unmatchedTrx[index + 1];
      setPageState((prev) => ({
        ...prev,
        unmatchedTrx: prev.unmatchedTrx.filter((trx) => trx.id !== id),
        selectedTransaction: nextTransaction
          ? nextTransaction
          : prev.unmatchedTrx[0],
      }));
    }
  };

  const handleFilterChange = async (changedValues: any, allValues: any) => {
    if (changedValues?.searchQuery || changedValues.searchQuery === "") {
      setPageState((prev) => ({
        ...prev,
        searchQuery: changedValues.searchQuery,
      }));
    } else if (
      changedValues?.senderNameFilter ||
      changedValues.senderNameFilter === ""
    ) {
      setPageState((prev) => ({
        ...prev,
        customerFilter: changedValues.senderNameFilter,
      }));
    } else {
      let trxDate;
      if (allValues?.paymentDateFilter) {
        // There was an issue with the simple new Date() implementation. That takes the
        // users timezone by default. When we call the toISOString method, it takes into
        // account the timezone of the user and converts it to UTC (adds or subtracts the
        // hours). We need the value sent to the backend to be 00:00:00. So, we are
        // adding the offset from UTC to the date returned.
        trxDate = dayjs(allValues.paymentDateFilter);
        let offset = trxDate.utcOffset();
        trxDate = trxDate.add(offset, "minute");
      }
      const dateString = trxDate ? trxDate.toISOString() : undefined;
      const newValues: BankTransfersFilters = {
        status: allValues.statusFilter,
        bankName: allValues.bankFilter,
        paymentDate: dateString,
        currency: allValues.currencyFilter,
      };
      setPageState((prev) => ({
        ...prev,
        filters: newValues,
      }));
    }
  };

  const resetFilters = () => {
    setPageState((prev) => ({
      ...prev,
      filters: {},
      searchQuery: "",
      customerFilter: "",
    }));
  };

  useEffect(() => {
    refresh();
  }, [debouncedSearchTerm, debouncedCustomerName, pageState.filters]);

  return {
    pageState,
    transactions,
    isLoading,
    openMatchingModal,
    openSpecificTransaction,
    handleSkipTransaction,
    handleUpdateTransaction,
    handleFilterChange,
    resetFilters,
    setPageState,
  };
};
