"use client";

import { Button, Descriptions, Form, FormInstance, Input } from "antd";
import {
  InfoCircleFilled,
  EditOutlined,
  SaveOutlined,
} from "@ant-design/icons";

import { Offer } from "@/types";

type OfferSettingsDisabledURLProps = {
  editingRedirectIfDisableUrl: boolean;
  offer: Offer;
  updateRedirectUrlForm: FormInstance<any>;
  updateRedirectIfDisabledForm: () => void;
  updateRedirectIfDisabledUrl: (values: any) => void;
};

export default function OfferSettingsDisabledURL({
  editingRedirectIfDisableUrl,
  offer,
  updateRedirectUrlForm,
  updateRedirectIfDisabledForm,
  updateRedirectIfDisabledUrl,
}: OfferSettingsDisabledURLProps) {
  return (
    <Descriptions
      column={2}
      size="small"
      bordered
      title={
        <>
          <span style={{ display: "flex", alignItems: "center" }}>
            <InfoCircleFilled
              style={{ fontSize: 24, color: "orange", marginRight: 10 }}
            />
            Redirect URL
          </span>
          <br />
          <small style={{ fontSize: 13, color: "darkgray" }}>
            This checkout is not accessible anymore and we are redirecting the
            customers to this url:
          </small>
        </>
      }
      style={{ marginBottom: 10 }}
    >
      <Descriptions.Item label="Redirect URL">
        <Form
          layout="inline"
          form={updateRedirectUrlForm}
          onFinish={updateRedirectIfDisabledUrl}
        >
          <Form.Item
            initialValue={offer.redirectIfDisabled}
            style={{ width: "100%" }}
            name="redirectIfDisabled"
            rules={[
              { required: true, message: "This value is required" },
              { type: "url", message: "Please enter a valid URL" },
            ]}
          >
            <Input
              disabled={!editingRedirectIfDisableUrl}
              style={{ color: "gray" }}
              className="paradox-disabled-input"
            />
          </Form.Item>
          <Form.Item>
            <Button
              icon={
                editingRedirectIfDisableUrl ? (
                  <SaveOutlined />
                ) : (
                  <EditOutlined />
                )
              }
              onClick={updateRedirectIfDisabledForm}
              style={{
                color: editingRedirectIfDisableUrl ? "green" : "",
              }}
              type="link"
            >
              {editingRedirectIfDisableUrl ? "Save" : "Edit"}
            </Button>
          </Form.Item>
        </Form>
      </Descriptions.Item>
    </Descriptions>
  );
}
