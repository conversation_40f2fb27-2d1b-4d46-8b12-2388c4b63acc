import {
  initChargebee,
  recordOfflinePaymentForInvoice,
} from "@/server/chargebee";
import { RecordOfflinePaymentInputParams } from "@/types";

import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the POST request for recording an offline refund.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the returned response or an error message.
 */
export async function POST(req: Request) {
  try {
    const body = (await req.json()) as RecordOfflinePaymentInputParams;
    if (body?.invoiceId) {
      const response = await recordOfflinePaymentForInvoice(body);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RECORD_OFFLINE_PAYMENT" },
      { status: 400 }
    );
  }
}
