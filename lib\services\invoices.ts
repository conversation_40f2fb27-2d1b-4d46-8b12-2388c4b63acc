"use server";

import { GetAllInvoicesParams, Invoice, InvoiceListResponse } from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/invoices`;

/**
 * Retrieves invoices from the Paradox API.
 * @param params - The parameters for the invoices.
 * @returns A Promise that resolves to the invoices.
 */
export const getInvoicesParadoxApi = async (
  params: GetAllInvoicesParams
): Promise<InvoiceListResponse> => {
  let requestQuery = new URL(`${baseUrl}?`);
  const query = new URLSearchParams();
  if (params?.searchQuery) query.append("query", params?.searchQuery);
  if (params?.billinEntity) query.append("fiscalEntity", params?.billinEntity);
  if (params?.customerId) query.append("customerId", params?.customerId);
  if (params?.subscriptionId)
    query.append("subscriptionId", params?.subscriptionId);
  if (params?.status) query.append("status", params?.status);
  query.append("limit", "1000");
  query.append("page", "1");
  query.append("orderBy", "DESC");
  query.append("paidAtStart", params?.paidAtStart?.toString() || "");
  query.append("paidAtEnd", params?.paidAtEnd?.toString() || "");
  requestQuery.search = query.toString();
  const res = await osRequest(requestQuery.href, "GET");

  return res;
};

/**
 * Retrieves an invoice from the Paradox API by its ID.
 * @param id - The ID of the invoice to retrieve.
 * @returns A Promise that resolves to the JSON representation of the invoice.
 */
export const getInvoiceParadoxApi = async (id: string): Promise<Invoice> => {
  const res = await osRequest(`${baseUrl}/${id}`, "GET");
  return res;
};
