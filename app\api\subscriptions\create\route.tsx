import { CreateSubscriptionParams } from "@/types";
import { createSubscription, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the POST request to create a subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function POST(req: any) {
  try {
    const body = (await req.json()) as CreateSubscriptionParams;
    const response = await createSubscription(body);
    return NextResponse.json(response);
  } catch (error) {
    console.log("❌ Error creating subscription", error);
    return NextResponse.json(
      { error: "CANNOT_CREATE_SUBSCRIPTION", details: error },
      { status: 400 }
    );
  }
}
