"use client";

import {
  But<PERSON>,
  Col,
  Divider,
  Form,
  Image,
  Input,
  Row,
  Select,
  Typography,
} from "antd";
import { useEffect, useMemo, useState } from "react";
import countryList from "react-select-country-list";

import UploadImage from "@/components/core/UploadImage";
import { getTaxProfiles } from "@/lib/taxProfiles";
import { CommunityInfoDTO, Course, Product, SpaceInfo } from "@/types";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { useProductForm } from "@/hooks/product-catalog/products";

type UpdateProps = {
  isModalOpen: boolean;
  formValues: Product | undefined;
  line: string | undefined;
  handleUpdate: Function;
  handleCancel: () => void;
};

const { Title, Text } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

const UpdateProductForm = ({
  isModalOpen,
  formValues,
  handleUpdate,
  line,
  handleCancel,
}: UpdateProps) => {
  const countryDropdownOptions = useMemo(() => countryList().getData(), []);
  const [image, setImage] = useState<string | undefined>(formValues?.image);
  const {
    form,
    formData,
    filterCommunity,
    setFormData,
    updateCourses,
    updateCommunity,
  } = useProductForm();

  const selectedCommunities = useMemo(() => {
    if (
      !formValues ||
      !formValues?.communityIds ||
      formValues.communityIds.length === 0
    ) {
      return [];
    }
    let communityOpts = formValues?.communityIds?.reduce(
      (acc: any, curr: any) => {
        return acc.concat(curr.community);
      },
      []
    );
    return communityOpts.map((comm: any) => comm)[0];
  }, [formValues]);

  const selectedSpaces = useMemo(() => {
    if (
      !formValues ||
      !formValues?.communityIds ||
      formValues.communityIds.length === 0
    ) {
      return [];
    }
    let spaceOpts = formValues?.communityIds?.reduce((acc: any, curr: any) => {
      return acc.concat(curr.spaces);
    }, []);
    return spaceOpts.map((space: any) => space.id);
  }, [formValues]);

  const selectedCourses = useMemo(() => {
    if (!formValues || !formValues?.lmsIds || formValues.lmsIds.length === 0) {
      return [];
    }
    let courseOpts = formValues?.lmsIds?.reduce((acc: any, curr: any) => {
      return acc.concat(curr.id);
    }, []);
    return courseOpts;
  }, [formValues]);

  const formatValues = (values: any) => {
    delete values.productLineId;
    if (image) {
      values.image = image;
    }
    if (values.courses) {
      values.lmsIds = values.courses.map((id: string) => {
        const course = formData.courses.find(
          (course: Course) => course.id === id
        );
        if (course) {
          return {
            id: course.id,
            name: course.title,
          };
        }
        return {
          id: id,
          name: id,
        };
      });
    }
    if (values.community && values.spaces) {
      let communityInfo: CommunityInfoDTO[] = [
        {
          community:
            typeof values.community === "string"
              ? values.community
              : values.community[0],
          spaces: [],
        },
      ];
      values.spaces.forEach((spaceId: string) => {
        const space = formData.spaces.find(
          (space: SpaceInfo) => space.id === Number(spaceId)
        );
        if (space) {
          communityInfo[0].spaces.push({
            id: space.id,
            slug: space.slug,
            name: space.name,
          });
        }
      });
      values.communityIds = communityInfo;
    }
    // remove the spaces and courses from the values
    delete values.spaces;
    delete values.courses;
    delete values.community;
    delete values.spaceGroup;
    handleUpdate(values);
  };

  const filterSpaceGroups = (values: any) => {
    setFormData({
      ...formData,
      filteredSpaces: [],
    });
    form.setFieldsValue({
      spaces: undefined,
    });
    const newSpaces = formData.spaces.filter((x: any) =>
      values.includes(x.space_group_id)
    );
    setFormData({
      ...formData,
      filteredSpaces: newSpaces,
    });
  };

  useEffect(() => {
    if (formValues && formData.spaces) {
      form.setFieldValue("community", selectedCommunities);
      const commId =
        selectedCommunities === "PXL"
          ? process.env.NEXT_PUBLIC_PXL_COMMUNITY_ID
          : process.env.NEXT_PUBLIC_PXS_COMMUNITY_ID;
      const tempSpaceGroups = formData.spaceGroups.filter(
        (x: any) => x.community_id.toString() === commId
      );
      setFormData({
        ...formData,
        filteredSpaceGroups: tempSpaceGroups,
      });
      const tempSpaces = formData.spaces.filter(
        (x: any) => x.community_id.toString() === commId
      );
      setFormData({
        ...formData,
        filteredSpaces: tempSpaces,
      });
      form.setFieldValue(
        "spaces",
        selectedSpaces.map((space: any) => space)
      );
      form.setFieldValue("courses", selectedCourses);
    }
  }, [formValues, formData.spaces]);

  useEffect(() => {
    form.setFieldsValue(formValues);
    form.setFieldValue("productLineId", line);
    form.setFieldValue("productFamilyId", formValues?.productFamily.name);
  }, [line]);

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      onClose={handleCancel}
      title="Update product"
      submitText="Update"
      onCancel={handleCancel}
      onSubmit={() => form.submit()}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={(values) => {
          formatValues(values);
        }}
      >
        <Title level={5}>Product classification</Title>
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>

        <Form.Item
          label="Product line name"
          name="productLineId"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product line attached to this product
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product family"
          name="productFamilyId"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product family attached to this product
        </Text>
        <br />
        <br />
        <Form.Item
          label="Event"
          name="isEvent"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { label: "Yes", value: true, disabled: true },
              { label: "No", value: false },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Is the product an event?
        </Text>
        <br />
        <br />
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.isEvent !== currentValues.isEvent
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("isEvent") === true ? (
              <Row>
                <Col span={11}>
                  <Form.Item
                    label="Year"
                    name="eventYear"
                    style={{ marginBottom: "0px" }}
                    rules={[
                      ...rules,
                      () => ({
                        // custom validator to check if year is a number and in the future
                        validator(_, value) {
                          if (isNaN(value)) {
                            return Promise.reject(
                              new Error("Year must be a number")
                            );
                          }
                          // year has to be current year or in the future, cannot be in the past
                          if (value < new Date().getFullYear()) {
                            return Promise.reject(
                              new Error("Year must be in the future")
                            );
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <Input placeholder="2024" type="number" />
                  </Form.Item>
                  <Text style={{ fontSize: 13, color: "darkgray" }}>
                    Event year
                  </Text>
                </Col>
                <Col span={11} offset={2}>
                  <Form.Item
                    label="Location"
                    name="eventLocation"
                    style={{ marginBottom: "0px" }}
                    rules={rules}
                  >
                    <Select
                      placeholder="Country"
                      options={countryDropdownOptions}
                      allowClear
                      showSearch
                      optionFilterProp="label"
                    />
                  </Form.Item>
                  <Text style={{ fontSize: 13, color: "darkgray" }}>
                    Event location
                  </Text>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>
        <Divider />
        <Title level={5}>Product settings</Title>
        <br />
        <Form.Item
          label="Product internal name"
          name="internalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="EDEC-CERTIFICATE" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this product internally.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product external name"
          name="externalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="EDEC-CERTIFICATE" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This will be used in invoices, Checkout & Self-Serve Portal.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product description"
          name="description"
          style={{ marginBottom: "0px" }}
        >
          <Input placeholder="Product description" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This will be used in invoices, Checkout & Self-Serve Portal.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product code"
          name="code"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input disabled placeholder="EDEC-CERTIFICATE" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This is the unique product code identifier for this product
        </Text>
        <br />
        <br />
        <Form.Item
          label="Status"
          name="status"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "active", label: "Active" },
              { value: "draft", label: "Draft" },
            ]}
          />
        </Form.Item>
        <Divider />
        <Title level={5}>Fiscality</Title>
        <br />
        <Form.Item
          label="Tax Profile (Product type)"
          name="taxProfile"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            placeholder="Select a tax profile"
            options={getTaxProfiles().map((item) => {
              return {
                value: item.value,
                label: <span>{item.label}</span>,
                description: item.description,
              };
            })}
            optionRender={(option) => (
              <div>
                <Text>{option.label}</Text> <br />
                <Text
                  style={{
                    fontSize: 13,
                    color: "gray",
                    whiteSpace: "wrap",
                  }}
                >
                  {option.data.description}
                </Text>
              </div>
            )}
          />
        </Form.Item>
        <Divider />
        <Title level={5}>Learnworld</Title>
        <br />
        <Form.Item
          label={
            <div
              style={{
                width: "100vw",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text>Courses</Text>
              <Button size="small" type="primary" onClick={updateCourses}>
                Update course list
              </Button>
            </div>
          }
          name="courses"
          style={{ marginBottom: "0px" }}
        >
          <Select
            placeholder="Select all the applicable courses"
            allowClear
            mode="multiple"
            options={formData.courses.map((item: Course) => {
              return {
                value: item.id,
                label: <span>{item.title}</span>,
                description: item.description,
                image: item.courseImage,
              };
            })}
            optionRender={(option) => (
              <Row>
                <Col span={2}>
                  <Image
                    preview={false}
                    src={option.data.image}
                    width={60}
                    style={{ borderRadius: "5px" }}
                  />
                </Col>
                <Col span={20} offset={2}>
                  <Text>{option.label}</Text> <br />
                  <Text
                    style={{
                      fontSize: 13,
                      color: "gray",
                      whiteSpace: "normal",
                    }}
                  >
                    {option.data.description}
                  </Text>
                </Col>
              </Row>
            )}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Please select which courses on Learnworld this product gives access
          to.
        </Text>
        <Divider />
        <Title level={5}>Circle</Title>
        <br />
        <Form.Item
          label={
            <div
              style={{
                width: "100vw",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text>Community</Text>
              <Button size="small" type="primary" onClick={updateCommunity}>
                Update community info
              </Button>
            </div>
          }
          name="community"
          style={{ marginBottom: "0px" }}
        >
          <Select
            placeholder="Select the applicable communities"
            allowClear
            options={[
              { value: "PXL", label: "PXL" },
              { value: "PXS", label: "PXS" },
            ]}
            onChange={filterCommunity}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Select the applicable community
        </Text>
        <br />
        <br />
        <Row>
          <Col span={11}>
            <Form.Item
              label="Space Group"
              name="spaceGroup"
              style={{ marginBottom: "0px" }}
            >
              <Select
                placeholder="Select all the applicable space groups"
                allowClear
                mode="multiple"
                options={formData.filteredSpaceGroups.map((item: any) => {
                  return {
                    value: item.id,
                    label: <span>{item.name}</span>,
                  };
                })}
                onChange={filterSpaceGroups}
              />
            </Form.Item>
          </Col>
          <Col span={11} offset={2}>
            <Form.Item
              label="Spaces"
              name="spaces"
              style={{ marginBottom: "0px" }}
            >
              <Select
                placeholder="Select all the applicable spaces"
                allowClear
                mode="multiple"
                options={formData.filteredSpaces.map((item: any) => {
                  return {
                    value: item.id,
                    label: <span>{item.name}</span>,
                  };
                })}
              />
            </Form.Item>
          </Col>
        </Row>
        <Divider />
        <Title level={5}>Other Settings</Title>
        <br />
        <UploadImage
          name="image"
          label="Product image"
          setImage={setImage}
          url={image}
        />
        <Text style={{ fontSize: 13, color: "darkgray", marginLeft: 10 }}>
          This image will be display on the checkout, user portal & invoices
        </Text>
      </Form>
    </DrawerFormContainer>
  );
};

export default UpdateProductForm;
