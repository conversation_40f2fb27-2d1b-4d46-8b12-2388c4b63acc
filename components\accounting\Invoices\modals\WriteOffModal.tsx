"use client";

import { App, Form, Input, Modal, Typography } from "antd";
import { InfoCircleFilled } from "@ant-design/icons";

import { Invoice } from "@/types";
import { writeOffInvoice } from "@/lib/services/chargebee";

type WriteOffProps = {
  isOpen: boolean;
  setIsOpen: Function;
  invoice: Invoice;
};

const { Text } = Typography;

export const WriteOffModal = ({
  isOpen,
  setIsOpen,
  invoice,
}: WriteOffProps) => {
  const [writeOffForm] = Form.useForm();
  const { message, modal } = App.useApp();

  const writeOffCurrentInvoice = async (values: any) => {
    modal.warning({
      title: `Are you sure you want to write off this invoice?`,
      content: `By clicking proceed, this invoice will be written off and a credit 
      note of type adjusted will automatically be created for and applied to this invoice.`,
      okText: "Proceed",
      okType: "primary",
      cancelText: "Close",
      okCancel: true,
      onCancel: () => {},
      onOk: async () => {
        const res = await writeOffInvoice({
          id: String(invoice?.chargebeeId),
          comment: values.comment,
        });
        if (res?.error) {
          message.open({
            type: "error",
            content: "Cannot write off invoice!",
          });
        } else {
          message.open({
            type: "success",
            content: "Invoice written off successfully!",
          });
        }
        setIsOpen(false);
      },
    });
  };

  return (
    <Modal
      open={isOpen}
      closable={false}
      onOk={() => {
        writeOffForm.submit();
      }}
      onCancel={() => {
        setIsOpen(false);
      }}
      okButtonProps={{ danger: true }}
      okText="Confirm"
    >
      <InfoCircleFilled style={{ color: "red" }} />
      &nbsp;
      <Text style={{ fontSize: 15 }} strong>
        Write-off this invoice #{invoice.id}
      </Text>
      <br />
      <Text style={{ fontSize: 13, color: "gray" }}>
        A credit note will be created and adjusted against this invoice upon
        writing off. The total amount due for this invoice will be 0.00€
      </Text>
      <br />
      <br />
      <Form
        layout="vertical"
        form={writeOffForm}
        onFinish={writeOffCurrentInvoice}
      >
        <Form.Item name="comment" label="Add a comment (optional)">
          <Input.TextArea
            placeholder="Your comment will be added to this invoice
              and the credit note created against this write-off"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
