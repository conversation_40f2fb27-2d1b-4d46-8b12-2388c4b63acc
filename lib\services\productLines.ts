"use server";

import { ProductLineListResponse, ProductLineParams } from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/product-lines`;

/**
 * Creates a new product line.
 * @param params - The parameters for creating the product line.
 * @returns A Promise that resolves to the response JSON.
 */
export const createProductLine = async (
  params: ProductLineParams
): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}`, "POST", params);

    return res;
  } catch (err) {
    console.error("cannot createProductLine: ", err);
  }
};

/**
 * Returns a list of product lines
 * @param page Page number
 * @param limit Number of lines to retrieve
 * @returns List of product lines with total count and current page number
 */
export const getAllProductLines = async (
  page: number,
  limit: number,
  status?: string,
  search?: string
): Promise<ProductLineListResponse> => {
  let requestQuery = new URL(`${baseUrl}`);
  const query = new URLSearchParams();
  query.set("page", page.toString());
  query.set("limit", limit.toString());
  if (status !== undefined) {
    query.set("status", status);
  }
  if (search !== undefined) {
    query.set("name", search);
  }
  requestQuery.search = query.toString();
  const res = await osRequest(requestQuery.toString(), "GET");

  if (!res || res.message) {
    throw new Error("Failed to fetch product lines", res);
  }

  return res;
};

/**
 * Updates a product line with the specified ID.
 *
 * @param params - The parameters to update the product line with.
 * @returns A Promise that resolves to the updated product line.
 */
export const updateProductLine = async (
  params: ProductLineParams
): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}/${params.id}`, "PATCH", params);

    return res;
  } catch (err) {
    console.error("cannot updateProductLine: ", err);
  }
};
