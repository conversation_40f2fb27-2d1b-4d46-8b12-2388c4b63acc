"use client";

import { CalendarOutlined } from "@ant-design/icons";
import { App, Divider, Table, Tag, Tooltip, Typography } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import AddOfferForm from "@/components/offering/Offers/forms/AddOfferForm";
import OffersFilterTool from "./OffersFilterTool";
import paginationConfig from "../../form/paginationConfig";
import { useFetchOffers } from "@/hooks/offering/offers";
import { useProtectPage } from "@/hooks/roles";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchProductLines } from "@/hooks/product-catalog/productLines";
import { useFetchProducts } from "@/hooks/product-catalog/products";
import { createOffer } from "@/lib/services";
import { FilterOffer, Offer, Product, ProductLine } from "@/types";
import { getPlanLink } from "@/lib/chargebee";
import { isOfferAccessConfigured } from "./forms/offerAccess/utils";
import LinkChargebee from "@/components/core/LinkChargebee";

const { Text, Title } = Typography;

const columns = [
  {
    title: "Offer Name",
    dataIndex: "name",
    key: "name",
    render: (val: string, data: Offer) => {
      const { hasCommunityAccess, hasLmsAccess } =
        isOfferAccessConfigured(data);
      let tooltipMsg = [];
      if (!hasCommunityAccess) {
        tooltipMsg.push(<li>No Circle access configured</li>);
      }
      if (!hasLmsAccess) {
        tooltipMsg.push(<li>No Learnword access configured</li>);
      }
      return (
        <div style={{ display: "flex", alignItems: "center" }}>
          <img
            src={data?.image}
            width={48}
            height={48}
            style={{ marginRight: 10 }}
          />
          {!hasCommunityAccess || !hasLmsAccess ? (
            <Tooltip title={<ul>{tooltipMsg}</ul>}>
              <Tag color="red">⚠️</Tag>
            </Tooltip>
          ) : null}
          <Text>{val}</Text>
        </div>
      );
    },
  },
  {
    title: "Offer ID",
    dataIndex: "chargebeeId",
    key: "chargebeeId",
    onCell: () => ({
      onClick: (e: React.MouseEvent<HTMLAnchorElement>) => {
        e.stopPropagation();
      },
    }),
    render: (val: string) => {
      return <LinkChargebee url={getPlanLink(val)}>{val}</LinkChargebee>;
    },
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (val: string) => {
      if (val === "active") {
        return <Tag color="green">Active</Tag>;
      }
      if (val === "disabled") {
        return <Tag color="orange">Disabled</Tag>;
      }
      if (val === "expired") {
        return <Tag color="red">Expired</Tag>;
      }
    },
  },
  {
    title: "Fiscal Entity",
    dataIndex: "fiscalEntity",
    key: "fiscalEntity",
  },
  {
    title: (
      <>
        <CalendarOutlined />
        &nbsp;Created At
      </>
    ),
    dataIndex: "createdAt",
    key: "createdAt",
    render: (val: string) => {
      return <Text>{new Date(val).toLocaleDateString()}</Text>;
    },
  },
  {
    title: "Redirect URL",
    dataIndex: "redirectUrl",
    key: "redirectUrl",
    render: (val: string) => {
      return (
        <Text copyable type="secondary">
          {val}
        </Text>
      );
    },
  },
];

export const initialFilter: FilterOffer = {
  name: "",
  productLine: null,
  productFamily: null,
  product: null,
  productPlan: null,
  status: null,
};

const OffersTable = () => {
  const router = useRouter();
  const { message } = App.useApp();
  const [filters, setFilters] = useState<FilterOffer>(initialFilter);
  const searchTerm = useDebounce(filters.name);
  const { canAccess } = useProtectPage("offers-list");

  // Modals
  const [creatingNewOffer, setCreatingNewOffer] = useState(false);

  // Queries
  const { data: productLines } = useFetchProductLines();
  const { data: products } = useFetchProducts({
    filters: {
      page: 1,
      status: "active",
      productLine: filters.productLine,
      productFamily: filters.productFamily,
    },
  });
  const {
    isLoading,
    data: offers,
    mutate: refetchOffers,
  } = useFetchOffers({
    filters: { ...filters, name: searchTerm },
  });

  const handleOfferAdd = async (values: any) => {
    let res = await createOffer(values);
    if (res && res.id) {
      message.open({
        type: "success",
        content: "Offer added!",
      });
      router.push(`/dashboard/offers/${res.chargebeeId}`);
    } else {
      message.open({
        type: "error",
        content: "Error creating offer!",
      });
    }
    setCreatingNewOffer(false);
  };

  useEffect(() => {
    refetchOffers();
  }, [
    filters.productLine,
    filters.productFamily,
    filters.product,
    filters.productPlan,
    filters.status,
    searchTerm,
  ]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Offers</Title>
      <OffersFilterTool
        filters={filters}
        setFilters={setFilters}
        productLines={productLines?.items as ProductLine[]}
        products={products?.items as Product[]}
        onCreateOffer={() => {
          if (canAccess("offer-create")) {
            setCreatingNewOffer(true);
          }
        }}
      />
      <Divider />
      <Table
        dataSource={offers?.items}
        loading={isLoading}
        columns={columns}
        pagination={paginationConfig}
        rowClassName={() => {
          return "paradox-table-row";
        }}
        size="small"
        rowKey="id"
        onRow={(data) => {
          return {
            onClick: () => {
              router.push(`/dashboard/offers/${data.chargebeeId}`);
            },
          };
        }}
      />
      {creatingNewOffer && (
        <AddOfferForm
          isModalOpen={creatingNewOffer}
          handleCancel={() => setCreatingNewOffer(false)}
          handleAdd={handleOfferAdd}
        />
      )}
    </div>
  );
};

export default OffersTable;
