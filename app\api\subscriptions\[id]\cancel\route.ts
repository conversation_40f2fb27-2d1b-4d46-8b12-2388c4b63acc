import { CancelSubscriptionParams } from "@/types";
import { cancelSubscription, initChargebee } from "@/server/chargebee";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the POST request to cancel a subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { id, cancel_at, cancel_reason, user_email } =
      body as CancelSubscriptionParams;
    const response = await cancelSubscription({
      id,
      cancel_at,
      cancel_reason,
      user_email,
    });
    return NextResponse.json(response);
  } catch (error: any) {
    return NextResponse.json(
      { error: "CANNOT_CANCEL_SUBSCRIPTION", details: error || error?.message },
      { status: 400 }
    );
  }
}
