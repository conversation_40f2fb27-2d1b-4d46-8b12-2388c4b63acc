import { CommunityInfo } from "./Community";
import { CourseInfo } from "./LMS";
import { ProductFamily } from "./ProductFamilies";
import { ProductLine } from "./ProductLines";
import { PlanDTO } from "./ProductPlans";

export type ProductUpdateParams = {
  id?: number;
  code?: string;
  fiscalEntity: string;
  image: string;
  internalName: string;
  externalName: string;
  description: string;
  status?: string;
  taxProfile?: string;
  isEvent?: boolean;
  eventYear?: string;
  eventLocation?: string;
  withProductDelivery: boolean;
  lmsIds?: CourseInfo[];
  communityIds?: CommunityInfo[];
};

export type ProductsFilter = {
  entity?: any | null;
  productLine?: any | null;
  page?: number;
  search?: string;
  productFamily?: any | null;
  status?: string | null;
  isForever?: boolean;
} & {
  current?: ProductLine;
};

export type GetAllProductsParams = {
  page?: number;
  limit?: number;
  searchQuery?: string;
  line?: string;
  family?: string;
  status?: string;
  entity?: string;
  isForever?: boolean;
};

/**
 * Represents a product list item.
 */
export type ProductListItem = {
  /**
   * The name of the product.
   */
  name: string;
  /**
   * The code of the product (generated by the system)
   */
  code: string;
  /**
   * The amount of the product.
   */
  amount: number;
};

export type Product = {
  code: string;
  createdAt: string;
  description: string;
  fiscalEntity: string;
  id: number;
  image: string;
  taxProfile?: string;
  isEvent?: boolean;
  eventYear?: string;
  eventLocation?: string;
  isForever?: boolean;
  internalName: string;
  externalName: string;
  productFamily: ProductFamily;
  productLine: ProductLine;
  status: string;
  withProductDelivery: boolean;
  lmsIds?: CourseInfo[];
  communityIds?: CommunityInfo[];
  updatedAt: string;
  plans: PlanDTO[];
};

export type ProductsListResponse = {
  items: Product[];
  total: number;
  currentPage: number;
};

export type Plan = {
  chargebeeId: string;
  createdAt: string;
  deletedAt: string;
  description: string;
  externalName: string;
  fiscalEntity: string;
  id: number;
  internalName: string;
  status: string;
  updatedAt: string;
};

export type ProductParams = {
  id?: number;
  code: string;
  fiscalEntity: string;
  image: string;
  internalName: string;
  externalName: string;
  productFamilyId: number;
  productLineId: number;
  description: string;
  taxProfile: string;
  isForever?: boolean;
  isEvent?: boolean;
  eventYear?: string;
  eventLocation?: string;
  withProductDelivery: boolean;
  lmsIds?: CourseInfo[];
  communityIds?: CommunityInfo[];
};
