"use client";

import { StepProps, Steps } from "antd";
import { useEffect, useState } from "react";

import { StepperFormsContainerProps } from "@/types";

const StepperFormsContainer = ({
  currentIdx,
  steps,
}: StepperFormsContainerProps) => {
  const [items, setItems] = useState<StepProps[]>(
    steps.map((step) => ({
      title: step.title,
      status: step.status,
    })) as StepProps[]
  );

  const onChange = (value: number) => {
    setItems((prev) => {
      return prev.map((item, index) => {
        if (index === value) {
          item.status = "process";
        } else if (index < value) {
          item.status = "finish";
        } else {
          item.status = "wait";
        }
        return item;
      });
    });
  };

  useEffect(() => {
    onChange(currentIdx);
  }, [currentIdx]);

  useEffect(() => {
    return () => {
      setItems(
        steps.map((step) => ({
          title: step.title,
          status: step.status,
        })) as StepProps[]
      );
    };
  }, []);

  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <Steps type="navigation" current={currentIdx} items={[...items]} />
      <div
        style={{
          marginTop: 50,
          marginLeft: 20,
        }}
      >
        {steps[currentIdx] ? steps[currentIdx].content : null}
      </div>
    </div>
  );
};

export default StepperFormsContainer;
