import * as Sentry from "@sentry/nextjs";
import type <PERSON>de<PERSON>ache from "node-cache";

export async function register() {
  if (process.env.NEXT_RUNTIME === "nodejs") {
    await import("./sentry.server.config");
    const NodeCache = (await import("node-cache")).default;
    const config: NodeCache.Options = {
      stdTTL: 0,
    };
    global.lmsCache = new NodeCache(config);
    global.communityCache = new NodeCache(config);
  }
  if (process.env.NEXT_RUNTIME === "edge") {
    await import("./sentry.edge.config");
  }
}

export const onRequestError = Sentry.captureRequestError;
