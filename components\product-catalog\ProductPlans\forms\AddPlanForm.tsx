"use client";

import { Form, Input, Select, Typography } from "antd";
import { useState } from "react";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { Product } from "@/types";

type AddProps = {
  isModalOpen: boolean;
  handleAdd: Function;
  handleCancel: Function;
  product: Product;
};

const { Title, Text } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

const AddPlanForm = ({
  isModalOpen,
  handleAdd,
  handleCancel,
  product,
}: AddProps) => {
  const [skuAddon, setSkuAddon] = useState<string>(`${product.code}-MON-`);
  const [planAddForm] = Form.useForm();

  const computePlanInternalName = (changedValues: any, allValues: any) => {
    if (product?.isForever) {
      setSkuAddon(`${product.code}-FOREVER-${allValues.billingCycle}-`);
    }
    if (
      changedValues.internalName === undefined &&
      allValues.billingCycle &&
      allValues.totalBillingCycles &&
      allValues.amount
    ) {
      planAddForm.setFieldValue(
        "internalName",
        `${product?.externalName} - ${allValues.billingCycle} - ${allValues.totalBillingCycles} - ${allValues.amount}`
      );
    }
    if (changedValues.totalBillingCycles) {
      setSkuAddon(`${product.code}-${changedValues.totalBillingCycles}-MON-`);
    }
  };

  const computeAmountPerBillingCycle = () => {
    if (product?.isForever) {
      let amount = planAddForm.getFieldValue("amount");
      planAddForm.setFieldValue("amountPerBillingCycle", amount);
    }
    let billingCycles = planAddForm.getFieldValue("totalBillingCycles");
    let amount = planAddForm.getFieldValue("amount");

    if (billingCycles && amount) {
      let tempAmount = Number(amount) / Number(billingCycles);
      planAddForm.setFieldValue("amountPerBillingCycle", tempAmount.toFixed(2));
    }
  };

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      title="Add a plan"
      onCancel={() => handleCancel()}
      onClose={() => handleCancel()}
      onSubmit={() => planAddForm.submit()}
      submitText="Add a plan"
    >
      <Form
        layout="vertical"
        onFinish={(values: any) => {
          values.crmSku = skuAddon + values.crmSku;
          handleAdd(values);
        }}
        form={planAddForm}
        onValuesChange={computePlanInternalName}
      >
        <Title level={5}>Price settings</Title>
        <br />
        <Form.Item label="Product price" name="amount" rules={rules}>
          <Input
            placeholder="10,500€"
            addonAfter="€"
            onChange={computeAmountPerBillingCycle}
          />
        </Form.Item>
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Billing cycles"
          name="billingCycle"
          rules={rules}
        >
          <Select
            placeholder="Monthly"
            options={
              product?.isForever
                ? [
                    {
                      value: "monthly-forever",
                      label: <span>Monthly - Forever</span>,
                    },
                    {
                      value: "yearly-forever",
                      label: <span>Yearly - Forever</span>,
                    },
                  ]
                : [
                    {
                      value: "monthly",
                      label: <span>Monthly - Fixed</span>,
                    },
                  ]
            }
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Choose if you&apos;d like this plan to be billed forever, for a fixed
          period or one-time payment.
        </Text>
        <br />
        <br />
        {!product?.isForever && (
          <div>
            <Form.Item
              style={{ marginBottom: "0px" }}
              label="# Billing cycles"
              name="totalBillingCycles"
              rules={rules}
            >
              <Input placeholder="12" onChange={computeAmountPerBillingCycle} />
            </Form.Item>
            <Text style={{ fontSize: 13, color: "darkgray" }}>
              Number of billing cycles for this plan
            </Text>
            <br />
            <br />
          </div>
        )}
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Amount per billing cycle"
          name="amountPerBillingCycle"
          rules={rules}
        >
          <Input disabled placeholder="1,000€" addonAfter="€" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This number is automatically computed based on the product price & #
          installments.
        </Text>
        <br />
        <br />
        <Title level={5}>Plan settings</Title>
        <br />
        <Form.Item
          label="Plan internal name"
          name="internalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="EDEC Certificate x12" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this plan internally.
        </Text>
        <br />
        <br />
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Stock Keeping Unit (SKU)"
          name="crmSku"
          rules={rules}
        >
          <Input addonBefore={skuAddon} placeholder="SKU" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Used to have a unique identifier per plan. It&apos;s also used in
          Hubspot at the product level for the property of the same name.
        </Text>
        <br />
        <br />
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Status"
          name="status"
          rules={rules}
        >
          <Select
            placeholder="Active"
            options={[
              { value: "active", label: <span>Active</span> },
              { value: "draft", label: <span>Draft</span> },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Setting a plan to &lsquo;Draft&lsquo; status will unpublished all the
          checkout page linked to this plan.
        </Text>
      </Form>
    </DrawerFormContainer>
  );
};

export default AddPlanForm;
