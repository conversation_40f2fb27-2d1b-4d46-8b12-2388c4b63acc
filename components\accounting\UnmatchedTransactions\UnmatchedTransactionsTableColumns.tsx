"use client";

import { <PERSON><PERSON>, Tag, Typography } from "antd";
import { FixedType } from "rc-table/lib/interface";

import { formatter } from "@/lib/currency";
import { formatDate } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { UnMatchedTransactions } from "@/types";

const { Text } = Typography;

const generateStatusTag = (status: string) => {
  switch (status) {
    case "matched":
      return <Tag color="green">{capitalizeWord(status)}</Tag>;
    case "unmatched":
      return <Tag color="orange">{capitalizeWord(status)}</Tag>;
    case "ignored":
      return <Tag color="gray">{capitalizeWord(status)}</Tag>;
    default:
      return <Tag>{capitalizeWord(status)}</Tag>;
  }
};

type UnmatchedTransactionsTableColumnsProps = {
  openSpecificTransaction: (id: number) => void;
};

export const getUnmatchedTransactionsTableColumns = ({
  openSpecificTransaction,
}: UnmatchedTransactionsTableColumnsProps) => [
  {
    title: "Bank name",
    dataIndex: "bankName",
    key: "id",
  },
  {
    title: "Business Entity",
    dataIndex: "entityId",
    key: "id",
  },
  {
    title: "Sender Email",
    dataIndex: "senderEmail",
    key: "id",
    render(val: string) {
      return (
        <>
          <Text copyable>{val}</Text>
        </>
      );
    },
  },
  {
    title: "Payment id",
    dataIndex: "paymentId",
    key: "id",
  },
  {
    title: "Payment date",
    dataIndex: "paymentDate",
    render: (data: string) => {
      return formatDate(data);
    },
    key: "id",
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "id",
    render: (status: string) => {
      return generateStatusTag(status);
    },
  },
  {
    title: "Payment description",
    dataIndex: "paymentDescription",
    key: "id",
    render: (description: string) => {
      return <Text ellipsis={{ tooltip: description }}>{description}</Text>;
    },
  },
  {
    title: "Sender first name",
    dataIndex: "senderFirstName",
    key: "id",
  },
  {
    title: "Sender last name",
    dataIndex: "senderLastName",
    key: "id",
  },
  {
    title: "Sender address",
    dataIndex: "senderAddress",
    key: "id",
  },
  {
    title: "Currency",
    dataIndex: "currency",
    key: "id",
  },
  {
    title: "Payment amount",
    dataIndex: "paymentAmount",
    render: (data: number) => {
      return <span>{formatter.format(data)}</span>;
    },
    key: "id",
  },
  {
    title: "Action",
    key: "id",
    fixed: "right" as FixedType,
    render: (record: UnMatchedTransactions) => {
      return (
        <Button
          type="link"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            openSpecificTransaction(record.id);
          }}
          disabled={record.status !== "unmatched"}
        >
          Attach Manually
        </Button>
      );
    },
  },
];
