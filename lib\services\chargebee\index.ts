export const requestChargebee = async (
  url: string = "",
  method: string,
  payload?: any
) => {
  const baseUrl = new URL(window.location.href).origin;
  let requestInit: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
    },
  };
  if (method !== "GET" && payload) {
    requestInit.body = JSON.stringify(payload);
  }
  return fetch(baseUrl + url, requestInit)
    .then((response) => response.json())
    .catch((err) => console.error("requestChargebee error(s)", err));
};

export * from "./coupons";
export * from "./creditNotes";
export * from "./customers";
export * from "./invoices";
export * from "./itemPrices";
export * from "./items";
export * from "./subscriptions";
