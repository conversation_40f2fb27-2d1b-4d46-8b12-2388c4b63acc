import { InvoiceLineItemPX, Invoice as InvoicePX } from "@/types";

/**
 * Computes the total discount amount from a list of invoices.
 *
 * @param invoices - The list of invoices.
 * @param totalBillingCycles - The total number of billing cycles.
 * @returns The total discount amount.
 */
export function computeDiscount(
  lineItems: InvoiceLineItemPX[],
  totalBillingCycles: number
) {
  const discountAmountReduced =
    lineItems?.reduce((acc: number, item: InvoiceLineItemPX) => {
      if (item.discount_amount) acc += item.discount_amount;
      return acc;
    }, 0) || 0;

  const result = discountAmountReduced * totalBillingCycles;
  return result;
}

/**
 * Computes the subtotal of the subcription based on the line items' amounts.
 *
 * @param invoices - The list of invoices.
 * @param totalBillingCycles - The total number of billing cycles.
 * @returns The computed subtotal.
 */
export function computeSubtotal(
  lineItems: InvoiceLineItemPX[],
  totalBillingCycles: number
) {
  const linesSubTotal = lineItems?.reduce(
    (acc: any, item: InvoiceLineItemPX) => {
      if (item.amount) acc += item.amount;
      return acc;
    },
    0
  );

  return linesSubTotal * totalBillingCycles;
}

/**
 * Computes the total tax amount based on the provided invoices and total billing cycles.
 * @param invoices - The list of invoices.
 * @param totalBillingCycles - The total number of billing cycles.
 * @returns The computed total tax amount.
 */
export function computeTax(
  lineItems: InvoiceLineItemPX[],
  totalBillingCycles: number
) {
  const linesTaxAmount = lineItems?.reduce(
    (acc: any, item: InvoiceLineItemPX) => {
      if (item.tax_amount) acc += item.tax_amount;
      return acc;
    },
    0
  );

  return linesTaxAmount * totalBillingCycles;
}

/**
 * Computes the total amount for a given list of invoices and total billing cycles.
 *
 * @param invoices - The list of invoices.
 * @param totalBillingCycles - The total number of billing cycles.
 * @returns The computed total amount.
 */
export function computeTotal(
  lineItems: InvoiceLineItemPX[],
  totalBillingCycles: number
) {
  const linesTotal = lineItems?.reduce((acc: any, item: InvoiceLineItemPX) => {
    acc += (item.amount as number) - (item.discount_amount || 0);
    return acc;
  }, 0);

  return linesTotal * totalBillingCycles;
}
