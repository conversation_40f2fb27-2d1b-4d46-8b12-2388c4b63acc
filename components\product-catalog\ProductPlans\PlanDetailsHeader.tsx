"use client";

import {
  BorderBottomOutlined,
  CopyOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { Button, Space, Tag, Typography, Image, Descriptions } from "antd";
import Link from "next/link";

import Link<PERSON>hargebee from "@/components/core/LinkChargebee";
import PageHeader from "@/components/core/PageHeader";
import { getAddonLink } from "@/lib/chargebee";
import { getProductLink } from "@/lib/hubspot";
import { PlanDTO } from "@/types";

const { Text } = Typography;

type PlanDetailsHeaderProps = {
  plan: PlanDTO;
  onArchivePlan: () => void;
  onActivatePlan: () => void;
  onDuplicatePlan: () => void;
  onOpenEditModal: () => void;
};

export default function PlanDetailsHeader({
  plan,
  onOpenEditModal,
  onArchivePlan,
  onActivatePlan,
  onDuplicatePlan,
}: PlanDetailsHeaderProps) {
  return (
    <PageHeader
      title="Plan Details"
      actions={
        <Space direction="vertical">
          <Button
            icon={<EditOutlined />}
            onClick={onOpenEditModal}
            type="primary"
          >
            Edit Plan
          </Button>
          <Button onClick={onDuplicatePlan} icon={<CopyOutlined />}>
            <Text>Duplicate plan</Text>
          </Button>
          {plan?.status === "active" ? (
            <Button onClick={onArchivePlan} icon={<BorderBottomOutlined />}>
              <Text>Switch to Draft</Text>
            </Button>
          ) : (
            <Button onClick={onActivatePlan} icon={<BorderBottomOutlined />}>
              <Text>Activate</Text>
            </Button>
          )}
        </Space>
      }
    >
      <Descriptions title="" size="small" bordered column={2}>
        <Descriptions.Item label="Internal Name">
          <Text copyable>{plan.internalName}</Text>
        </Descriptions.Item>
        <Descriptions.Item label="Chargebee Addon">
          <LinkChargebee url={getAddonLink(plan.chargebeeId || "")}>
            {plan.chargebeeId}
          </LinkChargebee>
        </Descriptions.Item>
        <Descriptions.Item label="CRM ID">
          {plan?.crmId ? (
            <Link
              target="_blank"
              passHref={true}
              href={getProductLink(plan?.crmSku)}
            >
              <Image
                src="/images/logo/hubspot.svg"
                preview={false}
                width={28}
                height={18}
              />
              <Text style={{ fontSize: 12, color: "darkgray" }}>
                {plan?.crmId}
              </Text>
            </Link>
          ) : (
            <Text style={{ fontSize: 12, color: "darkgray" }}>Unavailable</Text>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="Status">
          <Tag color={plan.status === "active" ? "green" : "orange"}>
            {plan.status === "active" ? "Active" : "Draft"}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Last modified">
          {plan.updatedAt}
        </Descriptions.Item>
      </Descriptions>
    </PageHeader>
  );
}
