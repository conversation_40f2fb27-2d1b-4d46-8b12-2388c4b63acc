import { searchItemPrices, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

import { SearchItemPricesParams } from "@/types";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the POST request to change the default payment method of a subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function POST(req: any) {
  try {
    const body = (await req.json()) as SearchItemPricesParams;
    const response = await searchItemPrices(body);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_SEARCH_ITEM_PRICES", details: error },
      { status: 400 }
    );
  }
}
