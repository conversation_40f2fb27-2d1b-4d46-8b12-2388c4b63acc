import { initChargebee, getSubscriptionComments } from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the GET request for retrieving subscription's comments.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function GET(req: any) {
  try {
    const urlPart = req.url.split("subscriptions/");
    const id = urlPart[1].split("/comments")[0];
    const comments = await getSubscriptionComments(id);

    return NextResponse.json(comments);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RETRIEVE_SUBSCRIPTION_COMMENTS", details: error },
      { status: 400 }
    );
  }
}
