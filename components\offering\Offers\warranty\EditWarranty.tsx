"use client";

import {
  BoldOutlined,
  ItalicOutlined,
  LinkOutlined,
  DeleteOutlined,
  EditOutlined,
  UnderlineOutlined,
} from "@ant-design/icons";
import { useEditor, EditorContent, BubbleMenu } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { App, Button, Input, Select, Typography } from "antd";
import Underline from "@tiptap/extension-underline";
import Link from "@tiptap/extension-link";
import { useEffect, useState } from "react";
import CharacterCount from "@tiptap/extension-character-count";
import * as allIcons from "@ant-design/icons";
import Icon from "@ant-design/icons/lib/components/Icon";

import { deleteWarranty, patchWarranty } from "@/lib/services";
import { Warranty } from "@/types";

const { Text } = Typography;

type WarrantyProps = {
  initialValues: Warranty;
  updateWarranties?: Function;
  warrantyCount?: number;
};

const UpdateWarranty = ({
  initialValues,
  updateWarranties,
  warrantyCount,
}: WarrantyProps) => {
  const { message } = App.useApp();
  const [currentLink, setCurrentLink] = useState("");
  const [currentIcon, setCurrentIcon] = useState<string | undefined>(
    initialValues.icon
  );
  const [icons, setIcons] = useState<(string | undefined)[]>();
  const [isEditable, setIsEditable] = useState<boolean>(false);
  const [isSaving, setSaving] = useState<boolean>(false);

  var showdown = require("showdown");
  var converter = new showdown.Converter();

  const editor = useEditor({
    editable: isEditable,
    extensions: [
      Underline,
      Link.configure({
        openOnClick: false,
      }),
      CharacterCount.configure({
        limit: 70,
      }),
      StarterKit,
    ],
    content: converter.makeHtml(initialValues.message),
  });

  useEffect(() => {
    setIcons(
      Object.keys(allIcons)
        .filter((name: string) => {
          return name.endsWith("Outlined");
        })
        .map((name: string) => {
          if (
            // @ts-ignore
            allIcons[name] &&
            // @ts-ignore
            typeof allIcons[name] === "object" &&
            // @ts-ignore
            allIcons[name].$$typeof
          ) {
            // @ts-ignore
            return name;
          }
        })
    );
  }, []);

  useEffect(() => {
    if (editor && editor?.getAttributes("link")?.href) {
      setCurrentLink(editor.getAttributes("link").href);
    } else {
      setCurrentLink("");
    }
  }, [editor && editor.getAttributes("link").href]);

  const handleLinkChange = (val: any) => {
    setCurrentLink(val);
    if (val && val !== "") {
      editor &&
        editor.chain().extendMarkRange("link").setLink({ href: val }).run();
    } else {
      editor && editor.chain().extendMarkRange("link").unsetLink().run();
    }
  };

  const handleIconChanged = (val: any) => {
    setCurrentIcon(val);
  };

  const handleEditClicked = async () => {
    if (!isEditable) {
      setIsEditable(true);
      editor?.setEditable(true);
      editor?.chain().focus().run();
    } else {
      let warranty: Warranty = {
        message: converter
          .makeMarkdown(editor?.getHTML())
          .replace(/(\r\n|\n|\r)/gm, ""),
        icon: currentIcon,
      };
      if (initialValues.id) {
        setSaving(true);
        let res = await patchWarranty({
          ...warranty,
          id: initialValues.id,
        });
        if (res.itemUpdated) {
          setSaving(false);
          message.open({
            type: "success",
            content: "Warranty updated!",
          });
          updateWarranties && updateWarranties();
          setIsEditable(false);
          editor?.setEditable(false);
        }
      }
    }
  };

  const handleDeleteWarranty = async () => {
    if (warrantyCount && warrantyCount <= 1) {
      message.open({
        type: "error",
        content: "At least one warranty is required",
      });
      return;
    }
    if (initialValues.id) {
      let res = await deleteWarranty(initialValues.id);
      if (res.affected && res.affected === 1) {
        message.open({
          type: "success",
          content: "Warranty deleted!",
        });
        updateWarranties && updateWarranties();
      }
    }
  };

  return (
    <>
      <div>
        {editor && (
          <BubbleMenu editor={editor} tippyOptions={{ duration: 100 }}>
            <Button
              icon={<BoldOutlined />}
              onClick={() => editor.chain().focus().toggleBold().run()}
              type={editor.isActive("bold") ? "primary" : "default"}
            />
            <Button
              icon={<ItalicOutlined />}
              onClick={() => editor.chain().focus().toggleItalic().run()}
              type={editor.isActive("italic") ? "primary" : "default"}
            />
            <Button
              icon={<UnderlineOutlined />}
              onClick={() => {
                editor.commands.focus();
                editor.commands.toggleUnderline();
              }}
              type={editor.isActive("underline") ? "primary" : "default"}
            />
            <Input
              value={currentLink}
              prefix={<LinkOutlined />}
              style={{ width: "150px" }}
              onChange={(e) => {
                handleLinkChange(e.target.value);
              }}
            />
          </BubbleMenu>
        )}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Select
            style={{ width: 80, height: 40 }}
            onChange={handleIconChanged}
            value={currentIcon}
            className="paradox-warranty-select"
            showSearch
            disabled={!isEditable}
            options={
              icons &&
              icons.map((item, index) => {
                return {
                  value: item,
                  label: (
                    <Icon
                      // @ts-ignore
                      component={allIcons[item]}
                      style={{ fontSize: 20 }}
                    />
                  ),
                };
              })
            }
          ></Select>
          <div style={{ width: "100%" }}>
            <EditorContent
              editor={editor}
              className="paradox-richtext-editor-input"
            />
          </div>
          <Button
            type="link"
            icon={isEditable ? <allIcons.SaveFilled /> : <EditOutlined />}
            onClick={handleEditClicked}
            style={{ color: isEditable ? "green" : "" }}
            loading={isSaving}
          >
            {isEditable ? "Save" : ""}
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={handleDeleteWarranty}
          />
        </div>
        {isEditable && (
          <Text style={{ fontSize: 13, color: "darkgray" }}>
            You are limited to to 70 characters
          </Text>
        )}
      </div>
    </>
  );
};

export default UpdateWarranty;
