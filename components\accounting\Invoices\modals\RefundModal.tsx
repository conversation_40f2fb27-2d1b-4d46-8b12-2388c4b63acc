"use client";

import { EditOutlined, InfoCircleTwoTone } from "@ant-design/icons";
import {
  App,
  Checkbox,
  Col,
  Form,
  InputNumber,
  Modal,
  Row,
  Select,
  Typography,
} from "antd";
import { useEffect } from "react";

import { formatCents } from "@/lib/currency";
import { markAsRefundedAndCancel } from "@/lib/services";
import { Invoice } from "@/types";
import { refundInvoice } from "@/lib/services/chargebee";

type RefundModalProps = {
  amountRefundable: number;
  isOpen: boolean;
  setIsOpen: Function;
  invoice: Invoice;
  refetchInvoice: Function;
  showMarkAsRefunded: boolean;
};

const { Text } = Typography;

const refundReasonOptions = [
  {
    value: "refund_during_retractation_period",
    label: "Refund during retractation period",
  },
  {
    value: "offer_error",
    label: "Offer error",
  },
  { value: "other", label: "Other" },
];

export const RefundModal = ({
  amountRefundable,
  isOpen,
  setIsOpen,
  invoice,
  refetchInvoice,
  showMarkAsRefunded,
}: RefundModalProps) => {
  const { message, modal } = App.useApp();
  const [refundForm] = Form.useForm();

  useEffect(() => {
    refundForm.setFieldValue(
      "amountToRefund",
      amountRefundable > 0 ? amountRefundable / 100 : 0
    );
  }, []);

  const refundCurrentInvoice = async (values: any) => {
    const refundReasonLabel = refundReasonOptions.find(
      (option) => option.value === values.refundReason
    )?.label as string;
    modal.warning({
      title: `Are you sure you want to refund ${formatCents(values.amountToRefund * 100)}?`,
      content: `By clicking proceed, the customer will be refunded the selected amount. This action cannot be undone.`,
      okText: "Proceed",
      okType: "primary",
      cancelText: "Close",
      okCancel: true,
      onCancel: () => {},
      onOk: async () => {
        // For the original refund.
        const res = await refundInvoice({
          id: String(invoice?.chargebeeId),
          reason: refundReasonLabel,
          amount: Math.round(values.amountToRefund * 100),
        });
        if (res?.error) {
          message.open({
            type: "error",
            content: "Cannot refund invoice!",
          });
        } else if (res.credit_note) {
          // Call to backend to mark as refunded.
          if (values.markRefund) {
            await markAsRefundedAndCancel(invoice?.subscriptionId as string);
          }
          message.open({
            type: "success",
            content: "Refund issued successfully",
          });
          setIsOpen(false);
          await refetchInvoice();
          refundForm.resetFields();
        }
      },
    });
  };

  return (
    <Modal
      open={isOpen}
      closable={false}
      onOk={() => {
        refundForm.submit();
      }}
      onCancel={() => {
        setIsOpen(false);
      }}
      okText="Proceed"
    >
      <Text style={{ fontSize: 15 }} strong>
        Issue a refund for invoice #{invoice.chargebeeId}
      </Text>
      <br />
      <Text style={{ fontSize: 13, color: "gray" }}>
        You can issue a complete refund or only a specific amount of the
        invoice. It will be automatically refunded to the customer&lsquo;s
        credit card
      </Text>
      <br />
      <br />
      <Row className="paradox-card">
        <Col span={1}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <InfoCircleTwoTone style={{ fontSize: 34 }} />
          </div>
        </Col>
        <Col span={22} offset={1}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "gray",
            }}
          >
            Issuing a refund will automaticaly create a Credit note and a
            transaction attached to this invoice/credit note.
          </div>
        </Col>
      </Row>
      <br />
      <Form layout="vertical" form={refundForm} onFinish={refundCurrentInvoice}>
        <Form.Item
          name="amountToRefund"
          label="Amount to refund"
          style={{ marginBottom: "5px" }}
          rules={[
            {
              required: true,
              message: "Please enter the amount to refund",
            },
          ]}
        >
          <InputNumber
            style={{ width: "250px" }}
            min={1}
            max={amountRefundable > 0 ? amountRefundable / 100 : 0}
            addonBefore="EUR"
            addonAfter={<EditOutlined />}
            placeholder="Amount to refund"
          />
        </Form.Item>
        <Text style={{ color: "gray", fontSize: 13 }}>
          Maximum refundable amount:{" "}
          {formatCents(amountRefundable > 0 ? amountRefundable : 0)}
        </Text>
        <br />
        <br />
        <Form.Item
          name="refundReason"
          label="Refund reason"
          rules={[
            {
              required: true,
              message: "Please select a refund reason",
            },
          ]}
        >
          <Select
            style={{ width: "250px" }}
            placeholder="Refund reason"
            options={refundReasonOptions}
          />
        </Form.Item>
        <Form.Item name="markRefund" valuePropName="checked">
          <Checkbox
            disabled={!showMarkAsRefunded}
            onChange={(e) => {
              refundForm.setFieldValue("markRefund", e.target.checked);
            }}
          >
            Change the order status to refund{" "}
            <span style={{ color: "red" }}>- danger zone</span>
          </Checkbox>
          <br />
          <Text style={{ color: "gray", fontSize: 13 }}>
            By clicking, we will stop the future invoices & set the subscription
            as refunded. It will also automatically update the deal in Hubspot
            (Closed lost & Refund status).{" "}
            <span style={{ color: "red" }}>
              This action can&apos;t be reverted.
            </span>
          </Text>
        </Form.Item>
      </Form>
    </Modal>
  );
};
