import { NextResponse } from "next/server";

import { setRole } from "@/server/clerk";

export async function PATCH(req: Request) {
  try {
    const id = req.url.split("users/")[1];
    const body = await req.json();
    const response = await setRole(id, body.role);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_SET_USER_ROLE" },
      { status: 400 }
    );
  }
}
