import { NextResponse } from "next/server";

import { get<PERSON><PERSON><PERSON>, initChargebee } from "@/server/chargebee";

initChargebee();

/**
 * Handles the GET request for retrieving a customer.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved customers or an error message.
 */
export async function GET(req: Request) {
  try {
    const urlPart = req.url.split("customer/");
    const customerId = urlPart[1];
    const response = await getCustomer(customerId);
    return NextResponse.json(response);
  } catch (error) {
    console.error("error", error);
    return NextResponse.json(
      { error: "CANNOT_SEARCH_CUSTOMERS" },
      { status: 400 }
    );
  }
}
