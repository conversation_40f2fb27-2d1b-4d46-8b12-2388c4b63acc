"use client";

import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Input,
  Row,
  Select,
  Table,
  Typography,
  Form,
  Divider,
} from "antd";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import paginationConfig from "../../form/paginationConfig";
import { getTransactionsTableColumns } from "./TransactionsTableColumns";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchTransactions } from "@/hooks/accounting/transactions";
import { useProtectPage } from "@/hooks/roles";
import { Transaction, TransactionsFilters } from "@/types";

const { Title } = Typography;

const initialFilters = {
  searchQuery: "",
  statusFilter: "",
  paymentMethodFilter: "",
  subscriptionFilter: "",
};

const TransactionsTable = () => {
  const router = useRouter();
  const [filters, setFilters] = useState<TransactionsFilters>(initialFilters);
  const [searchQuery, setSearchQuery] = useState("");
  const [subscriptionFilter, setSubscriptionFilter] = useState("");

  const debouncedSearchTerm = useDebounce(searchQuery);
  const debouncedSubId = useDebounce(subscriptionFilter);

  useProtectPage("transactions-list");

  const [filterForm] = Form.useForm();

  const {
    data: transactions,
    isLoading,
    mutate: refetchTransactions,
  } = useFetchTransactions({
    filters: {
      ...filters,
      searchQuery: debouncedSearchTerm,
      subscriptionFilter: debouncedSubId,
    },
  });

  const columns = getTransactionsTableColumns({
    onInvoiceClick: (invoiceId: string) => {
      router.push(`/dashboard/invoices/${invoiceId}`);
    },
    onRefundClick: (refundId: string) => {
      router.push(`/dashboard/transactions/${refundId}`);
    },
    onSubscriptionClick: (subscriptionId: string) => {
      router.push(`/dashboard/subscriptions/${subscriptionId}`);
    },
  });

  const filterTransactions = async (changedValues: any) => {
    if (changedValues?.searchQuery || changedValues?.searchQuery === "") {
      setSearchQuery(changedValues.searchQuery);
    } else if (
      changedValues?.subscriptionFilter ||
      changedValues?.subscriptionFilter === ""
    ) {
      setSubscriptionFilter(changedValues.subscriptionFilter);
    } else {
      setFilters(filterForm.getFieldsValue());
    }
  };

  const resetFilters = () => {
    setFilters(initialFilters);
    setSearchQuery("");
    setSubscriptionFilter("");
  };

  useEffect(() => {
    refetchTransactions();
  }, [debouncedSearchTerm, debouncedSubId, filters.statusFilter]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Transactions</Title>
      <Row style={{ lineHeight: "10px" }}>
        <Col span={24}>
          <Form form={filterForm} onValuesChange={filterTransactions}>
            <Form.Item name="searchQuery">
              <Input
                prefix={<SearchOutlined />}
                placeholder="Search by customer email"
              />
            </Form.Item>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <Form.Item name="subscriptionFilter" style={{ width: "25%" }}>
                <Input allowClear placeholder="Subscription id" />
              </Form.Item>
              <Form.Item name="statusFilter" style={{ width: "25%" }}>
                <Select
                  allowClear
                  placeholder="Status"
                  options={[
                    { label: "In Progress", value: "in_progress" },
                    { label: "Success", value: "success" },
                    { label: "Voided", value: "voided" },
                    { label: "Failure", value: "failure" },
                    { label: "Timeout", value: "timeout" },
                    { label: "Needs Attention", value: "needs_attention" },
                  ]}
                />
              </Form.Item>
              <Form.Item name="paymentMethodFilter" style={{ width: "25%" }}>
                <Select
                  allowClear
                  placeholder="Payment Method"
                  options={[
                    { label: "Card", value: "card" },
                    { label: "Check", value: "check" },
                    { label: "Chargeback", value: "chargeback" },
                    { label: "Bank Transfer", value: "bank_transfer" },
                    { label: "Cash", value: "cash" },
                    { label: "Other", value: "other" },
                  ]}
                />
              </Form.Item>
              <Button
                type="link"
                onClick={() => {
                  resetFilters();
                  filterForm.resetFields();
                }}
              >
                Clear Filters
              </Button>
            </div>
          </Form>
        </Col>
      </Row>
      <Divider />
      <Table
        dataSource={transactions?.items}
        loading={isLoading}
        columns={columns}
        scroll={{ x: 2000 }}
        size="small"
        pagination={paginationConfig}
        rowClassName={() => {
          return "paradox-table-row";
        }}
        onRow={(data: Transaction) => {
          return {
            onClick: () => {
              router.push(`/dashboard/transactions/${data.chargebeeId}`);
            },
          };
        }}
        rowKey="id"
      />
    </div>
  );
};

export default TransactionsTable;
