"use client";

import { Transaction } from "chargebee";
import useSWR, { SWRConfiguration } from "swr";

import { getInvoiceParadoxApi, getInvoicesParadoxApi } from "@/lib/services";
import { getInvoicePayments } from "@/lib/services/chargebee";
import {
  GetAllInvoicesParams,
  Invoice,
  InvoiceListResponse,
  InvoicesFilters,
} from "@/types";
import { convertDateToUnixInterval } from "@/lib/date";

type UseFetchInvoicesParams = {
  options?: SWRConfiguration;
  filters?: InvoicesFilters;
};

function prepareFetchInvoices(filters?: InvoicesFilters) {
  let options: GetAllInvoicesParams = {};
  if (filters) {
    if (filters.searchQuery)
      options.searchQuery = filters.searchQuery as string;
    if (filters.statusFilter) options.status = filters.statusFilter as string;
    if (filters.subscriptionFilter)
      options.subscriptionId = filters.subscriptionFilter as string;
    if (filters.fiscalEntityFilter)
      options.billinEntity = filters.fiscalEntityFilter as string;
    if (filters.paidAtFilter) {
      const { start, end } = convertDateToUnixInterval(filters.paidAtFilter);
      options.paidAtStart = start;
      options.paidAtEnd = end;
    }
  }
  return options;
}

export const useFetchInvoices = (params?: UseFetchInvoicesParams) => {
  const options: GetAllInvoicesParams = prepareFetchInvoices(params?.filters);
  return useSWR<InvoiceListResponse>(
    `useFetchInvoices-${JSON.stringify(options)}`,
    () => getInvoicesParadoxApi(options),
    params?.options
  );
};

export const useFetchInvoice = (id: string) => {
  return useSWR<Invoice>(`useFetchInvoice-${id}`, () =>
    getInvoiceParadoxApi(id)
  );
};

export const useFetchInvoicePayments = (
  id: string,
  options?: SWRConfiguration
) => {
  return useSWR<Transaction.PaymentsForInvoiceResponse>(
    id ? `invoice-payments-${id}` : null,
    () => getInvoicePayments(id),
    options
  );
};
