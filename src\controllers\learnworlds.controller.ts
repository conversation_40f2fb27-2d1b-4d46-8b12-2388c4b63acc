import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtPermissionsGuard } from 'src/auth/guards';
import { LearnworldsUseCases } from '@useCases';

@ApiTags('learnworlds')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('learnworlds')
export class LearnworldsController {
  constructor(private readonly learnworldsUseCases: LearnworldsUseCases) {}

  @ApiOperation({ summary: 'Get all courses from Learnworlds' })
  @ApiQuery({
    name: 'forceRefresh',
    required: false,
    type: Boolean,
    description: 'Whether to bypass cache and fetch fresh data'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Specific page to fetch (if not provided, fetches all pages)'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of courses per page'
  })
  @Get('/courses')
  async getCourses(
    @Query('forceRefresh') forceRefresh?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.learnworldsUseCases.getCourses(
      forceRefresh || false,
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined,
    );
  }

}

