import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiBearerAuth, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { JwtPermissionsGuard } from 'src/auth/guards';
import { LearnworldsUseCases } from '@useCases';
import { LearnworldsCoursesListResponseDto } from 'src/core/dtos/learnworlds.dto';

@ApiTags('learnworlds')
@UseGuards(JwtPermissionsGuard)
@ApiBearerAuth()
@Controller('learnworlds')
export class LearnworldsController {
  constructor(private readonly learnworldsUseCases: LearnworldsUseCases) { }

  @ApiOperation({ summary: 'Get all courses from Learnworlds' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved courses from Learnworlds',
    type: LearnworldsCoursesListResponseDto,
  })
  @ApiQuery({
    name: 'forceRefresh',
    required: false,
    type: Boolean,
    description: 'Whether to bypass cache and fetch fresh data'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Specific page to fetch (if not provided, fetches all pages)'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of courses per page'
  })
  @Get('/courses')
  async getCourses(
    @Query('forceRefresh') forceRefresh?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.learnworldsUseCases.getCourses({
      forceRefresh: forceRefresh || false,
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
    });
  }

}

