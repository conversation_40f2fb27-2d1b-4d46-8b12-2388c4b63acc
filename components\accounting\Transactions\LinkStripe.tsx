"use client";

import { Image, Typography } from "antd";
import Link from "next/link";

const { Text } = Typography;

type LinkStripeProps = {
  idAtGateway: string;
  copyable?: boolean;
};

export const LinkStripe = ({
  idAtGateway,
  copyable = true,
}: LinkStripeProps) => {
  return (
    <Link
      href={`https://dashboard.stripe.com/payments/${idAtGateway}`}
      passHref={true}
      target="_blank"
    >
      <Image
        src="/images/logo/stripe.png"
        alt="Stripe"
        width={16}
        height={16}
        preview={false}
      />
      &nbsp;
      {copyable ? (
        <Text
          copyable
        >{`https://dashboard.stripe.com/payments/${idAtGateway}`}</Text>
      ) : null}
    </Link>
  );
};
