"use client";

import "react-phone-number-input/style.css";
import PhoneInput from "react-phone-number-input";
import { useState } from "react";

type Tagged<A, T> = A & { __tag?: T };

type Phone = Tagged<string, "E164Number">;

type InputPhoneProps = {
  disabled?: boolean;
  value: Phone;
  onChange?: (value: Phone | undefined) => void;
};

export default function InputPhone({
  disabled = false,
  value,
  onChange,
}: InputPhoneProps) {
  const [data, setData] = useState<Phone>();

  const handleChange = (value: Phone | undefined) => {
    setData(value);
    onChange && onChange(value);
  };

  return (
    <PhoneInput
      placeholder={`Enter phone number`}
      value={value}
      onChange={handleChange}
      disabled={disabled}
      defaultCountry="FR"
      containerComponentProps={{
        style: {
          height: 40,
          lineHeight: 30,
        },
      }}
      numberInputProps={{
        disabled: false,
        style: {
          width: "50%",
          height: 40,
          padding: "5px 11px",
          fontSize: 16,
          borderRadius: 8,
          borderStyle: "solid",
          borderColor: "#d9d9d9",
          color: "rgba(0, 0, 0, 0.88)",
        },
      }}
    />
  );
}
