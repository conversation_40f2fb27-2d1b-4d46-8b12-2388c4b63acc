import { User, create<PERSON>lerkClient } from "@clerk/backend";

import { requestClerk } from ".";
import { UpdateUserRoleParams, UsersFilters } from "@/types";

// const clerkClient = createClerkClient({
//   secretKey: process.env.CLERK_SECRET_KEY,
// });

const DEFAULT_LIMIT = 100;

function mapUser(user: User) {
  return {
    id: user.id,
    imageUrl: user.imageUrl,
    email: user.emailAddresses[0].emailAddress,
    firstName: user.firstName,
    lastName: user.lastName,
    role: user.publicMetadata.role,
    lastSignInAt: user.lastSignInAt,
    createdAt: user.createdAt,
  };
}

/**
 * Retrieves all users based on the provided filters.
 *
 * @param params - The filters to apply when retrieving users.
 * @returns A promise that resolves to an array of User objects.
 */
export async function getAllUsers(params: UsersFilters | undefined) {
  const query = new URLSearchParams();

  // Remove '$' from search query to prevent injection
  if (params?.searchQuery) {
    const searchTerm = params.searchQuery.replace(/\$/g, "");
    query.append("searchQuery", searchTerm);
  }

  query.append("limit", DEFAULT_LIMIT.toString());

  try {
    const response = await requestClerk(
      `/api/users?${query.toString()}`,
      "GET"
    );
    return {
      ...response,
      data: response.data.map(mapUser),
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
}

/**
 * Updates the role of a user.
 *
 * @param params - The parameters for updating the user role.
 * NOTE: Only available to admins.
 * @returns A promise that resolves to the updated user.
 */
export async function updateUserRole(params: UpdateUserRoleParams) {
  return requestClerk(`/api/users/${params.user_id}`, "PATCH", {
    role: params.role,
  });
}
