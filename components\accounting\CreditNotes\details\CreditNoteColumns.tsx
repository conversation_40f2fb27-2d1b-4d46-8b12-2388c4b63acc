"use client";

import { formatCents } from "@/lib/currency";
import { CreditNote } from "@/types";
import { Col, Image, Row, Typography } from "antd";

const { Text } = Typography;

export const getCreditNoteColumns = (creditNote: CreditNote | undefined) => [
  {
    title: "Product",
    key: "id",
    width: "250",
    render: (record: any) => (
      <Row>
        <Col span={8}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Image
              src={record.image}
              style={{ width: "64px", borderRadius: "5px" }}
              alt=""
            />
          </div>
        </Col>
        <Col span={15} offset={1}>
          <div
            style={{
              display: "flex",
              alignItems: "start",
              justifyContent: "start",
              flexDirection: "column",
            }}
          >
            <Text style={{ color: "gray" }}>{record?.name}</Text>
            <Text
              style={{
                color: "gray",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                width: "200px",
              }}
              ellipsis={{ tooltip: record?.description }}
            >
              {record?.description}
            </Text>
          </div>
        </Col>
      </Row>
    ),
  },
  {
    title: "Unit price (Excl. VAT)",
    key: "id",
    render: (record: any) => {
      let unitAmountTaxExcluded = 0;
      let quantity = record.quantity;
      if (creditNote?.priceType === "tax_inclusive") {
        unitAmountTaxExcluded = record.amount / (1 + record.tax_rate / 100);
      } else {
        unitAmountTaxExcluded = record.amount;
      }
      return (
        <div>
          <Text>{`${formatCents(unitAmountTaxExcluded)} 
            x ${quantity}`}</Text>
        </div>
      );
    },
  },
  {
    title: "Discount (Excl. VAT)",
    key: "id",
    render: (record: any) => {
      let discountAmountTaxExcluded = 0;
      if (creditNote?.priceType === "tax_inclusive") {
        discountAmountTaxExcluded =
          record.discount_amount / (1 + record.tax_rate / 100);
      } else {
        discountAmountTaxExcluded = record.discount_amount;
      }
      return (
        <div>
          <Text>{`-
            ${formatCents(discountAmountTaxExcluded)}`}</Text>
        </div>
      );
    },
  },
  {
    title: "Total Incl. VAT",
    key: "id",
    render: (record: any) => {
      let discountAmountTaxExcluded = 0;
      if (record.price_type === "tax_inclusive") {
        discountAmountTaxExcluded =
          record.discount_amount / (1 + record.tax_rate / 100);
      } else {
        discountAmountTaxExcluded = record.discount_amount;
      }
      let unitAmountTaxExcluded = 0;
      if (creditNote?.priceType === "tax_inclusive") {
        unitAmountTaxExcluded = record.amount / (1 + record.tax_rate / 100);
      } else {
        unitAmountTaxExcluded = record.amount;
      }
      return (
        <div>
          <Text>{`
            ${formatCents(unitAmountTaxExcluded - discountAmountTaxExcluded)}`}</Text>
        </div>
      );
    },
  },
  {
    title: "VAT",
    dataIndex: "tax_amount",
    render: (record: any) => <span>{`${formatCents(record)}`}</span>,
    key: "id",
  },
  {
    title: "Amount (EUR)",
    key: "id",
    render: (record: any) => {
      let discountAmountTaxExcluded = 0;
      if (creditNote?.priceType === "tax_inclusive") {
        discountAmountTaxExcluded =
          record.discount_amount / (1 + record.tax_rate / 100);
      } else {
        discountAmountTaxExcluded = record.discount_amount;
      }
      let unitAmountTaxExcluded = 0;
      if (creditNote?.priceType === "tax_inclusive") {
        unitAmountTaxExcluded = record.amount / (1 + record.tax_rate / 100);
      } else {
        unitAmountTaxExcluded = record.amount;
      }
      const totalExclVat = unitAmountTaxExcluded - discountAmountTaxExcluded;
      return (
        <div>
          <Text>{`${formatCents(totalExclVat + record.tax_amount)}`}</Text>
        </div>
      );
    },
  },
];
