import { getChargebeeSubscription, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the GET request for retrieving subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function GET(req: any) {
  try {
    const id = req.url.split("subscriptions/")[1];
    const response = await getChargebeeSubscription(id);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RETRIEVE_SUBSCRIPTION", details: error },
      { status: 400 }
    );
  }
}
