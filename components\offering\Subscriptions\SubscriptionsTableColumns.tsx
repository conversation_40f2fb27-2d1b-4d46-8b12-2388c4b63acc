"use client";

import {
  CalendarOutlined,
  DollarOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import { Image, Tag, Tooltip, Typography } from "antd";

import { calculateTagBgColorWithStatus } from "./utils/styles";
import { capitalizeWord } from "@/lib/text";
import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { getDealLink } from "@/lib/hubspot";
import { PX_Subscription } from "@/types";

const { Text, Link } = Typography;

type SubscriptionsTableColumnsProps = {
  onCustomerClick: (customerId: string) => void;
};

export const getSubscriptionsTableColumns = ({
  onCustomerClick,
}: SubscriptionsTableColumnsProps) => [
  {
    title: "Subscription ID",
    dataIndex: "chargebeeId",
    key: "id",
    render(val: string, record: any) {
      return (
        <>
          {record.chargebeeId.indexOf("PX_DBG") === 0 ? (
            // Tootltip explaining this is a debug subscription
            <Tooltip title="This is a debug subscription">
              <Tag color="orange">
                <RobotOutlined />
              </Tag>
            </Tooltip>
          ) : null}
          <Link
            copyable
            onClick={(e) => {
              e.stopPropagation();
              window.open(
                `/dashboard/subscriptions/${record.chargebeeId}`,
                "_blank"
              );
            }}
          >
            {val}
          </Link>
          {record.crmId ? (
            <Link
              onClick={(e) => {
                e.stopPropagation();
                window.open(getDealLink(record?.crmId), "_blank");
              }}
            >
              <Image
                src="/images/logo/hubspot.svg"
                preview={false}
                width={28}
                height={18}
              />
            </Link>
          ) : null}
        </>
      );
    },
  },
  {
    title: "Status",
    dataIndex: "orderStatus",
    key: "orderStatus",
    render: (val: string) => {
      return (
        <Tag
          color={calculateTagBgColorWithStatus(
            val as PX_Subscription["orderStatus"]
          )}
        >
          {capitalizeWord(val).split("_").join(" ")}
        </Tag>
      );
    },
  },
  {
    title: "Customer",
    dataIndex: "customerEmail",
    key: "customerEmail",
    render: (val: string, record: any) => {
      return (
        <Text
          keyboard
          copyable
          style={{ cursor: "pointer" }}
          onClick={(e) => {
            e.stopPropagation();
            onCustomerClick(record.customerId);
          }}
        >
          {val}
        </Text>
      );
    },
  },
  {
    title: "Customer Name",
    dataIndex: "customerName",
    key: "id",
  },
  {
    title: "Offer name",
    dataIndex: "orderName",
    render: (_: string, record: any) => {
      return record.customFields.cf_os_offer_id ? (
        <Link
          onClick={(e) => {
            e.stopPropagation();
            window.open(
              "/dashboard/offers/" + record.customFields.cf_os_offer_id,
              "_blank"
            );
          }}
        >
          {record?.metadata?.offerName ?? record.customFields.cf_os_offer_id}
        </Link>
      ) : (
        <Text>{record?.metadata?.offerName ?? "?"}</Text>
      );
    },
  },
  {
    title: "Remaining billing cycles",
    dataIndex: "remainingBillingCycles",
    key: "id",
  },
  {
    title: (
      <div>
        <DollarOutlined />
        &nbsp;MRR
      </div>
    ),
    dataIndex: "mrr",
    key: "mrr",
    render: (val: number) => {
      return formatCents(val);
    },
  },
  {
    title: (
      <div>
        <CalendarOutlined />
        &nbsp;Next Billing At
      </div>
    ),
    dataIndex: "nextBillingAt",
    key: "id",
    render: (val: number) => {
      if (!val) return "N/A";
      return formatDateFromUnix(val);
    },
  },
  {
    title: (
      <div>
        <CalendarOutlined />
        &nbsp;Created At
      </div>
    ),
    dataIndex: "createdAt",
    key: "id",
    render: (val: number) => {
      return formatDateFromUnix(val);
    },
  },
];
