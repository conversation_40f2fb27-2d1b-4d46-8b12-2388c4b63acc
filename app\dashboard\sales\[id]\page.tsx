export const dynamic = "force-dynamic";

import { Metadata } from "next";

import SubscriptionDetails from "@/components/offering/Subscriptions/SubscriptionDetails";

export const metadata: Metadata = {
  title: "Sale details",
};

export default async function SalesDetailsPage(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = await props.params;
  const { id } = params;
  return <SubscriptionDetails id={id} />;
}
