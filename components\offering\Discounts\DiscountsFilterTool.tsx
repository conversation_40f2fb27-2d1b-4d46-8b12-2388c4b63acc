"use client";

import {
  PlusOutlined,
  SearchOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";
import { Button, Col, Input, Row, Select } from "antd";

import { initialFilter } from "./DiscountsTable";
import { DiscountsFilter, ProductLine } from "@/types";
import { useFetchProducts } from "@/hooks/product-catalog/products";
import { useFetchProductLines } from "@/hooks/product-catalog/productLines";

type DiscountsFilterParams = {
  filters: DiscountsFilter;
  setFilters: Function;
  onCreateDiscount: Function;
  onCreateQuickDiscount: Function;
};

export default function DiscountsFilterTool({
  filters,
  setFilters,
  onCreateDiscount,
  onCreateQuickDiscount,
}: DiscountsFilterParams) {
  const { data: products } = useFetchProducts({
    filters: {
      page: 1,
      productFamily: filters.productFamily as string,
      productLine: filters.productLine as string,
    },
  });
  const { data: productLines } = useFetchProductLines({
    filters: {
      status: "active",
      search: "",
    },
  });

  const handleFamilyFilterChange = async (value: any) => {
    if (value) {
      setFilters((prev: DiscountsFilter) => ({
        ...prev,
        productFamily: value,
        product: null,
        productPlans: null,
      }));
    } else {
      setFilters((prev: DiscountsFilter) => ({
        ...prev,
        productFamily: null,
        product: null,
        productPlans: null,
      }));
    }
  };

  const handleProductFilterChange = (value: any) => {
    setFilters((prev: DiscountsFilter) => ({
      ...prev,
      product: value,
      productPlans: products?.items
        ?.find((x: any) => x.id === value)
        ?.plans?.filter((val: any, index: any) => {
          return val.status === "active";
        }),
    }));
  };

  const handleProductLineChange = (value: number) => {
    setFilters((prev: DiscountsFilter) => ({
      ...prev,
      productLine: value,
      productFamily: null,
      product: null,
      productPlan: null,
      productFamilies: null,
      productPlans: null,
    }));

    if (value) {
      setFilters((prev: DiscountsFilter) => ({
        ...prev,
        productFamily: null,
        productFamilies: productLines?.items
          ?.find((x: ProductLine) => x.id === value && x.status === "active")
          ?.families?.filter((val: any) => {
            return val.status === "active";
          }),
      }));
    } else {
      setFilters((prev: DiscountsFilter) => ({
        ...initialFilter,
      }));
    }
  };

  return (
    <Row style={{ lineHeight: "10px" }}>
      <Col xs={12} sm={12} md={16} lg={16} xl={17} xxl={19}>
        <Input
          prefix={<SearchOutlined />}
          placeholder="Search in Discounts"
          onChange={(event) =>
            setFilters((prev: DiscountsFilter) => ({
              ...prev,
              name: event.target.value,
            }))
          }
        />

        <div
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            marginTop: 10,
          }}
        >
          <Select
            style={{ width: "20%", marginRight: 10 }}
            allowClear
            placeholder="Product line"
            onChange={handleProductLineChange}
            options={
              productLines?.items &&
              productLines?.items.map((item) => {
                return {
                  value: item.id,
                  label: <span>{item.name}</span>,
                };
              })
            }
            value={(filters.productLine && Number(filters.productLine)) || null}
          />

          <Select
            style={{ width: "20%", marginRight: 10 }}
            allowClear
            onChange={handleFamilyFilterChange}
            placeholder="Product family"
            disabled={!filters?.productFamilies}
            options={
              filters?.productFamilies &&
              filters?.productFamilies.map((item: any) => {
                return {
                  value: item.id,
                  label: <span>{item.name}</span>,
                };
              })
            }
            value={
              (filters.productFamily && Number(filters.productFamily)) || null
            }
          />

          <Select
            style={{ width: "20%", marginRight: 10 }}
            allowClear
            onChange={handleProductFilterChange}
            placeholder="Product"
            disabled={!products}
            options={
              products?.items &&
              products?.items.map((item) => {
                return {
                  value: item.id,
                  label: <span>{item.externalName}</span>,
                };
              })
            }
            value={(filters.product && Number(filters.product)) || null}
          />

          <Select
            style={{ width: "20%", marginRight: 10 }}
            allowClear
            placeholder="Plan"
            onChange={(value) =>
              setFilters((prev: DiscountsFilter) => ({
                ...prev,
                productPlan: value,
              }))
            }
            disabled={!filters.productPlans}
            options={
              filters.productPlans &&
              filters.productPlans.map((item) => {
                return {
                  value: item.id,
                  label: <span>{item.internalName}</span>,
                };
              })
            }
            value={(filters.productPlan && Number(filters.productPlan)) || null}
          />

          <Select
            style={{ width: "20%" }}
            allowClear
            onChange={(value) =>
              setFilters((prev: DiscountsFilter) => ({
                ...prev,
                status: value,
              }))
            }
            placeholder="Status"
            options={[
              { value: "active", label: <span>Active</span> },
              { value: "disabled", label: <span>Disabled</span> },
            ]}
            value={filters.status || null}
          />

          <Button
            type="link"
            onClick={() => {
              setFilters(initialFilter);
            }}
          >
            Clear Filters
          </Button>
        </div>
      </Col>
      <Col xs={11} sm={11} md={7} lg={7} xl={6} xxl={4} offset={1}>
        <Button
          style={{
            backgroundColor: "#7351EE",
            color: "white",
            width: "100%",
          }}
          icon={<PlusOutlined />}
          onClick={() => onCreateDiscount()}
        >
          Create a discount
        </Button>
        <Button
          style={{
            backgroundColor: "#7351EE",
            color: "white",
            width: "100%",
          }}
          icon={<ThunderboltOutlined />}
          onClick={() => onCreateQuickDiscount()}
        >
          Quick 10% Discount Code
        </Button>
      </Col>
    </Row>
  );
}
