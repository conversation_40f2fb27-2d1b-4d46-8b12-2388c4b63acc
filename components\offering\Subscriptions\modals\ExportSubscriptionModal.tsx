"use client";

import { DatePicker, Divider, Form, Modal } from "antd";
import dayjs from "dayjs";

import { convertDateToUnix } from "@/lib/date";
import { exportSubscriptions } from "@/lib/services";

type ExportSubscriptionModalProps = {
  isOpen: boolean;
  handleClose: (value: boolean) => void;
};

const ExportSubscriptionModal = ({
  isOpen,
  handleClose,
}: ExportSubscriptionModalProps) => {
  // form
  const [exportForm] = Form.useForm();

  const handleExportSubscription = async () => {
    const validationResult = await exportForm.validateFields();
    if (validationResult) {
      const values = exportForm.getFieldsValue();
      if (values?.exportDate) {
        const startDate = convertDateToUnix(values.exportDate[0].toISOString());
        const endDateValue = dayjs(values.exportDate[1]).endOf("day");
        const endDate = convertDateToUnix(endDateValue.toISOString());
        const res: any = await exportSubscriptions({
          startDate,
          endDate,
        });
        // base64 to blob
        const blob = await fetch(res).then((res) => res.blob());
        // simulate user link click to download the file
        const csvURL = window.URL.createObjectURL(blob);
        let tempLink = document.createElement("a");
        tempLink.href = csvURL;
        tempLink.setAttribute(
          "download",
          `PX_OS_Subscriptions_${new Date().toISOString()}.csv`
        );
        tempLink.click();
      }
    }
  };

  const disabledDates: any = (current: any) => {
    // Can not select days after today
    return current > dayjs().endOf("day");
  };

  return (
    <Modal
      open={isOpen}
      title="Export subscriptions"
      onCancel={() => handleClose(false)}
      onOk={handleExportSubscription}
      okText="Export"
    >
      <Divider />
      <Form form={exportForm}>
        <Form.Item
          rules={[{ required: true }]}
          name="exportDate"
          label="Select Date Range"
        >
          <DatePicker.RangePicker disabledDate={disabledDates} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ExportSubscriptionModal;
