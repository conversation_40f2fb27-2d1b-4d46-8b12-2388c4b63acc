"use client";

import { Descriptions, Divider, Image, Input, Select, Typography } from "antd";
import { useMemo, type JSX } from "react";
import countryList from "react-select-country-list";

import { getTaxProfileName } from "@/lib/taxProfiles";
import { Product, ProductLine } from "@/types";

const { Text } = Typography;

export const ProductSettings = (params: {
  product?: Product;
  line: ProductLine | undefined;
}): JSX.Element => {
  const countryDropdownOptions = useMemo(() => countryList().getData(), []);

  const selectedCommunities = useMemo(() => {
    if (
      !params?.product ||
      !params?.product?.communityIds ||
      params?.product.communityIds.length === 0
    ) {
      return [];
    }
    let communityOpts = params?.product?.communityIds?.reduce(
      (acc: any, curr: any) => {
        return acc.concat(curr.community);
      },
      []
    );
    return communityOpts.map((comm: any) => ({
      label: comm,
      value: comm,
    }));
  }, [params?.product]);

  const selectedSpaces = useMemo(() => {
    if (
      !params?.product ||
      !params?.product?.communityIds ||
      params?.product.communityIds.length === 0
    ) {
      return [];
    }
    let spaceOpts = params?.product?.communityIds?.reduce(
      (acc: any, curr: any) => {
        return acc.concat(curr.spaces);
      },
      []
    );
    return spaceOpts.map((space: any) => ({
      label: space.name,
      value: space.id,
    }));
  }, [params?.product]);

  const selectedCourses = useMemo(() => {
    if (
      !params?.product ||
      !params?.product?.lmsIds ||
      params?.product.lmsIds.length === 0
    ) {
      return [];
    }
    let courseOpts = params?.product?.lmsIds?.reduce((acc: any, curr: any) => {
      return acc.concat(curr);
    }, []);
    return courseOpts.map((course: any) => ({
      label: course.name,
      value: course.id,
    }));
  }, [params?.product]);

  if (!params.product) {
    return <div>Product not found</div>;
  }

  return (
    <>
      <Descriptions
        title="Product Info"
        style={{ width: "100%" }}
        column={2}
        bordered
      >
        <Descriptions.Item
          label={
            <>
              <span>Internal name</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Name that helps you identify this product internally.
              </small>
            </>
          }
        >
          <Text copyable>{params.product?.internalName}</Text>
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>External name</span>
              <br />
              <small style={{ color: "darkgray" }}>
                This will be used in invoices, Checkout & Self-Serve Portal.
              </small>
            </>
          }
        >
          {params.product?.description}
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Product code</span>
              <br />
              <small style={{ color: "darkgray" }}>
                This is the unique product code identifier for this product.
              </small>
            </>
          }
        >
          <Text copyable>{params.product?.code}</Text>
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Status</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Switching a product to Draft will disable all the plans &
                offers.
              </small>
            </>
          }
        >
          {params.product?.status}
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Is Forever Product?</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Wether this products if for forever subscriptions or not.
              </small>
            </>
          }
        >
          {params.product?.isForever ? "Yes" : "No"}
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Is Event?</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Wether this product is an event or not.
              </small>
            </>
          }
        >
          {params.product?.isEvent ? "Yes" : "No"}
        </Descriptions.Item>
        {params.product?.isEvent && (
          <>
            <Descriptions.Item
              label={
                <>
                  <span>Event year</span>
                  <br />
                  <small style={{ color: "darkgray" }}>
                    The year of the event.
                  </small>
                </>
              }
            >
              {params.product?.eventYear}
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Event location</span>
                  <br />
                  <small style={{ color: "darkgray" }}>
                    The location of the event.
                  </small>
                </>
              }
            >
              {
                countryDropdownOptions.find(
                  (x) =>
                    params?.product?.eventLocation &&
                    x.value === params?.product.eventLocation
                )?.label
              }
            </Descriptions.Item>
          </>
        )}
        <Descriptions.Item
          label={
            <>
              <span>Image</span>
              <br />
              <small style={{ color: "darkgray" }}>
                This image will be display on the checkout, user portal &
                invoices.
              </small>
            </>
          }
        >
          <Image src={params.product?.image} width={64} height={64} />
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Fiscality</span>
              <br />
              <small style={{ color: "darkgray" }}>
                The tax profile attached to this product.
              </small>
            </>
          }
        >
          <Input
            value={
              params.product.taxProfile &&
              getTaxProfileName(params.product.taxProfile)
            }
            disabled
          />
        </Descriptions.Item>
      </Descriptions>
      <Divider />
      <Descriptions title="Product Access" column={1} bordered>
        <Descriptions.Item
          label={
            <>
              <span>Learnworld</span>
              <br />
              <small style={{ color: "darkgray" }}>
                The learnworld courses that are part of this product.
              </small>
            </>
          }
        >
          <Select
            className="paradox-disabled-input"
            mode="multiple"
            allowClear
            placeholder="Selected courses"
            options={selectedCourses.map((course: any) => ({
              label: course.label,
              value: course.value,
            }))}
            value={selectedCourses.map((course: any) => course.value)}
            disabled
          />
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Circle.so - communities</span>
              <br />
              <small style={{ color: "darkgray" }}>
                The circle communities that are part of this product.
              </small>
            </>
          }
        >
          <Select
            className="paradox-disabled-input"
            mode="multiple"
            allowClear
            placeholder="Selected communities"
            options={[
              {
                label: "PXS",
                value: "PXS",
              },
              {
                label: "PXL",
                value: "PXL",
              },
            ]}
            value={selectedCommunities}
            disabled
          />
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Circle.so - spaces</span>
              <br />
              <small style={{ color: "darkgray" }}>
                The circle spaces that are part of this product.
              </small>
            </>
          }
        >
          <Select
            className="paradox-disabled-input"
            mode="multiple"
            allowClear
            placeholder="Selected spaces"
            options={selectedSpaces.map((space: any) => ({
              label: space.label,
              value: space.value,
            }))}
            value={selectedSpaces}
            disabled
          />
        </Descriptions.Item>
      </Descriptions>
    </>
  );
};
