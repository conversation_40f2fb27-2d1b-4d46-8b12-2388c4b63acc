"use client";

import { ConfigProvider, Flex, Layout } from "antd";
import { useState } from "react";
import { useSearchParams } from "next/navigation";

import Sidebar from "@/components/Sidebar";
import CustomerDrawer from "@/components/Customers/CustomerDrawer";

const { Content, Sider } = Layout;

const layoutStyle = {
  overflow: "hidden",
  width: "100%",
  height: "100vh",
};

const siderStyle: React.CSSProperties = {
  textAlign: "left",
  color: "#fff",
  backgroundColor: "#F7F8F9",
  overflowY: "auto",
};

const contentStyle: React.CSSProperties = {
  minHeight: 120,
  color: "#000",
  marginLeft: "10px",
  marginRight: "10px",
  backgroundColor: "white",
  overflowY: "auto",
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const searchParams = useSearchParams();
  const [collapsed, setCollapsed] = useState(false);

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: "#7351ED",
          controlItemBgHover: "#E5E4FC",
          fontSize: 12,
        },
        components: {
          Menu: {
            iconSize: 12,
            groupTitleFontSize: 14,
          },
          Table: {
            rowHoverBg: "#E5E4FC",
          },
        },
      }}
    >
      <Flex wrap="wrap">
        <Layout style={layoutStyle}>
          <Sider
            width="15%"
            style={siderStyle}
            trigger={null}
            collapsible
            collapsed={collapsed}
          >
            <Sidebar collapsed={collapsed} setCollapsed={setCollapsed} />
          </Sider>
          <Layout style={{ backgroundColor: "white" }}>
            <Content style={contentStyle}>{children}</Content>
            <CustomerDrawer open={searchParams.has("customer")} />
          </Layout>
        </Layout>
      </Flex>
    </ConfigProvider>
  );
}
