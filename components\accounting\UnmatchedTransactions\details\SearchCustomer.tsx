"use client";

import {
  FolderOutlined,
  StepForwardOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { Button, Form, Row, Select, Space, Typography } from "antd";
import { Card as ChargebeeCard, Customer } from "chargebee";
import { useEffect, useState } from "react";

import CustomerDropDownItem from "./CustomerDropDownItem";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchCustomers } from "@/hooks/customers";
import { getBusinessEntityName } from "@/lib/businessEntities";

type SearchCustomerProps = {
  handleSkip: Function;
  markPaymentAsIgnored: Function;
  attachPaymentToCustomer: Function;
  entityId?: string;
};

type CustomerItem = {
  customer: Customer;
  card?: ChargebeeCard;
};

const { Text } = Typography;

const SearchCustomer = ({
  handleSkip,
  markPaymentAsIgnored,
  attachPaymentToCustomer,
  entityId,
}: SearchCustomerProps) => {
  const [selectedCustomer, setSelectedCustomer] = useState<
    Customer | undefined
  >();
  const [customers, setCustomers] = useState<CustomerItem[] | []>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const searchTerm = useDebounce(searchQuery.trim(), 500);

  // Queries
  const {
    data: customerData,
    isLoading,
    mutate: refetchCustomers,
  } = useFetchCustomers({
    filters: {
      searchQuery: searchTerm,
    },
  });

  // This is needed because of the way the data is returned from the API.
  // Chargebee returns the list of customers in the list property of the response
  // if we have not passed any filters. Otherwise it provides the data directly in
  // the response.
  useEffect(() => {
    if (customerData?.error) {
      setCustomers([]);
      return;
    }
    if (!!customerData) {
      let custData = customerData?.list ? customerData.list : customerData;
      // Filter the customers based on the entityId
      if (!!entityId) {
        setCustomers(
          custData.filter((customer: CustomerItem) => {
            return customer.customer.business_entity_id === entityId;
          })
        );
      } else {
        setCustomers(custData);
      }
    }
  }, [customerData]);

  const [searchForm] = Form.useForm();

  const handleCustomerSelection = async (customerId: string) => {
    let customer = customers?.find(
      (customerItem: CustomerItem) => customerItem.customer?.id === customerId
    )?.customer;
    setSelectedCustomer(customer);
    setSearchQuery("");
    await refetchCustomers();
  };

  return (
    <div>
      <Row>
        <Form form={searchForm} style={{ width: "100vw" }}>
          <Form.Item name="customerSelectionDropdown">
            <Select
              style={{ width: "100%" }}
              allowClear
              showSearch
              filterOption={false}
              loading={isLoading}
              onClear={() => {
                setSelectedCustomer(undefined);
              }}
              onSelect={(value) => {
                handleCustomerSelection(value);
              }}
              onSearch={(value) => {
                setSearchQuery(value);
              }}
              placeholder="Search for a customer"
              options={
                (customers &&
                  customers?.map((customerItem: CustomerItem) => {
                    if (customerItem?.customer) {
                      return {
                        label:
                          customerItem?.customer?.first_name +
                          " " +
                          customerItem?.customer?.last_name,
                        value: customerItem?.customer?.id?.toString(),
                      };
                    } else {
                      return {
                        label: "",
                        value: "",
                      };
                    }
                  })) ||
                []
              }
              optionRender={(item) => {
                return (
                  <CustomerDropDownItem
                    customer={
                      customers.find((customerItem: CustomerItem) => {
                        return (
                          customerItem?.customer.id?.toString() === item.value
                        );
                      })?.customer
                    }
                  />
                );
              }}
            />
          </Form.Item>
          {/* </Form.Item> */}
        </Form>
      </Row>
      <div style={{ overflowY: "scroll", height: "500px" }}>
        {selectedCustomer !== undefined && selectedCustomer.id ? (
          <div className="paradox-rounded-box" style={{ padding: "20px" }}>
            <Text style={{ fontSize: 17 }}>
              {selectedCustomer?.first_name} {selectedCustomer?.last_name}
            </Text>
            <br />
            <br />
            <Text style={{ color: "gray" }}>Email: </Text>
            <Text>{selectedCustomer?.email}</Text>
            <br />
            <Text style={{ color: "gray" }}>Billing Entity: </Text>
            <Text>
              {getBusinessEntityName(selectedCustomer?.business_entity_id)}
            </Text>
            <br />
            <Text style={{ color: "gray" }}>Phone: </Text>
            <Text>{selectedCustomer?.phone}</Text>
            <br />
          </div>
        ) : (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              minHeight: "300px",
            }}
          >
            <Button type="primary" icon={<SyncOutlined />}>
              Search for a customer
            </Button>
          </div>
        )}
      </div>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
        }}
      >
        <Space style={{ marginTop: "20px" }}>
          <Button
            onClick={() => {
              markPaymentAsIgnored();
            }}
            icon={<FolderOutlined />}
          >
            Mark as ignored
          </Button>
          <Button
            onClick={() => {
              setSelectedCustomer(undefined);
              searchForm.resetFields();
              handleSkip();
            }}
            icon={<StepForwardOutlined />}
          >
            Skip
          </Button>
          <Button
            icon={<SyncOutlined />}
            type="primary"
            disabled={selectedCustomer === undefined}
            onClick={() => {
              if (!selectedCustomer?.id) {
                return;
              }
              attachPaymentToCustomer(selectedCustomer.id);
              setSelectedCustomer(undefined);
              searchForm.resetFields();
            }}
          >
            Attach bank transfer
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default SearchCustomer;
