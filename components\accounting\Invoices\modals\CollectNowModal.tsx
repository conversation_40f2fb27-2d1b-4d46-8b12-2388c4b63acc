"use client";

import { App, Form, InputNumber, Modal, Typography } from "antd";
import { EditOutlined } from "@ant-design/icons";

import { formatCents } from "@/lib/currency";
import { Invoice } from "@/types";
import { collectPayment } from "@/lib/services/chargebee";

const { Text } = Typography;

type CollectNowProps = {
  amountRefundable: number;
  isOpen: boolean;
  setIsOpen: Function;
  invoice: Invoice;
  isAutoCollectionOff: boolean;
  refetchInvoice: Function;
};

export const CollectNowModal = ({
  isOpen,
  setIsOpen,
  invoice,
  isAutoCollectionOff,
  refetchInvoice,
}: CollectNowProps) => {
  const { message, modal } = App.useApp();
  const [collectNowForm] = Form.useForm();

  const collectNowCurrentInvoice = async (values: any) => {
    collectNowForm.validateFields();
    modal.warning({
      title: `Are you sure you want to collect ${formatCents(values.amountCollected * 100)}?`,
      content: `By clicking Proceed, an attempt will be made to immediately charge the 
      customer's payment method for the specified amount. This action cannot be undone.`,
      okText: "Proceed",
      okType: "primary",
      cancelText: "Close",
      okCancel: true,
      onCancel: () => {},
      onOk: async () => {
        const res = await collectPayment({
          id: String(invoice?.chargebeeId),
          amount: values.amountCollected * 100,
        });
        if (res?.error) {
          message.open({
            type: "error",
            content: `Cannot collect payment! Reason: ${res.message}`,
          });
        } else {
          message.open({
            type: "success",
            content: "Invoice collected successfully!",
          });
        }
        setIsOpen(false);
        await refetchInvoice();
      },
    });
  };

  return (
    <Modal
      open={isOpen}
      closable={false}
      onOk={() => {
        collectNowForm.submit();
      }}
      onCancel={() => {
        setIsOpen(false);
      }}
      okText="Proceed"
      okButtonProps={{
        disabled: isAutoCollectionOff,
      }}
    >
      <Text style={{ fontSize: 15 }} strong>
        Collect now
      </Text>
      <br />
      <Text style={{ fontSize: 13, color: "gray" }}>
        Payment collection will be attempted immediately
      </Text>
      <br />
      <Text style={{ fontSize: 13, color: "red" }}>
        {isAutoCollectionOff
          ? "If you want to collect now, first change payment method for this subscription to 'Online Payment'"
          : ""}
      </Text>
      <br />
      <br />
      <Form
        layout="vertical"
        form={collectNowForm}
        onFinish={collectNowCurrentInvoice}
      >
        <Form.Item
          name="amountCollected"
          label="Amount collected"
          rules={[
            {
              required: true,
              message: "Please enter the amount to collect",
            },
          ]}
          style={{ marginBottom: "5px" }}
        >
          <InputNumber
            style={{ width: "250px" }}
            min={1}
            max={invoice.amountDue && invoice.amountDue / 100}
            addonBefore="EUR"
            addonAfter={<EditOutlined />}
            placeholder="Amount to collect"
          />
        </Form.Item>
        <Text style={{ color: "gray", fontSize: 13 }}>
          Maximum collectable amount:{" "}
          {formatCents(invoice.amountDue ? invoice.amountDue : 0)}
        </Text>
      </Form>
    </Modal>
  );
};
