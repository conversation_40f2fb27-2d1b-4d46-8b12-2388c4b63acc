"use client";

import { App, Card, Form, Input, Button } from "antd";
import { Customer } from "chargebee";
import { useState } from "react";

import { updateBillingInfo } from "@/lib/services/chargebee/customers";

type CustomerInfoProps = {
  data: Customer | null;
  onUpdate?: () => void;
};

type FormValues = {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  country: string;
  city: string;
  state: string;
};

const formFields = [
  {
    name: "first_name",
    label: "First name",
    rules: [{ required: true, message: "First name is required" }],
    disabled: false,
  },
  {
    name: "last_name",
    label: "Last name",
    rules: [{ required: true, message: "Last name is required" }],
    disabled: false,
  },
  {
    name: "phone",
    label: "Phone",
    rules: [{ required: false }],
    disabled: false,
  },
  {
    name: "country",
    label: "Country",
    rules: [{ required: true, message: "Country is required" }],
    disabled: false,
  },
  {
    name: "city",
    label: "City",
    rules: [{ required: true, message: "City is required" }],
    disabled: false,
  },
  {
    name: "state",
    label: "State",
    rules: [{ required: false }],
    disabled: false,
  },
];

function CustomerInfo({ data, onUpdate }: CustomerInfoProps) {
  const { message } = App.useApp();
  const [isLoading, setIsLoading] = useState(false);
  const [form] = Form.useForm<FormValues>();

  const initialValues = {
    first_name: data?.first_name || "",
    last_name: data?.last_name || "",
    phone: data?.phone || "",
    country: data?.billing_address?.country || "",
    city: data?.billing_address?.city || "",
    state: data?.billing_address?.state || "",
  };

  // TODO: Remove email field from form

  form.setFieldsValue(initialValues);

  const handleUpdateBillingInfo = async (values: FormValues) => {
    try {
      setIsLoading(true);
      await updateBillingInfo(data?.id || "", {
        ...values,
        line1: data?.billing_address?.line1 || "",
      });
      message.open({
        type: "success",
        content: "Billing information updated successfully",
      });
      onUpdate?.();
    } catch (error) {
      console.error(error);
      message.open({
        type: "error",
        content: "Failed to update billing information",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!data) return null;

  return (
    <>
      <Card title="General information" style={{ marginBottom: 20 }}>
        <Form<FormValues>
          form={form}
          layout="vertical"
          initialValues={initialValues}
          onFinish={handleUpdateBillingInfo}
        >
          {formFields.map(({ name, label, disabled, rules }, index) => (
            <Form.Item key={index} label={label} name={name} rules={rules}>
              <Input disabled={disabled} />
            </Form.Item>
          ))}
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={isLoading}>
              Update Information
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </>
  );
}

export default CustomerInfo;
