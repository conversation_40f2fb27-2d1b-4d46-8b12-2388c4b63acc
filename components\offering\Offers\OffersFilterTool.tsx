"use client";

import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Col, Input, Row, Select } from "antd";

import { initialFilter } from "./OffersTable";
import { FilterOffer, Product, ProductLine } from "@/types";

type OffersFilterToolProps = {
  filters: FilterOffer;
  productLines: ProductLine[];
  products: Product[];

  setFilters: Function;
  onCreateOffer: Function;
};

export default function OffersFilterTool({
  filters,
  productLines,
  products,
  onCreateOffer,
  setFilters,
}: OffersFilterToolProps) {
  const handleFamilyFilterChange = async (value: any) => {
    if (value) {
      setFilters((prev: FilterOffer) => ({
        ...prev,
        productFamily: value,
        product: null,
        productPlan: null,
        productPlans: null,
      }));
    } else {
      setFilters((prev: FilterOffer) => ({
        ...prev,
        productFamily: null,
        product: null,
        productPlan: null,
        productPlans: null,
      }));
    }
  };

  const handleProductLineChange = (value: number) => {
    setFilters((prev: FilterOffer) => ({
      ...prev,
      productLine: value,
      productFamily: null,
      product: null,
      productPlan: null,
      productFamilies: null,
      productPlans: null,
    }));
    if (value) {
      setFilters((prev: FilterOffer) => ({
        ...prev,
        productFamily: null,
        productFamilies: productLines
          ?.find((x: ProductLine) => x.id === value && x.status === "active")
          ?.families?.filter((val: any) => {
            return val.status === "active";
          }),
      }));
    } else {
      setFilters(() => ({
        ...initialFilter,
      }));
    }
  };

  const handleProductFilterChange = (value: any) => {
    setFilters((prev: FilterOffer) => ({
      ...prev,
      product: value,
      productPlan: null,
      productPlans: products
        ?.find((x: any) => x.id === value)
        ?.plans?.filter((val, index) => {
          return val.status === "active";
        }),
    }));
  };

  return (
    <Row style={{ lineHeight: "10px" }}>
      <Col xs={12} sm={12} md={16} lg={16} xl={17} xxl={19}>
        <Input
          prefix={<SearchOutlined />}
          placeholder="Search in Offers"
          onChange={(event) =>
            setFilters({ ...filters, name: event.target.value })
          }
        />

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginTop: 10,
          }}
        >
          <Select
            style={{ width: "15%" }}
            allowClear
            placeholder="Product line"
            onChange={handleProductLineChange}
            options={
              productLines &&
              productLines.map((item) => {
                return { value: item.id, label: <span>{item.name}</span> };
              })
            }
            value={(filters.productLine && Number(filters.productLine)) || null}
          />
          <Select
            style={{ width: "15%" }}
            allowClear
            onChange={handleFamilyFilterChange}
            placeholder="Product family"
            disabled={!filters?.productFamilies}
            options={
              filters.productFamilies &&
              filters.productFamilies.map((item) => {
                return {
                  value: item.id,
                  label: <span>{item.name}</span>,
                };
              })
            }
            value={
              (filters.productFamily && Number(filters.productFamily)) || null
            }
          />
          <Select
            style={{ width: "15%" }}
            allowClear
            onChange={handleProductFilterChange}
            placeholder="Product"
            disabled={!products}
            options={
              products &&
              products.map((item) => {
                return {
                  value: item.id,
                  label: <span>{item.externalName}</span>,
                };
              })
            }
            value={(filters.product && Number(filters.product)) || null}
          />
          <Select
            style={{ width: "15%" }}
            allowClear
            placeholder="Plan"
            onChange={(value) =>
              setFilters((prev: FilterOffer) => ({
                ...prev,
                productPlan: value,
              }))
            }
            disabled={!filters.productPlans}
            options={
              filters.productPlans &&
              filters.productPlans.map((item) => {
                return {
                  value: item.id,
                  label: <span>{item.internalName}</span>,
                };
              })
            }
            value={(filters.productPlan && Number(filters.productPlan)) || null}
          />
          <Select
            style={{ width: "15%" }}
            allowClear
            placeholder="Status"
            options={[
              { value: "active", label: <span>Active</span> },
              { value: "disabled", label: <span>Disabled</span> },
            ]}
            onChange={(values) => setFilters({ ...filters, status: values })}
          />
          <Button
            type="link"
            onClick={() => {
              setFilters(initialFilter);
            }}
          >
            Clear Filters
          </Button>
        </div>
      </Col>
      <Col xs={11} sm={11} md={7} lg={7} xl={6} xxl={4} offset={1}>
        <Button
          style={{
            backgroundColor: "#7351EE",
            color: "white",
            width: "100%",
          }}
          icon={<PlusOutlined />}
          onClick={() => onCreateOffer()}
        >
          Create an offer
        </Button>
      </Col>
    </Row>
  );
}
