"use client";

import { Modal, Select, Typography } from "antd";

import { PlanDTO, Product } from "@/types";
import { useMemo } from "react";
import { FilterForeverOfferProps } from "../forms/ConfigureForeverOfferProductsForm";
import ForeverProductDropDownSelect from "./ForeverProductDropdownSelect";
import { EmptyPlanItem } from "../type";

type AddForeverProductModalProps = {
  products: Product[];
  filters: FilterForeverOfferProps;
  isOpen: boolean;
  setFilters: (p: any) => void;
  setIsOpen: (s: boolean) => void;
};

const AddForeverProductModal = ({
  products,
  filters,
  isOpen,
  setFilters,
  setIsOpen,
}: AddForeverProductModalProps) => {
  const allProductsOpts = useMemo(() => {
    if (!products) {
      return {};
    } else {
      return {
        label: <span>All Products</span>,
        options: products
          .filter((x: Product) => x.id !== filters.selectedProduct?.id)
          .map((item: Product) => {
            return {
              label: item.externalName,
              value: item.id,
            };
          }),
      };
    }
  }, [products?.length, filters.selectedProduct]);

  const handleProductSelection = (selectedProductId: any) => {
    const selectedProduct = products.find(
      (x: Product) => x.id === selectedProductId
    );
    const monthlyPlan = selectedProduct?.plans.find(
      (x: PlanDTO) => x.prices[0].periodUnit === "monthly-forever"
    );
    const yearlyPlan = selectedProduct?.plans.find(
      (x: PlanDTO) => x.prices[0].periodUnit === "yearly-forever"
    );
    setFilters((prev: FilterForeverOfferProps) => ({
      ...prev,
      selectedProduct: selectedProduct,
      selectedMonthlyPlan: monthlyPlan ? monthlyPlan : EmptyPlanItem,
      selectedYearlyPlan: yearlyPlan ? yearlyPlan : EmptyPlanItem,
    }));
    setIsOpen(false);
  };
  return (
    <Modal
      onCancel={() => setIsOpen(false)}
      open={isOpen}
      width="50%"
      closable={false}
      footer={[]}
    >
      <br />
      {products && (
        <Select
          style={{ width: "100%" }}
          showSearch
          allowClear
          placeholder="Search product ..."
          autoFocus={true}
          onChange={handleProductSelection}
          value={filters?.selectedProduct && filters?.selectedProduct?.id}
          options={[
            ...(filters.selectedProduct
              ? [
                  {
                    label: <span>Selected Products</span>,
                    options: [
                      {
                        label: filters.selectedProduct.externalName,
                        value: filters.selectedProduct.id,
                      },
                    ],
                  },
                ]
              : []),
            allProductsOpts,
          ]}
          optionRender={(option) => (
            <ForeverProductDropDownSelect
              product={products.find((x: Product) => x.id === option.key)}
              showDisabledTooltip={false}
              alreadySelected={filters.selectedProduct?.id === option.key}
            />
          )}
        />
      )}
    </Modal>
  );
};

export default AddForeverProductModal;
