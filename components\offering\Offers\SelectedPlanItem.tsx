"use client";

import { PlanDTO } from "@/types";
import { DownOutlined } from "@ant-design/icons";
import { Col, Image, Popover, Row, Space, Tag, Typography } from "antd";
import { useEffect, useState } from "react";

const { Text } = Typography;

const europeImg =
  "https://paradox-os.s3.eu-west-3.amazonaws.com/projectimages/europe-flag-icon.png";

export const SelectedPlanItem = (params: {
  plans: PlanDTO[];
  billingCycle: string;
  image: string;
  onChange?: Function;
  selectedPlan: string | undefined;
}) => {
  const [selectedPlan, setSelectedPlan] = useState<PlanDTO>(params.plans[0]);
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);

  let multiplePlans: boolean = params.plans.length > 1;

  useEffect(() => {
    let tempPlan = params.plans.find(
      (x) => x.id?.toString() === params.selectedPlan
    );
    if (tempPlan) {
      setSelectedPlan(tempPlan);
    }
  }, [params.selectedPlan]);

  const togglePopover = () => {
    setPopoverOpen(!popoverOpen);
  };

  const handleSelectedPlanChanged = (plan: PlanDTO) => {
    setSelectedPlan(plan);
    setPopoverOpen(false);
    if (params.onChange) {
      params.onChange(params.billingCycle, plan.id);
    }
  };

  const popoverContent = () => {
    return (
      <Space style={{ width: "300px" }} direction="vertical">
        {params.plans.map((item) => {
          return (
            <Row
              key={item.chargebeeId}
              style={{
                padding: "10px",
                cursor: "pointer",
                backgroundColor:
                  selectedPlan.id === item.id ? "#F6F0FF" : "white",
              }}
              onClick={() => {
                handleSelectedPlanChanged(item);
              }}
            >
              <Col span={20} offset={0}>
                {item.internalName}
                <br />
                <Space>
                  <Image
                    src={europeImg}
                    preview={false}
                    height={18}
                    width={25}
                    style={{ borderRadius: "3px" }}
                  />
                  <Text style={{ color: "darkgray" }}>
                    {item.prices[0].currencyCode.toUpperCase()} -&nbsp;
                    {item.prices[0].amount}€ =&nbsp;
                    {item.prices[0].totalBillingCycles} x&nbsp;
                    {item.prices[0].amountPerBillingCycle}€
                  </Text>
                </Space>
              </Col>
              <Col span={4}>
                <Tag color="purple">x{item.prices[0].totalBillingCycles}</Tag>
              </Col>
            </Row>
          );
        })}
      </Space>
    );
  };

  if (multiplePlans)
    return (
      <div style={{ width: "100%", paddingRight: "12px" }}>
        <div className="paradox-plan-item">
          <div className="paradox-plan-image-container">
            <Image src={params.image} height={50} />
          </div>
          <div
            style={{
              width: "60%",
              overflow: "hidden",
              textOverflow: "ellipsis",
              marginLeft: "5px",
              whiteSpace: "nowrap",
            }}
          >
            <Popover title={selectedPlan.internalName}>
              {selectedPlan.internalName}
            </Popover>
            <br />
            <Text
              style={{
                fontSize: 13,
                color: "darkgray",
              }}
            >
              {selectedPlan.prices[0].amount}€ ={" "}
              {selectedPlan.prices[0].totalBillingCycles} x{" "}
              {selectedPlan.prices[0].amountPerBillingCycle}€
            </Text>
          </div>
          <Popover
            content={popoverContent}
            placement="bottomRight"
            trigger="click"
            open={popoverOpen}
          >
            <div
              className="paradox-planitem-arrow"
              style={{
                display: "flex",
              }}
              onClick={togglePopover}
            >
              <DownOutlined />
            </div>
          </Popover>
        </div>
      </div>
    );
  else
    return (
      <div style={{ width: "100%", paddingRight: "12px" }}>
        <div className="paradox-plan-item">
          <div className="paradox-plan-image-container">
            <Image src={params.image} height={50} />
          </div>
          <div
            style={{
              width: "70%",
              overflow: "hidden",
              textOverflow: "ellipsis",
              marginLeft: "5px",
              whiteSpace: "nowrap",
            }}
          >
            <Popover title={selectedPlan.internalName}>
              {selectedPlan.internalName}
            </Popover>{" "}
            <br />
            <Text
              style={{
                fontSize: 13,
                color: "darkgray",
              }}
            >
              {selectedPlan.prices[0].amount}€ ={" "}
              {selectedPlan.prices[0].totalBillingCycles} x{" "}
              {selectedPlan.prices[0].amountPerBillingCycle}€
            </Text>
          </div>
        </div>
      </div>
    );
};
