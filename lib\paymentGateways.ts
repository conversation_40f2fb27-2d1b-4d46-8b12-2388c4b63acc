type SelectOption = {
  label: string;
  value: string;
};

//***************** TODO: This is a temporary implementation until
//****************** the backend has the endpoint for getting this information
//****************** more info at https://app.clickup.com/t/86byr7n12

/**
 * Get the payment gateways configured in ChargeBee.
 * This will return different payment gateways based on the environment.
 * @returns {SelectOption[]} The list of payment gateways (labels and corresponding values).
 */
export const getPaymentGateways = (): SelectOption[] => {
  if (process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT === "production") {
    return [
      // ! The functionality is not ready for the entire product, so the option is not displayed at the moment.
      { label: "CheckoutPG", value: "gw_19ABawU8x4lHxBGV"},
      { label: "StripePG", value: "gw_199NZ6U8x36hb59" },
      { label: "StripePI", value: "gw_198cP4U8x5B5U5y" },
    ];
  } else if (
    process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT === "staging" ||
    process.env.NODE_ENV === "development"
  ) {
    return [
      { label: "Chargebee test (PI & PG)", value: "gw_199NXwU2AovKXlK" },
      { label: "PG - Stripe", value: "gw_773qKU2HkAz11o9" },
      { label: "PI - Stripe", value: "gw_199NW1U3UtmQOuj" },
      { label: "Checkout.com (Luc)", value: "gw_BTLyUhUFII51iKtd" },
    ];
  }
  return [];
};

/**
 * Get the name of the payment gateways based on the tax profile code.
 * @param {string} value The code of the payment gateway.
 * @returns {string} The name of the payment gateway.
 */
export const getPaymentGatewayName = (value: string | undefined): string => {
  if (!value) return "";
  const paymentGateways = getPaymentGateways();
  const paymentGateway = paymentGateways.find(
    (gateway) => gateway.value === value
  );
  return paymentGateway ? paymentGateway.label : "";
};
