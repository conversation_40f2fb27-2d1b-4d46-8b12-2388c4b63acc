"use client";

import { PlusOutlined } from "@ant-design/icons";
import { CloseOutlined } from "@ant-design/icons";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Button, Col, Divider, Drawer, Row, Space, Typography } from "antd";

const { Text } = Typography;

type DrawerFormContainerProps = {
  children: React.ReactNode;
  isOpen: boolean;
  submitButtonProps?: Record<string, any>;
  submitText: string;
  title: string;
  width?: string;
  onCancel: () => void;
  onClose: () => void;
  onSubmit?: () => void;
};

export default function DrawerFormContainer({
  children,
  isOpen,
  submitButtonProps,
  submitText = "Submit",
  title,
  width = "50%",
  onCancel,
  onClose,
  onSubmit,
}: DrawerFormContainerProps) {
  return (
    <Drawer open={isOpen} width={width} closeIcon={false} destroyOnClose>
      <div>
        <Row
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div
            style={{
              display: "flex",
              width: "60%",
              justifyContent: "start",
              alignItems: "center",
            }}
          >
            <Space>
              <Button
                icon={<ArrowLeftOutlined style={{ color: "#5B5B5D" }} />}
                onClick={() => {
                  onCancel();
                }}
              />
              <Text style={{ fontSize: 24 }}>{title}</Text>
            </Space>
          </div>
          <Space>
            <Button
              icon={<CloseOutlined />}
              onClick={() => {
                onCancel();
              }}
            >
              <Text strong>Dismiss</Text>
            </Button>
            {onSubmit && (
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => {
                  onSubmit?.();
                }}
                {...submitButtonProps}
              >
                <Text strong style={{ color: "white" }}>
                  {submitText}
                </Text>
              </Button>
            )}
          </Space>
        </Row>
        <Divider />
        <Row>
          <Col span={24} style={{ display: "flex", justifyContent: "center" }}>
            <div
              style={{
                width: "100%",
                paddingRight: "30px",
                paddingLeft: "30px",
              }}
            >
              {children}
            </div>
          </Col>
        </Row>
      </div>
    </Drawer>
  );
}
