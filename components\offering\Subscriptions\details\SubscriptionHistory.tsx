"use client";

import { CalendarOutlined } from "@ant-design/icons";
import { Card, Table, Tabs } from "antd";
import { TabsProps, Tag } from "antd";
import { useRouter } from "next/navigation";
import { useMemo } from "react";

import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { computeTransactionStatusColor } from "@/components/accounting/Transactions/utils/styles";
import { CreditNote, Invoice, Transaction } from "@/types";

type SubscriptionHistoryProps = {
  data: DataProps;
};

type DataProps = {
  invoices: Invoice[];
  creditNotes: CreditNote[];
  transactions: Transaction[];
};

type InvoicesProps = {
  data: Invoice[];
  onClick: (id: string) => void;
};
type CreditNotesProps = {
  data: CreditNote[];
  onClick: (id: string) => void;
};
type TransactionsProps = {
  data: Transaction[];
  onClick: (id: string) => void;
};

const Invoices = ({ data, onClick }: InvoicesProps) => {
  return (
    <Table
      dataSource={data}
      size="small"
      rowKey={(record) => record.id}
      onRow={(record) => {
        return {
          onClick: () => onClick(record.chargebeeId),
        };
      }}
      columns={[
        { title: "ID", key: "id", render: (record) => record.id },
        {
          title: "Status",
          key: "status",
          render: (record) => (
            <Tag color={record.status === "paid" ? "green" : "red"}>
              {record.status}
            </Tag>
          ),
        },
        {
          title: (
            <>
              <CalendarOutlined />
              &nbsp;Created at
            </>
          ),
          key: "date",
          render: (record) => formatDateFromUnix(record.date),
        },
        {
          title: "Total",
          key: "total",
          render: (record) => formatCents(record.total),
        },
      ]}
      pagination={false}
    />
  );
};

const CreditNotes = ({ data, onClick }: CreditNotesProps) => {
  return (
    <Table
      dataSource={data}
      size="small"
      rowKey={(record) => record.id}
      onRow={(record) => {
        return {
          onClick: () => onClick(record.chargebeeId),
        };
      }}
      columns={[
        { title: "ID", key: "id", render: (record) => record.id },
        {
          title: "Status",
          key: "status",
          render: (record) => (
            <Tag color={record.status === "adjusted" ? "green" : "red"}>
              {record.status}
            </Tag>
          ),
        },
        {
          title: (
            <>
              <CalendarOutlined />
              &nbsp;Created at
            </>
          ),
          key: "date",
          render: (record) => formatDateFromUnix(record.date),
        },
        {
          title: "Total",
          key: "total",
          render: (record) => formatCents(record.total),
        },
      ]}
      pagination={false}
    />
  );
};

const Transactions = ({ data, onClick }: TransactionsProps) => {
  return (
    <Table
      dataSource={data}
      size="small"
      rowKey={(record) => record.id}
      onRow={(record) => {
        return {
          onClick: () => onClick(record.chargebeeId),
        };
      }}
      columns={[
        { title: "ID", key: "id", render: (record) => record.id },
        {
          title: "Status",
          key: "status",
          render: (record) => (
            <Tag color={computeTransactionStatusColor(record.status)}>
              {record.status}
            </Tag>
          ),
        },
        {
          title: (
            <>
              <CalendarOutlined />
              &nbsp;Created at
            </>
          ),
          key: "date",
          render: (record) => formatDateFromUnix(record.date),
        },
        {
          title: "Amount",
          key: "amount",
          render: (record) => formatCents(record.amount),
        },
        {
          title: "Type",
          key: "type",
          render: (record) => record.type,
        },
      ]}
      pagination={false}
    />
  );
};

export default function SubscriptionHistory({
  data,
}: SubscriptionHistoryProps) {
  const router = useRouter();

  const items: TabsProps["items"] = useMemo(() => {
    return [
      {
        key: "1",
        label: `Invoices (${data?.invoices.length})`,
        children: (
          <Invoices
            data={data?.invoices as InvoicesProps["data"]}
            onClick={(id) => router.push(`/dashboard/invoices/${id}`)}
          />
        ),
      },
      {
        key: "2",
        label: `Credit notes (${data?.creditNotes.length})`,
        children: (
          <CreditNotes
            data={data?.creditNotes as CreditNotesProps["data"]}
            onClick={(id) => router.push(`/dashboard/credit-notes/${id}`)}
          />
        ),
      },
      {
        key: "3",
        label: `Transactions (${data?.transactions.length})`,
        children: (
          <Transactions
            data={data?.transactions as TransactionsProps["data"]}
            onClick={(id) => router.push(`/dashboard/transactions/${id}`)}
          />
        ),
      },
    ];
  }, [data]);

  if (!data) return null;

  return (
    <Card title="History" style={{ width: "100%" }}>
      <Tabs defaultActiveKey="1" items={items} tabPosition="left" />
    </Card>
  );
}
