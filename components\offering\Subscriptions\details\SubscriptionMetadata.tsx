"use client";

import { PX_Subscription } from "@/types";
import { Descriptions, Typography } from "antd";

const { Text, Title } = Typography;

type SubscriptionMetadataProps = {
  subscription: PX_Subscription;
};

export default function SubscriptionMetadata({
  subscription,
}: SubscriptionMetadataProps) {
  return subscription.metadata &&
    Object.keys(subscription.metadata).length > 0 ? (
    <Descriptions
      title="Metadata"
      style={{ width: "100%" }}
      bordered
      size="small"
      column={1}
    >
      {Object.keys(subscription.metadata).map((key) => (
        <Descriptions.Item key={key} label={key}>
          <Text
            copyable={{
              text: subscription.metadata[key],
            }}
          >
            {subscription.metadata[key]}
          </Text>
        </Descriptions.Item>
      ))}
    </Descriptions>
  ) : (
    <div>
      <Title level={5}>Metadata</Title>
      <Text>No metadata</Text>
    </div>
  );
}
