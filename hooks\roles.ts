"use client";

import { useEffect } from "react";
import { App } from "antd";
import { useRouter } from "next/navigation";

import { useUser } from "./utils/useUser";
import { acl } from "@/lib/acl";
import { AccessControlEntry, Role } from "@/types";

/**
 * Use user role
 * Extracts the user role from the current user
 * @returns user role
 * @example
 * const { role, canAccess } = useUserRole();
 * if (role && !canAccess(accessLevel, false)) {
 *   router.push("/dashboard");
 * }
 */
export const useUserRole = () => {
  const { user: currentUser } = useUser();
  const { message } = App.useApp();

  const role = currentUser?.publicMetadata?.role as Role["value"];

  /**
   * Checks if a user with a specific role can access a given access control entry.
   * @param entry - The key of the access control entry.
   * @param showNotification - Optional parameter to indicate whether to show a notification if access is denied.
   * @returns A boolean value indicating whether the user can access the entry.
   */
  const canAccess = (
    entry: AccessControlEntry["key"],
    showNotification = true
  ) => {
    if (!role || !entry || !currentUser) {
      return false;
    }
    // If the feature flag is disabled, allow access to all users
    if (process.env.NEXT_PUBLIC_FEATURE_ENABLE_ROLES !== "true") {
      return true;
    }
    // Find the access control item (in ACL) that matches the entry
    const acItem = acl.find((level) => level.key === entry);
    if (!acItem || !acItem?.authorizedRoles) {
      // If the access control item is not found or does not have authorized roles, return false
      return false;
    }
    // Get the user's email
    const userEmail = currentUser.primaryEmailAddress?.emailAddress;
    // Check if the user has the role to access the entry
    let isAllowed = acItem?.authorizedRoles.includes(role);
    // Check if the user is forbidden from accessing the entry
    if (userEmail && acItem?.forbiddenFor?.includes(userEmail)) {
      isAllowed = false;
    }
    // If the user does not have access and the feature flag is enabled, show a notification
    if (!isAllowed && showNotification && acItem.errorMsg) {
      message.open({
        type: "error",
        content: `Unauthorized - ${acItem.errorMsg}`,
      });
    }
    return isAllowed;
  };

  return { canAccess, currentUser, role };
};

/**
 * Protect page
 * If the user does not have access to the page, redirect to the dashboard
 * @param accessLevel - access level
 * @returns protect page
 */
export const useProtectPage = (accessLevel: AccessControlEntry["key"]) => {
  const router = useRouter();
  const { role, canAccess } = useUserRole();
  const { message } = App.useApp();

  useEffect(() => {
    if (role && !canAccess(accessLevel, false)) {
      message.open({
        type: "error",
        content: "Unauthorized - You are not authorized to access this page",
      });
      router.push("/dashboard");
    }
  }, [role]);

  return { canAccess };
};
