"use client";
import { EditOutlined } from "@ant-design/icons";
import { Button, Col, Descriptions, Row, Tag, Typography } from "antd";
import type { JSX } from "react";

import { convertDateToDateString } from "@/lib/date";
import { useUserRole } from "@/hooks/roles";

const { Text } = Typography;

const DiscountSettings = (params: {
  discount: any;
  showModal: Function;
}): JSX.Element => {
  const { canAccess } = useUserRole();

  return (
    <div>
      <Row style={{ width: "100%", marginBottom: 10 }}>
        <Col span={12}>
          <Descriptions
            title="Discount settings"
            column={1}
            bordered
            size="small"
          >
            <Descriptions.Item
              label={
                <>
                  <span>Name</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    A descriptive name for this coupon. Please note that this
                    will be displayed to customers, in case &quot;Invoice
                    Name&quot; field is not provided.
                  </small>
                </>
              }
            >
              <Text copyable={{ text: params.discount?.name }}>
                {params.discount?.name}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Discount code</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    This will be the reference used by Paradox OS to identify
                    this coupon. And this code will be used by our Customers
                    during checkout.
                  </small>
                </>
              }
            >
              <Text copyable={{ text: params.discount?.code }}>
                {params.discount?.code}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Status</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    Setting a discount code to ‘Disabled’ will make it
                    impossible to use this discount code on the checkout page.
                  </small>
                </>
              }
            >
              <Tag
                color={params.discount?.status === "active" ? "green" : "red"}
              >
                {params.discount.status
                  ?.charAt(0)
                  .toUpperCase()
                  .concat(params.discount.status.slice(1))}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Invoice name</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    Name displayed to your custotmers on the invoices and hosted
                    payment pages.
                  </small>
                </>
              }
            >
              {params.discount.invoiceName ? (
                <Text copyable={{ text: params.discount.invoiceName }}>
                  {params.discount.invoiceName}
                </Text>
              ) : (
                <Text style={{ color: "darkgray" }}>Not provided</Text>
              )}
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Duration type</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    How long should this coupon be applied to the subscription?
                  </small>
                </>
              }
            >
              {params.discount?.durationType === "one_time"
                ? "One Time"
                : params.discount?.durationType === "limited_period"
                  ? "Limited Period"
                  : "Forever"}
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Duration period</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    How long should this coupon be applied to the subscription?
                  </small>
                </>
              }
            >
              {params.discount?.validUntil
                ? convertDateToDateString(params.discount?.validUntil)
                : "Unlimited"}
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Maximum redemptions</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    Maximum number of times this coupon can be redeemed. If not
                    specified, the coupon can be redeemed an indefinite number
                    of times.
                  </small>
                </>
              }
            >
              {params?.discount.maxRedemptions === null
                ? "Unlimited"
                : params?.discount.maxRedemptions}
            </Descriptions.Item>
          </Descriptions>
        </Col>
        <Col span={12} style={{ paddingLeft: "10px" }}>
          <Descriptions title="Discount price" column={1} bordered size="small">
            <Descriptions.Item label="Discount type">
              {params?.discount.type === "percentage"
                ? "Percentage"
                : "Fixed Amount"}
            </Descriptions.Item>
            <Descriptions.Item label="Discount amount">
              {params?.discount.amount}&nbsp;
              {params?.discount.type === "percentage" ? "%" : "EUR"}
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Apply on</span>
                  <br />
                  <small style={{ fontSize: 13, color: "darkgray" }}>
                    Where do you want this discount code to be applied on, at
                    invoice amount or per plan.
                  </small>
                </>
              }
            >
              {params.discount?.applyOn === "invoice_amount"
                ? "Invoice amount"
                : "Each specified item"}
            </Descriptions.Item>
            <Descriptions.Item label="Offers">
              <Tag color="red">None</Tag>
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <span>Plans ({params.discount.pricesAttachedTo.length})</span>
              }
            >
              <span>
                {params?.discount?.attachedTo !== null &&
                params?.discount?.attachedTo[0]?.itemIds[0] === "specific"
                  ? "Specific Plans"
                  : params?.discount?.attachedTo !== null &&
                      params?.discount?.attachedTo[0]?.itemIds[0] === "all"
                    ? "All"
                    : "None"}
              </span>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => {
                  if (canAccess("discount-update")) {
                    params?.showModal();
                  }
                }}
              >
                Edit
              </Button>
            </Descriptions.Item>
          </Descriptions>
        </Col>
      </Row>
    </div>
  );
};

export default DiscountSettings;
