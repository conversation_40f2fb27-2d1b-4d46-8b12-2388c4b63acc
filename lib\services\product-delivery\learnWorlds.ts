"use server";

import { calculateDateSecondsFromNow } from "@/lib/date";
import { Course } from "@/types";

const baseURL = process.env.NEXT_PUBLIC_LW_API_URL;

const getAccesToken = async (): Promise<any> => {
  const cachedAccessToken = global.lmsCache.get("access-token");
  if (cachedAccessToken) {
    const cachedExpiry: string | undefined = global.lmsCache.get(
      "access-token-expiry"
    );
    if (cachedExpiry) {
      const now = new Date();
      const expiry = new Date(cachedExpiry);
      if (now < expiry) {
        return cachedAccessToken;
      }
    }
  }
  const requestInit: RequestInit = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      grant_type: "client_credentials",
      client_id: process.env.LW_CLIENT_ID,
      client_secret: process.env.LW_CLIENT_SECRET,
    }),
  };
  if (!requestInit.headers) {
    requestInit.headers = {};
  }
  (requestInit.headers as Record<string, string>)["Lw-Client"] =
    process.env.LW_CLIENT_ID?.toString() ?? "";
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_LW_AUTH_URL}`,
    requestInit
  );
  const jsonResult = await res.json();
  if (jsonResult?.tokenData && jsonResult.tokenData?.access_token) {
    global.lmsCache.set("access-token", jsonResult.tokenData.access_token);
    global.lmsCache.set(
      "access-token-expiry",
      calculateDateSecondsFromNow(jsonResult.tokenData.expires_in)
    );
    return jsonResult.tokenData?.access_token;
  }
  return "";
};

const requestLearnWorlds = async (url: string, method: string, body?: any) => {
  const accessToken = await getAccesToken();
  const requestInit: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  if (!requestInit.headers) {
    requestInit.headers = {};
  }
  (requestInit.headers as Record<string, string>)["Lw-Client"] =
    process.env.LW_CLIENT_ID?.toString() ?? "";
  if (method !== "GET" && body) {
    requestInit.body = JSON.stringify(body);
  }
  const res = await fetch(baseURL + url, requestInit);
  if (res.ok) {
    return res.json();
  }
  return undefined;
};

export const getCourses = async (forceUpdate?: boolean): Promise<Course[]> => {
  if (!forceUpdate) {
    let cachedCourses = global.lmsCache.get<Course[]>("courses");
    if (cachedCourses) {
      return cachedCourses;
    }
  }
  const res = await requestLearnWorlds(`courses`, "GET");
  let courses: Course[] = [];
  if (res && res?.data) {
    courses = res.data;
  }
  if (res && res?.meta) {
    if (res.meta.page < res.meta.totalPages) {
      for (let i = res.meta.page + 1; i <= res.meta.totalPages; i++) {
        const res = await requestLearnWorlds(`courses?page=${i}`, "GET");
        if (res && res?.data) {
          courses = [...courses, ...res.data];
        }
      }
    }
  }
  global.lmsCache.set<Course[]>("courses", courses);
  return courses;
};

/**
 * Get courses of a user
 * @param userId - The user id
 * @returns The courses of the user
 */
export const getLWCoursesOfUser = async (userId: string): Promise<Course[]> => {
  const res = await requestLearnWorlds(`users/${userId}/courses`, "GET");
  return res?.data || [];
};
