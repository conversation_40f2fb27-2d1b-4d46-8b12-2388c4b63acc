import useS<PERSON> from "swr";

import { getCronLogs } from "@/lib/services";
import { CronLogsListResponse, UseFetchCronLogsParams } from "@/types";

/**
 * Custom hook to fetch cron logs.
 *
 * @param params - The parameters for fetching cron logs.
 * @returns The SWR object containing the cron logs data.
 */
export const useFetchCronLogs = (params: UseFetchCronLogsParams) => {
  return useSWR<CronLogsListResponse>(
    ["cron-logs", params.filters],
    () => getCronLogs(params.filters),
    params?.options
  );
};
