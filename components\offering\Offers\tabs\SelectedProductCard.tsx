import { Product } from "@/types";
import { Image, Popover, Typography } from "antd";
import type { JSX } from "react";

const { Text } = Typography;

export const SelectedProductCard = (params: {
  product: Product | undefined;
  selectedBillingCycles: number;
}): JSX.Element => {
  return params.product !== undefined ? (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width: "100%",
        backgroundColor: "white",
        border: "1px solid lightgray",
        padding: "5px",
        borderRadius: "5px",
      }}
    >
      {" "}
      <div
        style={{
          width: "30%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {" "}
        <Image src={params.product.image} height={50} />{" "}
      </div>{" "}
      <div
        style={{
          width: "70%",
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
          marginLeft: "5px",
          cursor: "default",
        }}
      >
        {" "}
        <Popover title={params.product.externalName}>
          {params.product.externalName}
        </Popover>{" "}
        <br />{" "}
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          {" "}
          {params.selectedBillingCycles} billing cycle(s){" "}
        </Text>{" "}
      </div>{" "}
    </div>
  ) : (
    <div></div>
  );
};
