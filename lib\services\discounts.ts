"use server";

import { currentUser } from "@clerk/nextjs/server";
import {
  CreateDiscount,
  DiscountListResponse,
  SearchDiscountsParams,
} from "@/types";
import { osRequest } from "./utils";

const baseURL = `${process.env.API_URL}/discounts`;

/**
 * Activates a discount by sending a PATCH request to the backend API.
 * @param id - The ID of the discount to activate.
 * @returns A Promise that resolves to the JSON response from the API.
 */
export const activateDiscount = async (id: number): Promise<any> => {
  try {
    const res = await osRequest(`${baseURL}/${id}`, "PATCH", {
      status: "active",
    });

    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};

/**
 * Archives a discount by updating its status to "disabled".
 * @param id - The ID of the discount to be archived.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export const archiveDiscount = async (id: number): Promise<any> => {
  try {
    const res = await osRequest(`${baseURL}/${id}/disable`, "PATCH");

    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};

export type AttachDiscountParams = {
  params: any;
  id: string;
};

/**
 * Attaches a discount to a specific ID.
 * @param params - The parameters for attaching the discount.
 * @param id - The ID to attach the discount to.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export const attachDiscount = async (
  payload: AttachDiscountParams
): Promise<any> => {
  try {
    const res = await osRequest(
      `${baseURL}/${payload.id}/attach`,
      "POST",
      payload.params
    );

    return res;
  } catch (err) {
    console.error("charge result: ", err);
  }
};

/**
 * Creates a discount by sending a POST request to the backend API.
 * @param values - The values to be sent in the request body.
 * @returns A Promise that resolves to the JSON response from the API.
 */
export const createDiscount = async (
  values: CreateDiscount,
  isQuick = false
): Promise<any> => {
  let requestQuery = `${baseURL}`;
  if (isQuick) {
    const userInfo = await currentUser();
    const userName = userInfo?.firstName?.toUpperCase() || "NULL";
    values.name = `CS-${userName}-${values.code}`;
  }
  const res = await osRequest(requestQuery, "POST", values);

  return res;
};

/**
 * Retrieves the discount information for a given discount code.
 * @param code - The discount code to retrieve information for.
 * @returns A Promise that resolves to the discount information.
 */
export const getDiscount = async (code: string): Promise<any> => {
  try {
    const res = await osRequest(`${baseURL}/${code}`, "GET");
    return res;
  } catch (err) {
    console.error("cannot getDiscount: ", err);
  }
};

/**
 * Updates a discount by sending a PATCH request to the backend API.
 * @param params - The parameters to be sent in the request body.
 * @param id - The ID of the discount to be updated.
 * @returns A Promise that resolves to the JSON response from the API.
 */
export const patchDiscount = async (payload: {
  params: any;
  id: number;
}): Promise<any> => {
  try {
    const res = await osRequest(
      `${baseURL}/${payload.id}`,
      "PATCH",
      payload.params
    );
    return res;
  } catch (err) {
    console.error("charge result: ", err);
  }
};

/**
 * Searches for discounts based on the provided parameters.
 * @param params - The search parameters for discounts.
 * @returns A Promise that resolves to the search results.
 */
export const searchDiscounts = async (
  params: SearchDiscountsParams
): Promise<DiscountListResponse> => {
  let requestQuery = new URL(`${baseURL}/search?`);
  const query = new URLSearchParams();
  query.append("order", "DESC");

  const validParams = [
    "name",
    "line",
    "family",
    "plan",
    "product",
    "status",
  ] as const;
  validParams.forEach((param) => {
    if (params[param]) query.append(param, params[param] as string);
  });

  requestQuery.search = query.toString();

  const res = await osRequest(requestQuery.href, "GET");
  return await res;
};
