"use client";

import { Card, Checkbox, InputNumber, Tag, Typography } from "antd";

type ProductConfigProps = {
  itemData: any;
  isMandatory: boolean;
  isChecked: boolean;
  handleProductSelection: Function;
  handleProductQtyChange: Function;
};

const { Text } = Typography;

export default function ProductConfig({
  itemData,
  isMandatory,
  isChecked,
  handleProductSelection,
  handleProductQtyChange,
}: ProductConfigProps) {
  const onChecked = (event: any) => {
    if (isMandatory) return;
    handleProductSelection(itemData, event);
  };

  const onQtyChange = (value: number | null) => {
    handleProductQtyChange(itemData, value as number);
  };

  return (
    <Card
      style={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        marginBottom: 10,
      }}
    >
      <div style={{ display: "flex", flexDirection: "row" }}>
        <Checkbox
          id={"product-config-" + itemData.id}
          checked={isChecked}
          disabled={isMandatory}
          onChange={onChecked}
          style={{ marginRight: 20, transform: "scale(1.5)" }}
        />
        <label
          htmlFor={"product-config-" + itemData.id}
          style={{
            cursor: !isMandatory ? "pointer" : "not-allowed",
            display: "flex",
          }}
        >
          {itemData?.image ? (
            <img
              src={itemData?.image}
              width={32}
              height={32}
              style={{ marginRight: 20 }}
            />
          ) : null}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              marginRight: 20,
            }}
          >
            <Text style={{ marginBottom: 5 }}>{itemData.name}</Text>
            <InputNumber
              type="number"
              size="small"
              min={1}
              onChange={onQtyChange}
              value={itemData.quantity}
              disabled={itemData ? !itemData.canChangeQuantity : false}
            />
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
            }}
          >
            <Tag>{isMandatory ? "Mandatory" : "Recommended"}</Tag>
          </div>
        </label>
      </div>
    </Card>
  );
}
