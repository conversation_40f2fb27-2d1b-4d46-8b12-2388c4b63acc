"use client";

import { But<PERSON>, Col, Typography } from "antd";

const { Text } = Typography;

type FormActionsProps = {
  current: number;
  total: number;
  canAccessNext: () => boolean;
  next: () => void;
  prev: () => void;
};
export default function FormActions({
  canAccessNext,
  current,
  total,
  next,
  prev,
}: FormActionsProps) {
  return (
    <>
      <Col offset={1} span={10} style={{ marginTop: 20 }}>
        <Button onClick={prev} size="large">
          <Text>Go Back</Text>
        </Button>
      </Col>
      <Col offset={1} span={10} style={{ marginTop: 20 }}>
        <Button
          type="primary"
          onClick={next}
          disabled={!canAccessNext()}
          size="large"
        >
          <Text style={{ color: "white" }}>
            {current < total - 1 ? "Go to next step" : "Create subscription"}
          </Text>
        </Button>
      </Col>
    </>
  );
}
