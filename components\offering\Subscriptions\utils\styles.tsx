import { Subscription } from "chargebee";

export function calculateTagBgColorWithStatus(
  status: Subscription["orderStatus"]
) {
  switch (status) {
    case "paid":
      return "darkgreen";
    case "active":
      return "green";
    case "refunded":
      return "orange";
    case "stopped":
      return "darkgray";
    case "overdue":
      return "red";
    default:
      return "gray";
  }
}

export function calculateTagColorWithStatus(status: Subscription["status"]) {
  switch (status) {
    case "active":
      return "#115E3E";
    case "non_renewing":
      return "#D48806";
    case "cancelled":
      return "#CF1322";
    default:
      return "black";
  }
}
