import NodeCache from 'node-cache';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@config';
import { GlobalHelpers } from '@helpers';
import { RequestMethods } from '@types';
import { IDataServices } from '@abstracts';
import {
  PXActionResult,
  ILearnworldsCourseBase,
  ILearnworldsCoursesListResponse,
  ILearnworldsUserResponse,
} from '@px-shared-account/hermes';

@Injectable()
export class LearnworldsService {
  private readonly defaultHeaders: Record<string, string>;
  private accessTokenCache: NodeCache;
  private accessTokenCacheKey: string;
  private readonly apiEndpoint: string;
  private readonly CLIENT_ID: string;
  private readonly CLIENT_SECRET: string;
  private readonly ALREADY_ENROLLED_ERROR = 'Product is already owned by User';

  constructor(
    private readonly dataServices: IDataServices,
    private readonly configService: ConfigService,
  ) {
    this.accessTokenCache = new NodeCache({ deleteOnExpire: true });
    const learnworldsSecrets = this.configService.learnworldsSecrets;
    this.apiEndpoint = learnworldsSecrets.LEARNWORLDS_API_ENDPOINT;
    this.CLIENT_ID = learnworldsSecrets.LEARNWORLDS_CLIENT_ID;
    this.CLIENT_SECRET = learnworldsSecrets.LEARNWORLDS_CLIENT_SECRET;
    this.accessTokenCacheKey = learnworldsSecrets.LW_ACCESS_TOKEN_CACHE_KEY;
    this.defaultHeaders = { 'Lw-Client': this.CLIENT_ID };
  }

  /**
   * Gets the access token from cache if exists, otherwise generates a new one
   * In any case, the `access_token` is always retrieve from the cache, making
   * the cache the only source of truth for getting the token.
   * @returns Learnworlds access token
   */
  async getAccessToken(): Promise<string> {
    if (!this.accessTokenCache.get(this.accessTokenCacheKey)) {
      await this.refreshAccessToken();
    }
    return this.accessTokenCache.get(this.accessTokenCacheKey);
  }

  /**
   * Refreshes the `access_token` if it's expired
   * @returns Newly generated `access_token`
   */
  async refreshAccessToken(): Promise<void> {
    const requestData = {
      grant_type: 'client_credentials',
      client_id: this.CLIENT_ID,
      client_secret: this.CLIENT_SECRET,
    };
    const oAuthUrl = `${this.apiEndpoint}/oauth2/access_token`;
    this.defaultHeaders['Content-Type'] = 'application/x-www-form-urlencoded';
    const result = await GlobalHelpers.makeAxiosRequest(
      oAuthUrl,
      RequestMethods.POST,
      null,
      this.defaultHeaders,
      requestData,
    );
    if (!result?.success) {
      throw new Error(
        `Error refreshing Learnworlds access_token\n: ${JSON.stringify(
          result,
        )}`,
      );
    }
    const { tokenData } = result;
    const accessToken = tokenData.access_token;
    const expiry = tokenData.expires_in;
    this.accessTokenCache.set(this.accessTokenCacheKey, accessToken, expiry);
  }

  /**
   * Gets a user from Learnworlds
   * @param email Email address of the user to enroll
   * @returns A promise that resolves to the user object from Learnworlds
   */
  async getUser(email: string): Promise<PXActionResult> {
    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      const getUserApi = `${this.apiEndpoint}/v2/users/${email}`;
      const result = await GlobalHelpers.makeAxiosRequest(
        getUserApi,
        RequestMethods.GET,
        null,
        this.defaultHeaders,
      );

      return {
        data: !result?.error ? result : null,
        success: result?.id ? true : false,
        message: result?.error ? result?.error?.message : null,
      };
    } catch (err: any) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Creates a user in Learnworlds
   * @param email Email address of the user to create
   * @param customerFirstName First name of the user to create
   * @returns A promise that resolves to the user object from Learnworlds or null
   */
  async createUser(
    email: string,
    customerFirstName: string,
  ): Promise<PXActionResult> {
    const data = {
      email,
      username: customerFirstName,
    };

    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      this.defaultHeaders['Content-Type'] = 'application/json';
      const createUserApi = `${this.apiEndpoint}/v2/users`;
      const result = await GlobalHelpers.makeAxiosRequest(
        createUserApi,
        RequestMethods.POST,
        null,
        this.defaultHeaders,
        data,
      );

      return {
        data: !result?.error ? result : null,
        success: result?.id ? true : false,
        message: result?.error ? result?.error?.message : null,
      };
    } catch (err: any) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Gets a user from Learnworlds or creates a new one if it doesn't exist
   * @param email Email address of the user to get or create
   * @param customerFirstName First name of the user to get or create
   * @returns A promise that resolves to the user object from Learnworlds or null
   */
  async getOrCreateUser(
    email: string,
    customerFirstName: string,
  ): Promise<PXActionResult> {
    try {
      const foundUser = await this.getUser(email);
      if (foundUser?.success) {
        return {
          data: foundUser?.data,
          success: true,
        };
      }
      return await this.createUser(email, customerFirstName?.toLowerCase());
    } catch (err: any) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Enrolls a user in a course
   * @param email Email address of the user to enroll
   * @param customerFirstName First name of the user to enroll - used as username
   * @param courseId Learnworlds course id to enroll user in
   * @param comment Optional comment for the enrollment
   * @param notifyUser Whether to notify the user via email about enrollment or not
   * @returns `true` if the enrollment was successful, otherwise `undefined` or `false`
   */
  async enrollUser(
    email: string,
    customerFirstName: string,
    courseId: string,
    comment?: string,
    notifyUser = false,
  ): Promise<PXActionResult> {
    const user = await this.getOrCreateUser(email, customerFirstName);
    if (!user?.success) {
      return {
        success: false,
        message: `Failed to enroll user in Learnworlds. Unable to get or create user for email: ${email}`,
      };
    }

    await this.dataServices.user.update(
      { email },
      {
        lmsId: user?.data?.id,
      },
    );
    const data = {
      price: 0,
      productId: courseId,
      productType: 'course',
      justification: comment,
      send_enrollment_email: notifyUser,
    };
    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      this.defaultHeaders['Content-Type'] = 'application/json';
      const enrollmentApi = `${this.apiEndpoint}/v2/users/${email}/enrollment`;
      const result = await GlobalHelpers.makeAxiosRequest(
        enrollmentApi,
        RequestMethods.POST,
        null,
        this.defaultHeaders,
        data,
      );
      const isEnrolled = result?.success || false;
      const message = isEnrolled
        ? `Successfully enrolled user in Learnworlds. User email: ${email}`
        : `Failed to enroll user for email: ${email}`;
      return { success: isEnrolled, message };
    } catch (err: any) {
      const error = GlobalHelpers.constructErrorResponse(err);
      if (error?.message === this.ALREADY_ENROLLED_ERROR) {
        return {
          success: true,
          message: `User already enrolled in course: ${courseId}`,
        };
      }
      return error;
    }
  }

  /**
   * UnEnrolls a user from a course
   * @param email Email address of the user to enroll
   * @param courseId Learnworlds course id to enroll user in
   * @returns `true` if the enrollment was successful, otherwise `undefined` or `false`
   */
  async unEnrollUser(email: string, courseId: string): Promise<PXActionResult> {
    const data = {
      productId: courseId,
      productType: 'course',
    };
    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      this.defaultHeaders['Content-Type'] = 'application/json';
      const enrollmentApi = `${this.apiEndpoint}/v2/users/${email}/enrollment`;
      const result = await GlobalHelpers.makeAxiosRequest(
        enrollmentApi,
        RequestMethods.DELETE,
        null,
        this.defaultHeaders,
        data,
      );
      const isUnEnrolled = !result?.error || true;
      const message = isUnEnrolled
        ? `Successfully unenrolled user in Learnworlds. User email: ${email}`
        : `Unable to unenroll user for email: ${email}`;
      return { success: isUnEnrolled, message };
    } catch (err) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Adds tags to a user in Learnworlds
   * @param email Email address of the user to add tags to
   * @param tags List of tags to add to the user
   * @returns A promise that resolves to the list of tags added to the user
   */
  async addTagsToUser(email: string, tags: string[]): Promise<PXActionResult> {
    const data = {
      tags,
      action: 'attach',
    };

    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      this.defaultHeaders['Content-Type'] = 'application/json';
      const enrollmentApi = `${this.apiEndpoint}/v2/users/${email}/tags`;
      const result = await GlobalHelpers.makeAxiosRequest(
        enrollmentApi,
        RequestMethods.POST,
        null,
        this.defaultHeaders,
        data,
      );
      const isTagged = result?.id ? true : false;
      return { success: isTagged };
    } catch (err) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Removes tags from a user in Learnworlds
   * @param email Email address of the user to remove tags from
   * @param tags List of tags to remove from the user
   * @returns A promise that resolves to the list of tags removed from the user
   */
  async removeTagsFromUser(
    email: string,
    tags: string[],
  ): Promise<PXActionResult> {
    const data = {
      tags,
      action: 'detach',
    };

    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      this.defaultHeaders['Content-Type'] = 'application/json';
      const enrollmentApi = `${this.apiEndpoint}/v2/users/${email}/tags`;
      const result = await GlobalHelpers.makeAxiosRequest(
        enrollmentApi,
        RequestMethods.POST,
        null,
        this.defaultHeaders,
        data,
      );
      const isUnTagged = result?.id ? true : false;
      return { success: isUnTagged };
    } catch (err) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Suspends a user in Learnworlds
   * @param email Email address of the user to suspend
   * @returns A promise that resolves to the suspended user
   **/
  async suspendUser(email: string): Promise<PXActionResult> {
    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      this.defaultHeaders['Content-Type'] = 'application/json';
      const enrollmentApi = `${this.apiEndpoint}/v2/users/${email}/suspend`;
      const result = await GlobalHelpers.makeAxiosRequest(
        enrollmentApi,
        RequestMethods.PUT,
        null,
        this.defaultHeaders,
      );
      const isSuspended = result?.id ? true : false;
      const message = isSuspended
        ? `Successfully suspended user in Learnworlds. User email: ${email}`
        : `Failed to suspend user for email: ${email}`;
      return { success: isSuspended, message };
    } catch (err) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Unsuspends a user in Learnworlds
   * @param email Email address of the user to unsuspend
   * @returns A promise that resolves to the unsuspended user
   */
  async unsuspendUser(email: string): Promise<any> {
    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
      this.defaultHeaders['Content-Type'] = 'application/json';
      const enrollmentApi = `${this.apiEndpoint}/v2/users/${email}/unsuspend`;
      const result = await GlobalHelpers.makeAxiosRequest(
        enrollmentApi,
        RequestMethods.PUT,
        null,
        this.defaultHeaders,
      );
      const isUnSuspended = result?.id ? true : false;
      const message = isUnSuspended
        ? `Successfully unsuspended user in Learnworlds. User email: ${email}`
        : `Failed to unsuspend user for email: ${email}`;
      return { success: isUnSuspended, error: !isUnSuspended, message };
    } catch (err) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }

  /**
   * Gets all courses from Learnworlds with pagination support
   * @param forceRefresh Whether to bypass cache and fetch fresh data
   * @param page Specific page to fetch (optional, if not provided fetches all pages)
   * @param limit Number of courses per page (optional)
   * @returns A promise that resolves to the list of courses from Learnworlds
   */
  async getCourses(forceRefresh = false, page?: number, limit?: number): Promise<ILearnworldsCoursesListResponse> {
    try {
      const token = await this.getAccessToken();
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;

      let allCourses = [];

      if (page) {
        // Fetch specific page
        let getCoursesApi = `${this.apiEndpoint}/v2/courses?page=${page}`;
        if (limit) {
          getCoursesApi += `&limit=${limit}`;
        }

        const result = await GlobalHelpers.makeAxiosRequest(
          getCoursesApi,
          RequestMethods.GET,
          null,
          this.defaultHeaders,
        );

        if (result?.error) {
          return {
            data: [],
            page: page || 1,
            total: 0,
          };
        }

        return {
          data: result?.data || [],
          page: page || 1,
          total: result?.meta?.total || (result?.data || []).length,
        };
      } else {
        // Fetch all pages (existing behavior)
        let currentPage = 1;
        let hasMorePages = true;

        while (hasMorePages) {
          let getCoursesApi = `${this.apiEndpoint}/v2/courses?page=${currentPage}`;
          if (limit) {
            getCoursesApi += `&limit=${limit}`;
          }

          const result = await GlobalHelpers.makeAxiosRequest(
            getCoursesApi,
            RequestMethods.GET,
            null,
            this.defaultHeaders,
          );

          if (result?.error) {
            return {
              data: [],
              page: 1,
              total: 0,
            };
          }

          if (result?.data) {
            allCourses = [...allCourses, ...result.data];
          }

          // Check if there are more pages
          if (result?.meta && result.meta.page < result.meta.totalPages) {
            currentPage++;
          } else {
            hasMorePages = false;
          }
        }

        return {
          data: allCourses,
          success: true,
          message: `Successfully fetched ${allCourses.length} courses from Learnworlds`,
        };
      }
    } catch (err: any) {
      return GlobalHelpers.constructErrorResponse(err);
    }
  }
}
