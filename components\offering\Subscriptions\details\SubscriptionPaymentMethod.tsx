"use client";

import { <PERSON><PERSON>, <PERSON>, Col, Row } from "antd";
import { Card as CardDetails, Customer } from "chargebee";

type SubscriptionPaymentMethodProps = {
  autoCollection: string;
  card: CardDetails;
  customer: Customer;
  handleChange: () => void;
};

const renderMethod = (
  autoCollection: string,
  card: CardDetails,
  customer: Customer
) => {
  if (
    autoCollection === "off" ||
    customer?.offline_payment_method === "bank_transfer"
  ) {
    return "Bank Transfer";
  }
  if (!card) {
    return "N/A";
  }
  if (!autoCollection || autoCollection === "on" || card) {
    return (
      <>
        Card {card.card_type?.toUpperCase()} {card.masked_number}
      </>
    );
  }

  return `Unknown: ${autoCollection}`;
};

export default function SubscriptionPaymentMethod({
  autoCollection,
  card,
  customer,
  handleChange,
}: SubscriptionPaymentMethodProps) {
  return (
    <Card
      title="Payment method"
      extra={
        <Button type="link" onClick={handleChange}>
          Change
        </Button>
      }
      style={{ width: "100%" }}
    >
      <>
        <Row gutter={[16, 32]}>
          <Col span={12}>Payment method</Col>
          <Col span={12}>{renderMethod(autoCollection, card, customer)}</Col>
        </Row>
        {card
          ? (!autoCollection || autoCollection === "on") && (
              <>
                <Row gutter={[16, 32]}>
                  <Col span={12}>Expires</Col>
                  <Col span={12}>
                    {card?.expiry_month} / {card?.expiry_year}
                  </Col>
                </Row>
                <Row gutter={[16, 32]}>
                  <Col span={12}>Gateway account</Col>
                  <Col span={12}>{card?.gateway}</Col>
                </Row>
                <Row gutter={[16, 32]}>
                  <Col span={12}>Gateway transaction id</Col>
                  <Col span={12}>{card?.payment_source_id}</Col>
                </Row>
              </>
            )
          : null}
      </>
    </Card>
  );
}
