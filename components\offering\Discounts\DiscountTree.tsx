"use client";

import { InfoCircleTwoTone } from "@ant-design/icons";
import { <PERSON>, Divider, Modal, Row, Tree, Typography } from "antd";
import { useEffect, useState } from "react";

import TreeNode from "./TreeNode";
import { useFetchProductLines } from "@/hooks/product-catalog/productLines";
import { useFetchProducts } from "@/hooks/product-catalog/products";
import { PlanDTO, Product, ProductFamily } from "@/types";

type TreeProps = {
  isModalOpen: boolean;
  handleUpdate: Function;
  handleCancel: Function;
  handleChange: Function;
  checkedItems: any;
};

const { Text } = Typography;

const DiscountTree = ({
  isModalOpen,
  handleUpdate,
  handleCancel,
  handleChange,
  checkedItems,
}: TreeProps) => {
  const [treeData, setTreeData] = useState<any>();

  const { data: products } = useFetchProducts();
  const { data: productLines } = useFetchProductLines();

  const generateProductBranchs = (family: ProductFamily) => {
    if (!products) return [];
    return products.items
      .filter((data) => {
        return data.productFamily.id === family.id;
      })
      .map((prod) => {
        return {
          title: (
            <Text>
              {prod.status === "active" ? (
                prod.externalName
              ) : (
                <Text strong>{prod.externalName + " (Inactive)"}</Text>
              )}
            </Text>
          ),
          key: prod.code,
          children: generatePlanLeafs(prod),
        };
      });
  };

  const generatePlanLeafs = (product: Product) => {
    return product.plans.map((plan: PlanDTO) => {
      return {
        title: (
          <TreeNode
            name={plan.internalName}
            currency="EUR"
            billingCycles={plan.prices[0].totalBillingCycles}
            amount={plan.prices[0].amount}
            disabled={plan.status === "active" ? true : false}
          />
        ),
        key: plan.prices[0].chargebeeId,
        isLeaf: true,
      };
    });
  };

  const handleSelection = (keys: any, e: any) => {
    handleChange(
      e.checkedNodes.filter((item: any) => {
        return item.isLeaf;
      })
    );
  };

  useEffect(() => {
    if (isModalOpen && products && productLines?.items) {
      const treeObj = productLines.items.map((item) => {
        return {
          title: item.name,
          key: item.chargebeeId,
          children: item.families
            ? item.families.map((family) => {
                return {
                  title: family.name,
                  key: family.chargebeeId,
                  children: generateProductBranchs(family),
                };
              })
            : {},
        };
      });
      setTreeData(treeObj);
    }
  }, [productLines]);

  return (
    <Modal
      open={isModalOpen}
      closeIcon={null}
      width="60%"
      onCancel={() => {
        handleCancel();
      }}
      onOk={() => {
        handleUpdate();
      }}
      okText={"Selected " + checkedItems.length + " Plans"}
    >
      <Text strong>Select Plans</Text>
      <Text> Choose the ‘Plans’ you want to add discounts for.</Text>
      <Divider />
      <Row>
        <Col span={1}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <InfoCircleTwoTone style={{ fontSize: 34 }} />
          </div>
        </Col>
        <Col span={22} offset={1}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            When you select a discount at the Product line/Product
            family/Product level, it will select the current selection. If you
            previously set up a discount code at the PXL level and added a new
            product/plan, you’ll need to add this new product/plan to this
            discount manually.
          </div>
        </Col>
      </Row>
      <br />
      <br />
      <div
        style={{
          marginLeft: "10%",
          marginRight: "20px",
        }}
      >
        <Text strong>Plans</Text>
        <br />
        <br />
        <Tree
          treeData={treeData}
          checkable
          onCheck={handleSelection}
          checkedKeys={checkedItems}
          defaultCheckedKeys={checkedItems}
          defaultExpandedKeys={checkedItems}
          height={300}
        />
      </div>
    </Modal>
  );
};

export default DiscountTree;
