import { Product } from "./Products";

export type PlanDTO = {
  internalName: string;
  externalName: string;
  description: string;
  fiscalEntity: string;
  productId?: number;
  id?: number;
  status?: string;
  updatedAt?: string;
  chargebeeId?: string;
  prices: PriceInfo[];
  product?: Product;
  crmSku?: string;
  crmId?: string;
};

export type PlanListResponse = {
  items: PlanDTO[];
  total: number;
  currentPage: number;
};

export type PlanInfo = {
  internalName: string;
  externalName: string;
  description: string;
  fiscalEntity: string;
  productId?: number;
  id?: number;
  crmSku?: string;
};

export type PriceInfo = {
  chargebeeId?: string;
  internalName: string;
  externalName: string;
  description: string;
  amountPerBillingCycle: number;
  amount: number;
  currencyCode: string;
  billingCycle: string;
  totalBillingCycles: number;
  id?: number;
  periodUnit?: string;
};

export type PlanAddDTO = {
  amount?: number;
  amountPerBillingCycle?: number;
  billingCycle?: string;
  externalName: string;
  fiscalEntity: string;
  internalName: string;
  status?: string;
  totalBillingCycles?: number;
  productId?: number;
  description?: string;
  crmSku: string;
};
