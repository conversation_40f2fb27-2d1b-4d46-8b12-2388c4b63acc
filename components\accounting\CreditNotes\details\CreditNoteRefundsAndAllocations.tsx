"use client";

import { Col, Row, Typography } from "antd";
import { useRouter } from "next/navigation";

import { formatCents } from "@/lib/currency";
import { capitalizeWord } from "@/lib/text";
import { CreditNote } from "@/types";
import { formatDateFromUnix } from "@/lib/date";

const { Text } = Typography;

type CreditNoteRefundsAndAllocationsProps = {
  creditNote: CreditNote | undefined;
};

export default function CreditNoteRefundsAndAllocations({
  creditNote,
}: CreditNoteRefundsAndAllocationsProps) {
  const router = useRouter();

  return (
    <div style={{ padding: "20px", border: "1px solid #F5F5F5" }}>
      <Text strong style={{ fontSize: 15 }}>
        Refunds and Allocations
      </Text>
      <br />
      <br />
      {creditNote?.type === "refundable"
        ? creditNote.linkedRefunds &&
          creditNote.linkedRefunds.map((record) => {
            return (
              <Row key={record.txn_id}>
                <Col
                  span={2}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <div className="paradox-tag-green">
                    {capitalizeWord(record.txn_status)}
                  </div>
                </Col>
                <Col
                  span={20}
                  offset={1}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "start",
                  }}
                >
                  <Text strong>
                    {record.applied_amount &&
                      formatCents(record.applied_amount)}
                  </Text>
                  &nbsp; was refunded to&nbsp;
                  <a
                    onClick={() => {
                      router.push(`/dashboard/transactions/${record.txn_id}`);
                    }}
                  >
                    #{record.txn_id}
                  </a>
                  &nbsp;on{" "}
                  {record.applied_at
                    ? formatDateFromUnix(record.applied_at)
                    : ""}
                </Col>
              </Row>
            );
          })
        : creditNote?.allocations &&
          creditNote.allocations.map((record) => {
            return (
              <Row key={record.invoice_id}>
                <Col
                  span={2}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <div className="paradox-tag-green">
                    {capitalizeWord(record.invoice_status)}
                  </div>
                </Col>
                <Col
                  span={20}
                  offset={1}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "start",
                  }}
                >
                  <Text strong>
                    {record.allocated_amount &&
                      formatCents(record.allocated_amount)}
                  </Text>
                  &nbsp; was allocated to&nbsp;
                  <a
                    onClick={() => {
                      router.push(`/dashboard/invoices/${record.invoice_id}`);
                    }}
                  >
                    #{record.invoice_id}
                  </a>
                  &nbsp;on{" "}
                  {record.invoice_date &&
                    formatDateFromUnix(record.invoice_date)}
                </Col>
              </Row>
            );
          })}
    </div>
  );
}
