"use client";

import { App, Form } from "antd";
import { useEffect, useState } from "react";

import {
  buildCommunityIds,
  getCommunityBySpaceId,
  getCurrentSpaceAndCommunityInfo,
  getDefaultCommunities,
  getDefaultCourses,
} from "@/components/offering/Offers/forms/offerAccess/utils";
import { archiveOffer } from "@/lib/services/offers";
import {
  CircleSpaceGroup,
  CommunityInfo,
  CommunityType,
  ConfigureOfferAccessFormProps,
  ConfigureOfferAccessProps,
  Course,
  CourseInfo,
  Offer,
  OfferAccesses,
  Product,
  SpaceInfo,
} from "@/types";
import { getCommunityInfo, getCourses } from "@/lib/services/product-delivery";

type OfferSettingsTabState = {
  editingOfferProducts: boolean;
  editingRedirectIfDisableUrl: boolean;
  editingAccess: boolean;
};

/**
 * Offer settings tab hooks
 * @param offer - offer
 * @param refreshOffer - refresh offer
 * @returns offer settings tab
 */
export const useOfferSettings = (offer: Offer, refreshOffer: Function) => {
  const { message } = App.useApp();
  const [updateRedirectUrlForm] = Form.useForm();
  const [modals, setModals] = useState<OfferSettingsTabState>({
    editingOfferProducts: false,
    editingRedirectIfDisableUrl: false,
    editingAccess: false,
  });

  // To dynamically change the colors and action of the button
  const updateRedirectIfDisabledForm = () => {
    if (modals.editingRedirectIfDisableUrl) {
      updateRedirectUrlForm.submit();
    } else {
      setModals({ ...modals, editingRedirectIfDisableUrl: true });
    }
  };

  const updateRedirectIfDisabledUrl = async (values: any) => {
    if (offer.id) {
      await archiveOffer(offer.id, values.redirectIfDisabled);
      setModals({ ...modals, editingRedirectIfDisableUrl: false });
      message.open({
        type: "success",
        content: "The redirect URL has been updated successfully",
      });
      refreshOffer();
    }
  };

  // This is needed to update the value in the form
  // when an offer is disabled from the disable offer modal
  useEffect(() => {
    updateRedirectUrlForm.setFieldsValue({
      redirectIfDisabled: offer.redirectIfDisabled,
    });
  }, [offer.redirectIfDisabled]);

  return {
    modals,
    updateRedirectUrlForm,
    updateRedirectIfDisabledForm,
    updateRedirectIfDisabledUrl,
    setModals,
  };
};

/**
 * Offer accesses state
 */
type OfferAccessesState = {
  // Main
  selectedCourses: CourseInfo[];
  selectedCommunities: any[];
  selectedSpaces: SpaceInfo[];
  spacesFromCircle: any[];
  coursesFromLW: Course[];
  spaceGroups: any[];
  mainCommunityAccesses: CommunityInfo[];
  // Recommended
  recommendedCourses: CourseInfo[];
  recommendedCommunities: any[];
  recommendedSpaces: SpaceInfo[];
  recommendedCommunityAccesses: CommunityInfo[];
  suspensionRules: boolean;
  suspensionDelay: number;
};

/**
 * Configure offer access form
 * @param params - configure offer access form params
 * @returns configure offer access form
 */
export const useConfigureOfferAccessForm = (
  params: Partial<ConfigureOfferAccessFormProps>
) => {
  const [offerAccesses, setOfferAccesses] = useState<OfferAccessesState>({
    selectedCourses: [],
    recommendedCourses: [],
    recommendedCommunities: [],
    mainCommunityAccesses: [],
    recommendedCommunityAccesses: [],
    suspensionRules: false,
    suspensionDelay: 2,
    selectedCommunities: [],
    selectedSpaces: [],
    spacesFromCircle: [],
    coursesFromLW: [],
    spaceGroups: [],
    recommendedSpaces: [],
  });

  /**
   * Updates the list of courses from LearnWorlds
   */
  const updateCoursesList = async () => {
    const tempCourses = await getCourses(true);
    console.log(tempCourses);
    setOfferAccesses({ ...offerAccesses, coursesFromLW: tempCourses });
    console.log(offerAccesses);
  };

  /**
   * Updates the list of communities from Circle
   */
  const updateCommunityList = async () => {
    const tempComm = await getCommunityInfo(true);
    setOfferAccesses({
      ...offerAccesses,
      spaceGroups: tempComm.spaceGroups,
      spacesFromCircle: tempComm.spaces,
    });
  };

  /**
   * Selects courses for the offer and updates the offer accesses state
   * @param value - The course ids to select
   * @param isRecommended - Whether the courses are recommended
   */
  const selectCourse = (value: string[], isRecommended: boolean) => {
    const selectedCourses: CourseInfo[] = value
      .map((id) =>
        offerAccesses.coursesFromLW.find((course) => course.id === id)
      )
      .filter((course): course is Course => Boolean(course))
      .map(({ id, title }) => ({ id, name: title }));
    const fieldsToUpdate = isRecommended
      ? {
          recommendedCourses: selectedCourses,
        }
      : {
          selectedCourses: selectedCourses,
        };
    setOfferAccesses((prev) => ({
      ...prev,
      ...fieldsToUpdate,
    }));
  };

  /**
   * Removes spaces from the offer and updates the offer accesses state
   * @param value - The space ids to remove
   * @param isRecommended - Whether the spaces are for recommended products
   */
  const removeSpace = (value: number[], isRecommended: boolean) => {
    const spacesFieldName = isRecommended
      ? "recommendedSpaces"
      : "selectedSpaces";
    const communityAccessesFieldName = isRecommended
      ? "recommendedCommunityAccesses"
      : "mainCommunityAccesses";
    const baseSpaces = offerAccesses[spacesFieldName];

    // Filter out the spaces to be removed
    const filteredSpaces = baseSpaces.filter(
      (space) => !value.includes(space.id)
    );

    // Group remaining spaces by community for proper community access structure
    const spacesByCommunity = new Map<CommunityType | null, SpaceInfo[]>();

    filteredSpaces.forEach((space) => {
      const communityType = getCommunityBySpaceId(
        space.id,
        offerAccesses.spacesFromCircle
      );

      if (!spacesByCommunity.has(communityType)) {
        spacesByCommunity.set(communityType, []);
      }

      spacesByCommunity.get(communityType)?.push(space);
    });

    // Create community accesses from the grouped spaces
    const communityAccesses = Array.from(spacesByCommunity.entries())
      .filter(([community]) => community !== null)
      .map(([community, spaces]) => ({
        community: community as CommunityType,
        spaces: spaces,
      }));

    setOfferAccesses((prev) => ({
      ...prev,
      [spacesFieldName]: filteredSpaces,
      [communityAccessesFieldName]: communityAccesses,
    }));
  };

  /**
   * Selects spaces for the offer and updates the offer accesses state
   * @param value - The space ids to select
   * @param isRecommended - Whether the spaces are for recommended products
   */
  const selectSpace = (value: number[], isRecommended: boolean) => {
    const spacesFieldName = isRecommended
      ? "recommendedSpaces"
      : "selectedSpaces";
    const communityAccessesFieldName = isRecommended
      ? "recommendedCommunityAccesses"
      : "mainCommunityAccesses";
    const baseSpaces = offerAccesses[spacesFieldName];

    // Find the new spaces to add
    const newSpacesToAdd: SpaceInfo[] = value
      .map((selectedSpaceId: number) =>
        offerAccesses.spacesFromCircle.find(
          (space: SpaceInfo) => space.id === selectedSpaceId
        )
      )
      .filter((space): space is SpaceInfo => Boolean(space))
      .map(({ id, name, slug }) => ({ id, name, slug }));

    // Combine existing spaces with new ones, ensuring no duplicates
    const updatedSpaces: SpaceInfo[] = [
      ...baseSpaces,
      ...newSpacesToAdd.filter(
        (newSpace) =>
          !baseSpaces.some((existingSpace) => existingSpace.id === newSpace.id)
      ),
    ];

    // Group spaces by community for proper community access structure
    const spacesByCommunity = new Map<CommunityType | null, SpaceInfo[]>();

    updatedSpaces.forEach((space) => {
      const communityType = getCommunityBySpaceId(
        space.id,
        offerAccesses.spacesFromCircle
      );

      if (!spacesByCommunity.has(communityType)) {
        spacesByCommunity.set(communityType, []);
      }

      spacesByCommunity.get(communityType)?.push(space);
    });

    // Create community accesses from the grouped spaces
    const communityAccesses = Array.from(spacesByCommunity.entries())
      .filter(([community]) => community !== null)
      .map(([community, spaces]) => ({
        community: community as CommunityType,
        spaces: spaces,
      }));

    setOfferAccesses((prev) => ({
      ...prev,
      [spacesFieldName]: updatedSpaces,
      [communityAccessesFieldName]: communityAccesses,
    }));
  };

  const fetchCourseAndCommunityData = async () => {
    const [comm, courseResult] = await Promise.all([
      getCommunityInfo(false),
      getCourses(false),
    ]);
    setOfferAccesses({
      ...offerAccesses,
      coursesFromLW: courseResult,
      spaceGroups: comm.spaceGroups,
      spacesFromCircle: comm.spaces,
    });
  };

  const populateCurrentAccesses = (currentAccesses: OfferAccesses) => {
    const { communities: mainCommunities, spaces: mainSpaces } =
      getCurrentSpaceAndCommunityInfo(
        currentAccesses?.communityAccesses?.products || []
      );
    const { communities: recommendedCommunities, spaces: recommendedSpaces } =
      getCurrentSpaceAndCommunityInfo(
        currentAccesses?.communityAccesses?.recommended || []
      );
    const mainCommunityAccesses =
      currentAccesses?.communityAccesses?.products || [];
    const recommendedCommunityAccesses =
      currentAccesses?.communityAccesses?.recommended || [];

    setOfferAccesses((prev) => ({
      ...prev,
      selectedCourses: currentAccesses?.lmsAccesses?.products || [],
      recommendedCourses: currentAccesses?.lmsAccesses?.recommended || [],
      selectedSpaces: mainSpaces,
      recommendedSpaces: recommendedSpaces,
      selectedCommunities: mainCommunities,
      recommendedCommunities: recommendedCommunities,
      mainCommunityAccesses: mainCommunityAccesses,
      recommendedCommunityAccesses: recommendedCommunityAccesses,
    }));
  };

  useEffect(() => {
    // Fetch data from Circle and LearnWorlds
    fetchCourseAndCommunityData().then(() => {
      if (
        params.currentAccesses?.communityAccesses ||
        params.currentAccesses?.lmsAccesses
      ) {
        // If there are current accesses, populate with the current accesses
        populateCurrentAccesses(params.currentAccesses);
      } else {
        // If no current accesses, populate with default values
        if (params.offerProducts) {
          let defaultCourses = getDefaultCourses(params.offerProducts);
          setOfferAccesses((prev) => ({
            ...prev,
            selectedCourses: [
              ...new Map(
                defaultCourses
                  .filter((x) => x !== null && x !== undefined)
                  .map((item) => [item.id, item])
              ).values(),
            ],
          }));
          let defaultCommunities = getDefaultCommunities(params.offerProducts);
          setOfferAccesses((prev) => ({
            ...prev,
            selectedCommunities: defaultCommunities.map((x) => x.community),
          }));
          let spacesFromProducts = params.offerProducts.flatMap(
            (product: Product) => product.communityIds || []
          );
          let defaultSpaces = spacesFromProducts
            .flatMap((comm: CommunityInfo) => comm.spaces || [])
            .filter((space) => space !== undefined) as SpaceInfo[];

          setOfferAccesses((prev) => ({
            ...prev,
            selectedSpaces: [
              ...new Map(defaultSpaces.map((item) => [item.id, item])).values(),
            ],
          }));
        }
        // Init recommended access
        if (
          params.offerRecommendedProducts &&
          params.offerRecommendedProducts.length > 0
        ) {
          let defaultRecommendedCourses = getDefaultCourses(
            params.offerRecommendedProducts
          );
          setOfferAccesses({
            ...offerAccesses,
            recommendedCourses: [
              ...new Map(
                defaultRecommendedCourses.map((item) => [item.id, item])
              ).values(),
            ],
          });

          let defaultRecommendedCommunities = getDefaultCommunities(
            params.offerRecommendedProducts
          );
          setOfferAccesses({
            ...offerAccesses,
            recommendedCommunities: defaultRecommendedCommunities.map(
              (x) => x.community
            ),
          });
          let spacesFromRecommended = params.offerRecommendedProducts.flatMap(
            (product: Product) => product.communityIds || []
          );
          let defaultRecommendedSpaces = spacesFromRecommended
            .flatMap((comm: CommunityInfo) => comm.spaces || [])
            .filter((space) => space !== undefined) as SpaceInfo[];

          setOfferAccesses({
            ...offerAccesses,
            recommendedSpaces: [
              ...new Map(
                defaultRecommendedSpaces.map((item) => [item.id, item])
              ).values(),
            ],
          });
        }

        if (params.suspendable) {
          setOfferAccesses({
            ...offerAccesses,
            suspensionRules: params.suspendable,
          });
        }
        if (params.delayPeriod) {
          setOfferAccesses({
            ...offerAccesses,
            suspensionDelay: params.delayPeriod,
          });
        }
      }
    });
  }, []);

  /**
   * Selects communities for the offer and updates the offer accesses state
   * @param value - The community types to select
   * @param isRecommended - Whether the communities are for recommended products
   */
  const selectCommunity = (value: string[], isRecommended: boolean) => {
    const communitiesFieldName = isRecommended
      ? "recommendedCommunities"
      : "selectedCommunities";

    // Convert string array to CommunityType array
    const selectedCommunities = value.map(
      (communityStr) => communityStr as CommunityType
    );

    setOfferAccesses((prev) => ({
      ...prev,
      [communitiesFieldName]: selectedCommunities,
    }));
  };

  return {
    offerAccesses,
    removeSpace,
    selectCourse,
    selectSpace,
    selectCommunity,
    updateCommunityList,
    updateCoursesList,
    setOfferAccesses,
  };
};

type Modals = {
  addingCourse: boolean;
  setCommunitiesAndCourses: boolean;
};

/**
 * Configure offer access
 * @param params - configure offer access params
 * @returns configure offer access
 */
export const useConfigureOfferAccess = ({
  spaceGroups,
  spaces,
  offerProducts,
}: Partial<ConfigureOfferAccessProps>) => {
  const [modals, setModals] = useState<Modals>({
    addingCourse: false,
    setCommunitiesAndCourses: false,
  });
  const [filtered, setFiltered] = useState({
    spaceGroups: [] as CircleSpaceGroup[],
    spaces: [] as SpaceInfo[],
  });

  useEffect(() => {
    // Skip if space data is not available
    if (!spaceGroups || !spaces) return;

    if (offerProducts) {
      // Get the default communities associated with the offer products
      const defaultCommunities = getDefaultCommunities(offerProducts);

      // Build array of community IDs based on the community types present
      const communityIds = buildCommunityIds(
        defaultCommunities.map((comm: CommunityInfo) => comm.community)
      );

      // Filter space groups and spaces to only show those belonging to the relevant communities
      setFiltered((prev) => ({
        ...prev,
        spaceGroups: spaceGroups.filter((x) =>
          communityIds.includes(x.community_id.toString())
        ),
        spaces: spaces.filter((x) =>
          communityIds.includes(x.community_id.toString())
        ),
      }));
    }
  }, [offerProducts, spaceGroups, spaces]);

  return {
    modals,
    filtered,
    setFiltered,
    setModals,
  };
};
