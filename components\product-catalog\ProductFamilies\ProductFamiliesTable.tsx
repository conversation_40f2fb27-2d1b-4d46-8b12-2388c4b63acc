"use client";

import { App, Table, Typography, Divider } from "antd";
import React, { useEffect, useState } from "react";

import ProductFamiliesFilterTool from "./ProductFamiliesFilterTool";
import CreateProductFamilyForm from "./forms/CreateProductFamilyForm";
import UpdateProductFamilyForm from "./forms/UpdateProductFamilyForm";
import { getProductFamiliesTableColumns } from "./ProductFamiliesTableColumns";
import { useFetchProductFamilies } from "@/hooks/product-catalog/productFamilies";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useProtectPage } from "@/hooks/roles";
import { createProductFamily, patchProductFamily } from "@/lib/services";
import { ProductFamilyFilter } from "@/types";

const { Title } = Typography;

export default function ProductFamiliesTable() {
  const { message } = App.useApp();
  const [filters, setFilters] = useState<ProductFamilyFilter>({
    status: null,
    search: "",
  });
  const searchTerm = useDebounce(filters.search);
  const { canAccess } = useProtectPage("product-families-list");
  const [modalState, setModalState] = useState({
    creatingNewPF: false,
    updatingPF: false,
  });

  // Queries
  const {
    isLoading,
    data: productFamilies,
    mutate: refetchProductFamilies,
  } = useFetchProductFamilies({
    filters: {
      status: filters.status,
      search: searchTerm,
    },
  });

  const handleMessageFlow = async (
    operation: () => Promise<any>,
    loadingMessage: string,
    successMessage: string
  ) => {
    const hideMessage = message.loading(loadingMessage, 0);
    try {
      const res = await operation();
      if (res) {
        hideMessage();
        message.open({
          type: "success",
          content: successMessage,
        });
        await refetchProductFamilies();
      }
      return true;
    } catch (error) {
      console.error(`Error: ${loadingMessage}`, error);
      message.open({
        type: "error",
        content: `Error ${loadingMessage.toLowerCase()}`,
      });
      return false;
    } finally {
      hideMessage();
    }
  };

  const onAddNewProduct = async (values: any) => {
    const success = await handleMessageFlow(
      () =>
        createProductFamily({
          ...values,
          productLine: { id: values.productLine },
        }),
      "Adding new product family",
      "New product family added!"
    );
    if (success) {
      setModalState((prev) => ({ ...prev, creatingNewPF: false }));
    }
  };

  const handleUpdate = async (values: any) => {
    const success = await handleMessageFlow(
      () =>
        patchProductFamily({
          ...values,
          productLine: { id: values.productLine },
        }),
      "Editing product family",
      "Product family updated!"
    );
    if (success) {
      setModalState((prev) => ({ ...prev, updatingPF: false }));
    }
  };

  const columns = getProductFamiliesTableColumns({
    onEdit: (data) => {
      setFilters((prev) => ({ ...prev, current: data }));
      setModalState((prev) => ({ ...prev, updatingPF: true }));
    },
  });

  useEffect(() => {
    refetchProductFamilies();
  }, [filters.status, searchTerm, refetchProductFamilies]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Product Families</Title>
      <ProductFamiliesFilterTool
        filters={filters}
        setFilters={setFilters}
        onCreateProduct={() => {
          if (canAccess("product-family-create")) {
            setModalState((prev) => ({ ...prev, creatingNewPF: true }));
          }
        }}
      />
      <Divider />
      <Table
        dataSource={productFamilies?.items}
        loading={isLoading}
        columns={columns}
        size="small"
        rowKey="id"
        rowClassName={() => {
          return "paradox-table-row";
        }}
      />
      {modalState.creatingNewPF && (
        <CreateProductFamilyForm
          isOpen={modalState.creatingNewPF}
          onAddNewProduct={onAddNewProduct}
          onDismiss={() =>
            setModalState((prev) => ({ ...prev, creatingNewPF: false }))
          }
        />
      )}
      {modalState.updatingPF && (
        <UpdateProductFamilyForm
          isModalOpen={modalState.updatingPF}
          handleUpdate={handleUpdate}
          formValues={filters.current}
          handleCancel={() =>
            setModalState((prev) => ({ ...prev, updatingPF: false }))
          }
        />
      )}
    </div>
  );
}
