"use client";

import { App, Space, Typography, Modal, Divider, Skeleton, Radio } from "antd";
import React, { useEffect, useState } from "react";

import DiscountTree from "@/components/offering/Discounts/DiscountTree";
import UpdateDiscountForm from "@/components/offering/Discounts/forms/UpdateDiscountForm";
import DiscountSettings from "@/components/offering/Discounts/DiscountSettings";
import { useFetchDiscount } from "@/hooks/offering/discounts";
import { useProtectPage } from "@/hooks/roles";
import {
  activateDiscount,
  archiveDiscount,
  attachDiscount,
  AttachDiscountParams,
  patchDiscount,
} from "@/lib/services";
import DiscountDetailsHeader from "./details/DiscountDetailsHeader";

type DiscountDetailsProps = {
  discountId: string;
};

const { Text } = Typography;

const DiscountDetails = (params: DiscountDetailsProps) => {
  if (!params.discountId) {
    throw new Error("Invalid discount ID");
  }
  const { message } = App.useApp();
  const { canAccess } = useProtectPage("discount-details");

  const [pageState, setPageState] = useState({
    isModalOpen: false,
    isEditModalOpen: false,
    plansModalVisible: false,
    selectedPlans: [],
    applyPlans: "",
  });

  // Queries
  const {
    isLoading,
    data: discount,
    mutate: refetchDiscount,
  } = useFetchDiscount(params.discountId);

  const getAdditionalData = async () => {
    const mapped = discount?.pricesAttachedTo.map((item: any) => {
      return item.chargebeeId;
    });
    setPageState({
      ...pageState,
      selectedPlans: mapped,
    });
    if (discount?.attachedTo !== null) {
      setPageState({
        ...pageState,
        applyPlans: discount?.attachedTo[0].itemIds[0],
      });
    }
  };

  const handleChangeDiscountPlans = (values: any) => {
    setPageState({
      ...pageState,
      selectedPlans: values.map((item: any) => {
        return item.key;
      }),
    });
  };

  const handleUpdateDiscountPlans = async () => {
    let planIDs: any = [
      {
        itemType: "plan",
        itemIds: [],
      },
    ];
    if (pageState.selectedPlans && pageState.selectedPlans.length > 0) {
      planIDs[0].itemIds = pageState.selectedPlans;
    }
    await requestAttachDiscount({
      id: params.discountId,
      params: planIDs,
    });
  };

  const updateDiscount = async (values: any) => {
    let updatedDiscount = {
      id: values.id,
      name: values.name,
      status: values.status,
      invoiceName: values.invoiceName,
      applyOn: values.applyOn,
      validUntil:
        values.validUntil && new Date(values.validUntil).toISOString(),
      maxRedemptions: values.maxRedemptions,
      currency: "EUR",
    };
    let result = await patchDiscount({
      params: updatedDiscount,
      id: values.id,
    });
    if (result) {
      message.open({
        type: "success",
        content: "Discount updated!",
      });
    }
    setPageState({
      ...pageState,
      isEditModalOpen: false,
    });
    await refetchDiscount();
  };

  const archiveDisc = async () => {
    if (canAccess("discount-archive")) {
      if (discount) {
        await archiveDiscount(Number(params.discountId));
        await refetchDiscount();
      }
    }
  };

  const activateDisc = async () => {
    if (canAccess("discount-activate")) {
      if (discount) {
        await activateDiscount(Number(params.discountId));
        await refetchDiscount();
      }
    }
  };

  const requestAttachDiscount = async (params: AttachDiscountParams) => {
    let res = await attachDiscount(params);
    message.open({
      type: "success",
      content: "Plans updated!",
    });
    setPageState({
      ...pageState,
      isModalOpen: false,
      plansModalVisible: false,
    });
    await refetchDiscount();
    return res;
  };

  const handleRadioChange = (value: any) => {
    if (value.target.value === 3) {
      setPageState({
        ...pageState,
        applyPlans: "specific",
      });
    }
    if (value.target.value === 1) {
      setPageState({
        ...pageState,
        applyPlans: "all",
      });
    }
    if (value.target.value === 2) {
      setPageState({
        ...pageState,
        applyPlans: "none",
      });
    }
  };

  useEffect(() => {
    getAdditionalData();
  }, [discount?.id]);

  return isLoading ? (
    <Skeleton />
  ) : (
    <div>
      <DiscountDetailsHeader
        discount={discount}
        onActivate={activateDisc}
        onArchive={archiveDisc}
        onEdit={() => {
          if (canAccess("discount-update")) {
            setPageState({
              ...pageState,
              isEditModalOpen: true,
            });
          }
        }}
      />
      <Divider />
      <DiscountSettings
        discount={discount}
        showModal={() =>
          setPageState({
            ...pageState,
            isModalOpen: true,
          })
        }
      />
      {pageState.isModalOpen && (
        <Modal
          open={pageState.isModalOpen}
          closeIcon={null}
          width="50%"
          onCancel={() =>
            setPageState({
              ...pageState,
              isModalOpen: false,
            })
          }
          okText={pageState.applyPlans === "specific" ? "Select Plans" : "Save"}
          onOk={async () => {
            if (pageState.applyPlans === "specific") {
              setPageState({
                ...pageState,
                plansModalVisible: true,
              });
            }
            if (pageState.applyPlans === "all") {
              await requestAttachDiscount({
                params: [
                  {
                    itemType: "plan",
                    itemIds: ["all"],
                  },
                ],
                id: params.discountId,
              });
            }
            if (pageState.applyPlans === "none") {
              await requestAttachDiscount({
                params: [
                  {
                    itemType: "plan",
                    itemIds: ["none"],
                  },
                ],
                id: params.discountId,
              });
            }
          }}
        >
          <Text strong>Select Plans</Text>
          <Text> Choose the ‘Plans’ you want to add discounts for.</Text>
          <Divider />
          <Radio.Group
            onChange={handleRadioChange}
            defaultValue={
              discount?.attachedTo !== null &&
              discount?.attachedTo[0].itemIds[0] === "specific"
                ? 3
                : discount?.attachedTo !== null &&
                    discount?.attachedTo[0].itemIds[0] === "all"
                  ? 1
                  : 2
            }
          >
            <Space direction="vertical" style={{ lineHeight: "40px" }}>
              <Radio value={1}>
                All plans are applicable &nbsp;&nbsp;
                <Text style={{ color: "#BFBFBF" }}>
                  This discount is available on all the plans (existing and
                  future ones)
                </Text>
              </Radio>
              <Radio value={2}>
                No plans are applicable &nbsp;&nbsp;
                <Text style={{ color: "#BFBFBF" }}>
                  This discount won’t be available on all the plans (existing
                  and future ones)
                </Text>
              </Radio>
              <Radio value={3}>
                Specific plans are applicable &nbsp;&nbsp;
                <Text style={{ color: "#BFBFBF" }}>
                  You will be able to select on which plans this discount is
                  available
                </Text>
              </Radio>
            </Space>
          </Radio.Group>
        </Modal>
      )}
      {pageState.plansModalVisible ? (
        <DiscountTree
          isModalOpen={pageState.plansModalVisible}
          handleUpdate={handleUpdateDiscountPlans}
          handleCancel={() =>
            setPageState({
              ...pageState,
              plansModalVisible: false,
            })
          }
          handleChange={handleChangeDiscountPlans}
          checkedItems={pageState.selectedPlans}
        />
      ) : null}
      {discount && pageState.isEditModalOpen ? (
        <UpdateDiscountForm
          isModalOpen={pageState.isEditModalOpen}
          handleAdd={updateDiscount}
          handleCancel={() =>
            setPageState({
              ...pageState,
              isEditModalOpen: false,
            })
          }
          data={discount}
        />
      ) : null}
    </div>
  );
};

export default DiscountDetails;
