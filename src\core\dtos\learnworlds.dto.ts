import { createZodDto } from 'nestjs-zod';
import {
  GetLearnworldsCoursesParamsSchema,
  LearnworldsCoursesResponseSchema,
  LearnworldsCourseBaseSchema,
  LearnworldsEnrollmentParamsSchema,
  LearnworldsUnenrollmentParamsSchema,
  LearnworldsUserTagsParamsSchema,
  LearnworldsUserSuspensionParamsSchema,
  LearnworldsUserResponseSchema,
  CreateLearnworldsUserParamsSchema,
  GetLearnworldsUserParamsSchema,
} from '@px-shared-account/hermes';

// Query parameters DTOs
export class GetLearnworldsCoursesParamsDto extends createZodDto(
  GetLearnworldsCoursesParamsSchema,
) {}

// Response DTOs
export class LearnworldsCoursesResponseDto extends createZodDto(
  LearnworldsCoursesResponseSchema,
) {}

export class LearnworldsCourseDto extends createZodDto(
  LearnworldsCourseBaseSchema,
) {}

export class LearnworldsUserResponseDto extends createZodDto(
  LearnworldsUserResponseSchema,
) {}

// User management DTOs
export class LearnworldsEnrollmentParamsDto extends createZodDto(
  LearnworldsEnrollmentParamsSchema,
) {}

export class LearnworldsUnenrollmentParamsDto extends createZodDto(
  LearnworldsUnenrollmentParamsSchema,
) {}

export class LearnworldsUserTagsParamsDto extends createZodDto(
  LearnworldsUserTagsParamsSchema,
) {}

export class LearnworldsUserSuspensionParamsDto extends createZodDto(
  LearnworldsUserSuspensionParamsSchema,
) {}

export class CreateLearnworldsUserParamsDto extends createZodDto(
  CreateLearnworldsUserParamsSchema,
) {}

export class GetLearnworldsUserParamsDto extends createZodDto(
  GetLearnworldsUserParamsSchema,
) {}
