"use client";

import { Card, Col, Row, Space, Table, Tag, Typography } from "antd";
import { Subscription } from "chargebee";
import { useEffect, useState } from "react";

import {
  computeAdjustment,
  computeDiscount,
  computeRefund,
  computeSubtotal,
  computeTax,
  computeTotal,
} from "@/core";
import { formatCents } from "@/lib/currency";
import {
  SubscriptionHistoricalData,
  InvoiceLineItemPX,
  PX_Subscription,
} from "@/types";

const { Text } = Typography;

type SubscriptionSummaryProps = {
  lineItems: InvoiceLineItemPX[];
  subscription: PX_Subscription;
  history: SubscriptionHistoricalData;
  billingCycle: number;
  isForever: boolean;
  billingPeriod: string;
};

type Details = {
  subtotal: number;
  discount: number;
  refund: number;
  adjusted: number;
  taxes: number;
  total: number;
};

const computeDetails = (
  lineItems: InvoiceLineItemPX[],
  history: SubscriptionHistoricalData,
  details: Details,
  billingCycles: number,
  isForever: boolean
) => {
  const { creditNotes } = history;

  if (!lineItems?.length) return null;

  const totalBillingCycles = isForever ? 1 : billingCycles;

  return {
    subtotal: computeSubtotal(lineItems, totalBillingCycles),
    discount: computeDiscount(lineItems, totalBillingCycles),
    refund: computeRefund(creditNotes),
    adjusted: computeAdjustment(creditNotes),
    taxes: computeTax(lineItems, totalBillingCycles),
    total: computeTotal(lineItems, totalBillingCycles),
  };
};

export default function SubscriptionSummary({
  lineItems,
  subscription,
  history,
  billingCycle,
  isForever,
  billingPeriod,
}: SubscriptionSummaryProps) {
  const [details, setDetails] = useState<Details>({
    subtotal: 0,
    discount: 0,
    refund: 0,
    adjusted: 0,
    taxes: 0,
    total: 0,
  });

  useEffect(() => {
    const computedDetails = computeDetails(
      lineItems,
      history,
      details,
      billingCycle,
      isForever
    );
    if (computedDetails) {
      setDetails(computedDetails);
    }
  }, [
    history && history?.invoices?.length,
    subscription?.id,
    lineItems.length,
  ]);

  const columns = [
    {
      title: "Product",
      dataIndex: "description",
    },
    {
      title: "Billing cycles",
      dataIndex: "billing_cycles",
      render: (text: string, record: any) => (
        <span>{isForever ? billingPeriod : billingCycle}</span>
      ),
    },
    {
      title: "Units",
      dataIndex: "quantity",
    },
    {
      title: "Subtotal",
      render: (text: number, record: any) => (
        <span>{formatCents(record.amount)}</span>
      ),
    },
    {
      title: "Discount",
      render: (text: number, record: InvoiceLineItemPX) => (
        <span>{formatCents(record.discount_amount as number)}</span>
      ),
    },
    {
      title: "Total",
      render: (text: number, record: InvoiceLineItemPX) => (
        <span>
          {formatCents(
            (record.amount as number) - (record.discount_amount || 0)
          )}
        </span>
      ),
    },
    {
      title: "# per billing cycle",
      render: (_: number, record: InvoiceLineItemPX) => (
        <span>
          {formatCents(
            (record.amount as number) - (record.discount_amount || 0)
          )}
        </span>
      ),
    },
  ];

  if (!history || !lineItems?.length) return null;

  return (
    <Card
      title="Subscription summary"
      extra={
        <Space direction="horizontal">
          <Tag>{lineItems?.length} products</Tag>
          <Tag>
            {billingCycle ? billingCycle : isForever ? "Forever" : "?"}
            &nbsp;billing cycles
          </Tag>
          {!isForever && (
            <Tag>
              {subscription.remainingBillingCycles || 0} billing cycles
              remaining
            </Tag>
          )}
          <Tag>{subscription.currencyCode}</Tag>
          <Tag> {subscription.businessEntityId}</Tag>
          <Tag>per {billingPeriod}</Tag>
        </Space>
      }
      style={{ width: "100%" }}
    >
      <Table
        columns={columns}
        dataSource={lineItems}
        rowKey="entity_id"
        pagination={false}
        size="small"
      />
      <Space
        direction="vertical"
        style={{
          display: "flex",
          width: 200,
          marginLeft: "auto",
          marginTop: "20px",
        }}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text>Subtotal</Text>
          </Col>
          <Col span={12}>
            <Text>{formatCents(details.subtotal)}</Text>
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text>Discount</Text>
          </Col>
          <Col span={12}>
            <Text>{formatCents(details.discount)}</Text>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text>Taxes</Text>
          </Col>
          <Col span={12}>
            <Text>{formatCents(details.taxes)}</Text>
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text strong>Total amount</Text>
          </Col>
          <Col span={12}>
            <Text strong>{formatCents(details.total)}</Text>
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text>Refund</Text>
          </Col>
          <Col span={12}>
            <Text>{formatCents(details.refund)}</Text>
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text>Adjusted</Text>
          </Col>
          <Col span={12}>
            <Text>{formatCents(details.adjusted)}</Text>
          </Col>
        </Row>
      </Space>
    </Card>
  );
}
