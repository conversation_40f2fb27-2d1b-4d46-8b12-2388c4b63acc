import { EditNextBillingDateParams } from "@/types";
import { editNextBillingDate, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the POST request to edit next billing date of a subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function POST(req: any) {
  try {
    const body = (await req.json()) as EditNextBillingDateParams;
    const response = await editNextBillingDate(body);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_EDIT_NEXT_BILLING_DATE", details: error },
      { status: 400 }
    );
  }
}
