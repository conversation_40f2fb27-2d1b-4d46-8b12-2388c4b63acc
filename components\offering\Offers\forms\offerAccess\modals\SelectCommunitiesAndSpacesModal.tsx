"use client";

import { Check<PERSON>, Divider, Modal, TreeSelect, Typography } from "antd";
import { useMemo, useState } from "react";

import { prepareTreeData } from "../utils";
import { CircleSpaceGroup, CommunityType, SpaceInfo } from "@/types";

const { Text } = Typography;

type SelectCommunitiesAndSpacesModalProps = {
  isOpen: boolean;
  communities: CommunityType[];
  spaces: SpaceInfo[];
  spaceGroups: CircleSpaceGroup[];
  selectedSpaces: SpaceInfo[];
  onClose: () => void;
  onSelectCommunity: (value: string[]) => void;
  onSelectSpace: (value: number[]) => void;
  onRemoveSpace: (value: number[]) => void;
};

export default function SelectCommunitiesAndSpacesModal({
  isOpen,
  communities,
  spaces,
  spaceGroups,
  selectedSpaces,
  onClose,
  onSelectCommunity,
  onSelectSpace,
  onRemoveSpace,
}: SelectCommunitiesAndSpacesModalProps) {
  const communityOptions = useMemo(() => {
    return [
      {
        value: "PXL",
        label: "PXL",
        id: process.env.NEXT_PUBLIC_PXL_COMMUNITY_ID,
      },
      {
        value: "PXS",
        label: "PXS",
        id: process.env.NEXT_PUBLIC_PXS_COMMUNITY_ID,
      },
    ];
  }, []);
  const [searchValue, setSearchValue] = useState("");

  const treeData = useMemo(() => {
    return prepareTreeData({
      communities,
      communityOptions,
      spaces,
      spaceGroups,
      selectedSpaces,
      searchValue: searchValue,
    });
  }, [
    communities,
    communityOptions,
    spaces,
    spaceGroups,
    selectedSpaces,
    searchValue,
  ]);

  return (
    <Modal
      open={isOpen}
      title="Select communities and spaces"
      onCancel={onClose}
      onOk={() => {
        onClose();
      }}
    >
      <div style={{ width: "100%" }}>
        <Text style={{ fontWeight: "bold" }}>Communities&nbsp;</Text>
        <Checkbox.Group
          options={communityOptions}
          defaultValue={communities}
          onChange={(value) => {
            onSelectCommunity(value);
          }}
        />
      </div>
      <Divider />
      <div style={{ width: "100%" }}>
        <Text style={{ fontWeight: "bold" }}>Spaces&nbsp;</Text>
        <TreeSelect
          treeData={treeData}
          style={{ width: "100%" }}
          value={selectedSpaces.map((space) => space.id)}
          treeLine
          multiple
          treeCheckable
          placeholder="Select communities and spaces"
          showSearch
          searchValue={searchValue}
          onChange={(value) => {
            onSelectSpace(value.map((v) => +v));
          }}
          onDeselect={(value) => {
            onRemoveSpace([+value]);
          }}
          onSearch={(value) => {
            setSearchValue(value);
          }}
        />
      </div>
    </Modal>
  );
}
