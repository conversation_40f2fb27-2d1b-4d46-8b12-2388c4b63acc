"use client";

import { SignUp as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useAuth } from "@clerk/nextjs";
import { Flex } from "antd";
import { redirect } from "next/navigation";

export default function SignUp() {
  const { isSignedIn } = useAuth();
  if (isSignedIn) {
    redirect("/dashboard");
  } else {
    return (
      <Flex justify="center" align="center" style={{ height: "100%" }}>
        <SignUpClerk />
      </Flex>
    );
  }
}
