"use client";

import { Affix, Space } from "antd";

import { Invoice, PX_Subscription } from "@/types";
import SideMenuActionItem from "@/components/core/SideMenuActionItem";

type InvoiceSideMenuProps = {
  invoice: Invoice;
  subscription: PX_Subscription | undefined;
  showOfflineRefund: boolean;
  showRefund: boolean;
  hasCardPayment: boolean;
  setCustomerAddressModalVisible: (visible: boolean) => void;
  setCollectNowModalVisible: (visible: boolean) => void;
  setOfflineRefundModalVisible: (visible: boolean) => void;
  setRefundModalVisible: (visible: boolean) => void;
  setWriteOffModalVisible: (visible: boolean) => void;
};

export const InvoiceSideMenu = ({
  invoice,
  subscription,
  showOfflineRefund,
  showRefund,
  hasCardPayment,
  setCustomerAddressModalVisible,
  setCollectNowModalVisible,
  setOfflineRefundModalVisible,
  setRefundModalVisible,
  setWriteOffModalVisible,
}: InvoiceSideMenuProps) => {
  return (
    <Affix offsetTop={150}>
      <Space direction="vertical" style={{ padding: "0px 10px" }}>
        <SideMenuActionItem
          title="Update address"
          description="Update billing or shipping address for the current invoice"
          onClick={() => setCustomerAddressModalVisible(true)}
          permission="invoice-update-address"
        />

        <SideMenuActionItem
          title="Collect now"
          description="Retry to collect this invoice on customer payment method"
          onClick={() => setCollectNowModalVisible(true)}
          permission="invoice-collect-now"
          disabled={
            subscription?.autoCollection === "off" || invoice.amountDue === 0
          }
          tooltipTitle={
            subscription?.autoCollection === "off"
              ? "If you want to collect now, first change payment method for this subscription to 'Online Payment'"
              : invoice.amountDue === 0
                ? "Cannot collect payment as there is no amount due"
                : undefined
          }
        />

        <SideMenuActionItem
          title="Record an offline refund"
          description="Create an offline refund if you have already manually refunded the invoice to the customer."
          onClick={() => setOfflineRefundModalVisible(true)}
          disabled={!showOfflineRefund}
          permission="invoice-record-offline-refund"
          tooltipTitle={
            !showOfflineRefund
              ? "You can't record an offline refund because the invoice is not due"
              : undefined
          }
        />

        <SideMenuActionItem
          title="Issue a refund"
          description="Refund this invoice to the Customer's payment method"
          disabled={!showRefund || !hasCardPayment}
          onClick={() => setRefundModalVisible(true)}
          permission="invoice-issue-refund"
          tooltipTitle={
            !showRefund
              ? "You can't issue a refund because the invoice is not eligible for refund"
              : !hasCardPayment
                ? "You can't issue a refund because there is no card payment associated with this invoice"
                : undefined
          }
        />

        <SideMenuActionItem
          title="Void Invoice with Credit Note"
          description="If the invoice due amount was not collected even after multiple attempts"
          disabled={invoice.amountDue === 0}
          onClick={() => setWriteOffModalVisible(true)}
          permission="invoice-write-off"
          tooltipTitle={
            invoice.amountDue === 0
              ? "Cannot write off payment as there is no amount due"
              : undefined
          }
        />
      </Space>
    </Affix>
  );
};
