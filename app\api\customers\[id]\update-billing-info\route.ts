import { updateBillingInfo, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

export async function POST(req: any) {
  try {
    const body = await req.json();
    const { id } = body;
    const response = await updateBillingInfo(id, body);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_UPDATE_BILLING_INFO", details: error },
      { status: 400 }
    );
  }
}
