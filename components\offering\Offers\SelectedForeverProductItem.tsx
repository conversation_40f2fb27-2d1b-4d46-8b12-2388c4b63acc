"use client";

import { PlanDTO, Product } from "@/types";
import { Image, Popover, Typography } from "antd";
import { SelectedPlanItem } from "./SelectedPlanItem";
import { SelectedForeverPlanItem } from "./SelectedForeverPlanItem";
import { EmptyPlanItem } from "./type";

const { Text } = Typography;

const SelectedForeverProductItem = (params: {
  product: Product;
  selectedMonthlyPlan?: PlanDTO;
  selectedYearlyPlan?: PlanDTO;
  handlePlanChanged: Function;
}) => {
  const handleSelectedPlanChange = (
    periodUnit: string,
    planId: number | string
  ) => {
    params.handlePlanChanged(periodUnit, planId.toString());
  };

  return (
    <div
      key={params.product.id}
      style={{
        width: "100%",
        maxWidth: "400px",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
          backgroundColor: "white",
          border: "1px solid lightgray",
          padding: "5px",
          borderRadius: "5px",
        }}
      >
        <div
          style={{
            width: "30%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Image src={params.product.image} height={50} />
        </div>
        <div
          style={{
            width: "70%",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            marginLeft: "5px",
            cursor: "default",
          }}
        >
          <Popover title={params.product.externalName}>
            {params.product.externalName}
          </Popover>{" "}
          <br />
          <Text style={{ fontSize: 13, color: "darkgray" }}>
            {params.product?.plans?.length} Plans
          </Text>
        </div>
      </div>
      <br />
      <br />
      {params.selectedMonthlyPlan ? (
        <SelectedForeverPlanItem
          plans={[
            ...params.product.plans.filter(
              (x) => x.prices[0].periodUnit === "monthly-forever"
            ),
            EmptyPlanItem,
          ]}
          billingCycle={"monthly-forever"}
          image={params.product.image}
          onChange={handleSelectedPlanChange}
          selectedPlan={params.selectedMonthlyPlan.chargebeeId}
        />
      ) : null}
      {params.selectedYearlyPlan ? (
        <SelectedForeverPlanItem
          plans={[
            ...params.product.plans.filter(
              (x) => x.prices[0].periodUnit === "yearly-forever"
            ),
            EmptyPlanItem,
          ]}
          billingCycle={"yearly-forever"}
          image={params.product.image}
          onChange={handleSelectedPlanChange}
          selectedPlan={params.selectedYearlyPlan.chargebeeId}
        />
      ) : null}
    </div>
  );
};

export default SelectedForeverProductItem;
