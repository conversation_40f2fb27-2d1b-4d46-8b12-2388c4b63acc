"use client";

import { Descriptions, Typography } from "antd";

import { Invoice } from "@/types";

const { Text } = Typography;

type InvoiceAddressProps = {
  invoice: Invoice;
};

const InvoiceAddress = ({ invoice }: InvoiceAddressProps) => {
  return (
    <Descriptions
      title={
        <>
          <span>Addresses</span>
          <br />
          <small style={{ color: "gray", whiteSpace: "break-spaces" }}>
            Updating billing information only changes the addresses on this
            invoice; it does not affect the billing or shipping address for the
            subscription.
          </small>
        </>
      }
      bordered
      column={1}
      size="small"
    >
      <Descriptions.Item label="Billing address">
        <pre>
          <Text strong>
            {invoice?.billingAddress?.first_name}&nbsp;
            {invoice?.billingAddress?.last_name}
          </Text>
          &nbsp;
          <br />
          {invoice?.billingAddress?.email && (
            <Text style={{ color: "gray", fontSize: 13 }} copyable>
              {invoice?.billingAddress?.email}
            </Text>
          )}
          &nbsp;
          {invoice?.billingAddress?.phone && (
            <Text style={{ color: "gray", fontSize: 13 }} copyable>
              {invoice?.billingAddress?.phone}
            </Text>
          )}
          <br />
          <Text style={{ color: "#434343", whiteSpace: "break-spaces" }}>
            {[
              invoice?.billingAddress?.line1,
              invoice?.billingAddress?.city,
              invoice?.billingAddress?.zip,
              invoice?.billingAddress?.country,
            ]
              .filter((x) => x)
              .join(", ")}
          </Text>
          <br />
        </pre>
      </Descriptions.Item>
    </Descriptions>
  );
};

export default InvoiceAddress;
