"use client";

import { Typography, Tag, Space } from "antd";

import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { Transaction } from "@/types";
import { LinkStripe } from "./LinkStripe";

const { Text } = Typography;

type TransactionsTableColumnsProps = {
  onSubscriptionClick: (subscriptionId: string) => void;
  onInvoiceClick: (invoiceId: string) => void;
  onRefundClick: (refundId: string) => void;
};

export const getTransactionsTableColumns = ({
  onSubscriptionClick,
  onInvoiceClick,
  onRefundClick,
}: TransactionsTableColumnsProps) => [
  {
    title: "Transaction Id",
    dataIndex: "chargebeeId",
    key: "id",
    render: (data: string, txn: Transaction) => {
      return (
        <>
          <Text type="secondary" copyable>
            {data}
          </Text>
          &nbsp;
          {txn.idAtGateway && txn.idAtGateway.startsWith("ch_") && (
            <LinkStripe idAtGateway={txn.idAtGateway} copyable={false} />
          )}
        </>
      );
    },
  },
  {
    title: "Customer Email",
    dataIndex: "customerEmail",
    key: "id",
    render(val: string) {
      return (
        <>
          <Text copyable>{val}</Text>
        </>
      );
    },
  },
  {
    title: "Customer Name",
    dataIndex: "customerName",
    key: "id",
  },
  {
    title: "Subscription Id",
    dataIndex: "subscriptionId",
    render: (data: string) => {
      return data ? (
        <Text type="secondary" copyable={{ text: data }}>
          <a
            onClick={(e) => {
              e.stopPropagation();
              onSubscriptionClick(data);
            }}
          >
            {data}
          </a>
        </Text>
      ) : (
        "Unknown"
      );
    },
    key: "id",
  },
  {
    title: "Invoices",
    render: (data: Transaction) => {
      if (data.linkedInvoices && data.linkedInvoices?.length > 0) {
        return (
          <Space>
            {data.linkedInvoices.map((invoice) => {
              return (
                <a
                  key={invoice.invoice_id}
                  onClick={(e) => {
                    e.stopPropagation();
                    onInvoiceClick(invoice.invoice_id);
                  }}
                >
                  {invoice.invoice_id}
                </a>
              );
            })}
          </Space>
        );
      }
    },
    key: "id",
  },
  {
    title: "Transaction Date",
    dataIndex: "date",
    render: (data: number) => {
      return <span>{formatDateFromUnix(data)}</span>;
    },
    key: "date",
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "id",
    render: (data: string) => {
      return (
        <Tag
          color={
            data === "success" ? "green" : data === "failure" ? "red" : "orange"
          }
          style={{ textTransform: "capitalize" }}
        >
          {data}
        </Tag>
      );
    },
  },
  {
    title: "Type",
    dataIndex: "type",
    key: "type",
    render: (data: string) => {
      return capitalizeWord(data);
    },
  },
  {
    title: "Amount",
    dataIndex: "amount",
    render: (data: any) => {
      return formatCents(data);
    },
    key: "id",
  },
  {
    title: "Amount Unused",
    dataIndex: "amountUnused",
    render: (data: any) => {
      return formatCents(data);
    },
    key: "id",
  },
  {
    title: "Currency",
    dataIndex: "currency",
    key: "id",
  },
  {
    title: "Refunds",
    render: (data: Transaction) => {
      if (data.linkedRefunds && data.linkedRefunds?.length > 0) {
        return (
          <Space>
            {data.linkedRefunds.map((refund) => {
              return (
                <Tag
                  key={refund.txn_id}
                  onClick={(e) => {
                    e.stopPropagation();
                    onRefundClick(refund.txn_id);
                  }}
                  color="blue"
                >
                  {refund.txn_id}
                </Tag>
              );
            })}
          </Space>
        );
      } else {
        return "Unknown";
      }
    },
    key: "id",
  },
  {
    title: "Payment Method",
    dataIndex: "paymentMethod",
    key: "id",
    render: (data: string) => {
      return capitalizeWord(data);
    },
  },
  {
    title: "Reference Number",
    dataIndex: "referenceNumber",
    key: "id",
    render: (data: string) => {
      return data ? data : "Unknown";
    },
  },
  {
    title: "Gateway",
    dataIndex: "gateway",
    key: "id",
    render: (data: string) => {
      return data ? capitalizeWord(data) : "Unknown";
    },
  },
  {
    title: "Business Entity",
    dataIndex: "businessEntityId",
    key: "id",
  },
];
