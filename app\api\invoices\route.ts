import { getInvoices, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the GET request for retrieving invoices.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved invoices or an error message.
 */
export async function GET() {
  try {
    const response = await getInvoices();
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RETRIEVE_INVOICES" },
      { status: 400 }
    );
  }
}
