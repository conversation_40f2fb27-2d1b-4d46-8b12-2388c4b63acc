"use client";

import { CalendarOutlined } from "@ant-design/icons";
import { memo, useCallback, useMemo, useState } from "react";
import {
  Button,
  Col,
  Divider,
  Drawer,
  Form,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
} from "antd";

import { useFetchWebhooks } from "@/hooks/dev/webhooks";
import { Webhook, WebhooksFilter } from "@/types";

const { Text, Title } = Typography;

// Memoized constant values
const INITIAL_FILTERS: WebhooksFilter = {
  sender: "chargebee",
  page: 1,
  limit: 25,
};

const SENDER_OPTIONS = [
  {
    value: "clerk",
    label: <span>CLERK</span>,
  },
  {
    value: "chargebee",
    label: <span>CHARGEBEE</span>,
  },
  {
    value: "bank-transfer",
    label: <span>BANK TRANSFER</span>,
  },
];

const STATUS_OPTIONS = [
  { value: "processed", label: <span>PROCESSED</span> },
  { value: "failed", label: <span>FAILED</span> },
];

// Memoized Status Tag component
const StatusTag = memo(({ status }: { status: string }) => {
  switch (status) {
    case "processed":
      return <Tag color="green">{status}</Tag>;
    case "failed":
      return <Tag color="red">{status}</Tag>;
    default:
      return <Tag>{status}</Tag>;
  }
});
StatusTag.displayName = "StatusTag";

// Memoized Filter Form component
const FilterForm = memo(({ form, onValuesChange, initialSender }: any) => (
  <Form
    form={form}
    onValuesChange={onValuesChange}
    initialValues={{ sender: initialSender }}
  >
    <div style={{ display: "flex", justifyContent: "space-between" }}>
      <Form.Item name="sender" style={{ width: "25%" }}>
        <Select placeholder="Sender" options={SENDER_OPTIONS} />
      </Form.Item>
      <Form.Item name="status" style={{ width: "25%" }}>
        <Select allowClear placeholder="Status" options={STATUS_OPTIONS} />
      </Form.Item>
      <Button type="link" onClick={() => form.resetFields()}>
        Clear Filters
      </Button>
    </div>
  </Form>
));
FilterForm.displayName = "FilterForm";

// Memoized Drawer Content component
const DrawerContent = memo(({ current }: any) => (
  <>
    <Space direction="vertical">
      <Tag color="blue">
        <Text copyable>{current?.id}</Text>
      </Tag>
    </Space>
    <Divider />
    <Title level={3}>Outcome</Title>
    {current?.outcome ? (
      <>
        {current.outcome.message && (
          <>
            <Title level={4}>Message</Title>
            <blockquote>{current.outcome.message}</blockquote>
          </>
        )}
        {current.outcome.affectedEntity && (
          <>
            <Title level={4}>Affected Entity</Title>
            <blockquote>{current.outcome.affectedEntity}</blockquote>
          </>
        )}
        <Title level={4}>Affected Entity ID</Title>
        {current.outcome.affectedEntityId ? (
          <Text copyable>{current.outcome.affectedEntityId}</Text>
        ) : (
          <Text type="secondary">No affected entity id</Text>
        )}
      </>
    ) : (
      <Text type="secondary">No outcome</Text>
    )}
    <Divider />
  </>
));
DrawerContent.displayName = "DrawerContent";

type PageState = {
  isOpen: boolean;
  current: Webhook | null;
  filters: WebhooksFilter;
  processing: boolean;
};

export default function WebhooksTable() {
  const [pageState, setPageState] = useState<PageState>({
    isOpen: false,
    current: null,
    filters: INITIAL_FILTERS,
    processing: false,
  });
  const [filterForm] = Form.useForm();

  const { isLoading, data: cronLogs } = useFetchWebhooks({
    filters: pageState.filters,
  });

  // Memoized columns definition
  const columns = useMemo(
    () => [
      { title: "Id", dataIndex: "id", key: "id" },
      {
        title: "Sender",
        dataIndex: "sender",
        key: "sender",
        render: (text: string) => <span>{text}</span>,
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        render: (text: string) => <StatusTag status={text} />,
      },
      {
        title: (
          <>
            <CalendarOutlined />
            &nbsp;Created At
          </>
        ),
        dataIndex: "createdAt",
        key: "createdAt",
      },
      {
        title: (
          <>
            <CalendarOutlined />
            &nbsp;Updated At
          </>
        ),
        dataIndex: "updatedAt",
        key: "updatedAt",
      },
    ],
    []
  );

  const filterWebhooks = useCallback((changedValues: any) => {
    setPageState((prev) => ({
      ...prev,
      filters: { ...prev.filters, ...changedValues },
    }));
  }, []);

  const handleRowClick = useCallback((data: Webhook) => {
    setPageState((prev) => ({ ...prev, isOpen: true, current: data }));
  }, []);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Webhooks</Title>
      <Row style={{ lineHeight: "10px" }}>
        <Col span={24}>
          <FilterForm
            form={filterForm}
            onValuesChange={filterWebhooks}
            initialSender={pageState.filters.sender}
          />
        </Col>
      </Row>
      <Table
        dataSource={cronLogs?.items}
        columns={columns}
        size="small"
        pagination={{
          total: cronLogs?.total,
          pageSize: pageState.filters.limit,
          onChange: (page) =>
            setPageState((prev) => ({
              ...prev,
              filters: { ...prev.filters, page },
            })),
        }}
        loading={isLoading}
        rowClassName="paradox-table-row"
        onRow={(data) => ({
          onClick: () => handleRowClick(data),
        })}
        rowKey="id"
      />
      <Drawer
        title="Webhook Details"
        placement="right"
        size="large"
        closable={true}
        onClose={() => setPageState((prev) => ({ ...prev, isOpen: false }))}
        open={pageState.isOpen}
      >
        <DrawerContent current={pageState.current} />
      </Drawer>
    </div>
  );
}
