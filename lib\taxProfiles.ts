type SelectOption = {
  label: string;
  value: string;
  description: string;
};

/**
 * Get the tax profiles configured in ChargeBee.
 * This will return different tax profiles based on the environment.
 * @returns {SelectOption[]} The list of tax profiles (labels and corresponding values).
 */
export const getTaxProfiles = (): SelectOption[] => {
  if (process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT === "production") {
    return [
      {
        label: "Digital Products",
        value: "199FXkU8m5vDD107U",
        description:
          "Use this tax profile when it comes to digital products (e.g. Trainings)",
      },
      {
        label: "Digital product with live session",
        value: "773tOU8m7N6H11DM",
        description:
          "Use this tax profile when it comes to digital products with live event (e.g. Live session)",
      },
      {
        label: "Non digital products - Event",
        value: "19ABccUDZe70USfV",
        description:
          "Use this tax profile when it comes to non-digital products (e.g. Events)",
      },
    ];
  } else if (
    process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT === "staging" ||
    process.env.NODE_ENV === "development"
  ) {
    return [
      {
        label: "Digital Products",
        value: "BTTwfEU2TMvFRPKY",
        description:
          "Use this tax profile when it comes to digital products (e.g. Trainings)",
      },
      {
        label: "Digital product with live session",
        value: "19A43EU31cJZ4UWf",
        description:
          "Use this tax profile when it comes to digital products with live event (e.g. Live session)",
      },
      {
        label: "Non digital products - Event",
        value: "BTLyUrUDZcGW9zYu",
        description:
          "Use this tax profile when it comes to non-digital products (e.g. Events)",
      },
    ];
  }
  return [];
};

/**
 * Get the name of the tax profile based on the tax profile code.
 * @param {string} value The code of the tax profile.
 * @returns {string} The name of the tax profile.
 */
export const getTaxProfileName = (value: string): string => {
  const taxProfiles = getTaxProfiles();
  const taxProfile = taxProfiles.find((profile) => profile.value === value);
  return taxProfile ? taxProfile.label : "";
};
