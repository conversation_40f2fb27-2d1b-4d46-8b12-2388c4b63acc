"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, Space, Descriptions } from "antd";
import { EditOutlined } from "@ant-design/icons";

import OfferAccessDetails from "../OfferAccessDetails";
import { useUserRole } from "@/hooks/roles";
import { Offer } from "@/types";

const { Text } = Typography;

type OfferSettingsAccessProps = {
  offer: Offer;
  setEditingAccess: (value: boolean) => void;
};

export default function OfferSettingsAccess({
  offer,
  setEditingAccess,
}: OfferSettingsAccessProps) {
  const { canAccess } = useUserRole();
  return (
    <div className="paradox-card">
      <Descriptions
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <span>Access in the offer</span>
              <br />
              <small style={{ fontSize: 13, color: "darkgray" }}>
                For the courses & community spaces
              </small>
            </div>
            <div style={{ marginLeft: "auto" }}>
              <Button
                icon={<EditOutlined />}
                onClick={() => {
                  if (canAccess("offer-update")) {
                    setEditingAccess(true);
                  }
                }}
              >
                Edit
              </Button>
            </div>
          </div>
        }
        bordered
        column={1}
        size="small"
      >
        <Descriptions.Item label="Main product(s)">
          <OfferAccessDetails
            offer={offer}
            courses={offer?.lmsIds && offer.lmsIds?.products}
            communities={offer?.communityIds && offer.communityIds?.products}
          />
        </Descriptions.Item>
        <Descriptions.Item label="Recommended product(s)">
          <OfferAccessDetails
            offer={offer}
            courses={offer?.lmsIds && offer.lmsIds?.recommended}
            communities={offer?.communityIds && offer.communityIds?.recommended}
          />
        </Descriptions.Item>
      </Descriptions>
      <Divider />
      <Descriptions
        title={
          <>
            <span>Auto Suspension</span>
          </>
        }
        bordered
        column={2}
        size="small"
      >
        <Descriptions.Item label="Suspendable">
          <Text style={{ fontSize: 11 }}>
            {offer.suspendable ? "Yes" : "No"}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="Delay period">
          {offer.delayPeriod
            ? offer.delayPeriod + " day(s)"
            : offer.delayPeriod === 0
              ? "0 days"
              : "Undefined"}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
}
