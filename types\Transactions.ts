import { Currencies } from "./Currencies";
import { Transaction as ChargebeeTransaction } from "chargebee";

type LinkedInvoice = ChargebeeTransaction.LinkedInvoice;
type LinkedCreditNote = ChargebeeTransaction.LinkedCreditNote;
type LinkedRefund = ChargebeeTransaction.LinkedRefund;

export type Transaction = {
  id: number;
  chargebeeId: string;
  status?:
    | "in_progress"
    | "success"
    | "voided"
    | "failure"
    | "timeout"
    | "needs_attention";
  date?: number;
  type?: "authorization" | "payment" | "refund" | "payment_reversal";
  currency?: Currencies;
  amount?: number;
  amountUnused?: number;
  subscriptionId?: string;
  customerId?: string;
  customerEmail?: string;
  customerName?: string;
  exchangeRate?: number;
  referenceNumber?: string;
  linkedInvoices?: LinkedInvoice[];
  linkedCreditNotes?: LinkedCreditNote[];
  linkedRefunds?: LinkedRefund[];
  paymentMethod?:
    | "card"
    | "check"
    | "chargeback"
    | "bank_transfer"
    | "apple_pay"
    | "google_pay";
  maskedCardNumber?: string;
  paymentMethodDetails?: any;
  gateway?: string;
  idAtGateway?: string;
  businessEntityId?: string;
  errorCode?: string;
  errorText?: string;
  createdAt?: Date;
};

export type TransactionListResponse = {
  items: Transaction[];
  total: number;
  currentPage: number;
};

export type UnMatchedTransactions = {
  id: number;
  bankName: string;
  paymentId: string;
  paymentDate: string;
  paymentDescription: string;
  senderFirstName: string;
  senderLastName: string;
  senderAddress: string;
  senderEmail: string;
  currency: string;
  paymentAmount: number;
  status: "matched" | "unmatched";
  entityId: string;
};

export type TransactionsFilters = {
  searchQuery?: string;
  subscriptionFilter?: string;
  statusFilter?: string;
  paymentMethodFilter?: string;
};

export type GetAllTransactionsParams = {
  searchQuery?: string;
  status?: string;
  paymentMethod?: string;
  customerId?: string;
  subscriptionId?: string;
};
