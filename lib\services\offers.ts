"use server";

import {
  CreateOfferProducts,
  GetOffersParams,
  Offer,
  OfferListResponse,
  UpdateOfferAccessConfig,
  UpdateOfferProducts,
  WarrantyListResponse,
} from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/product-offers`;
const warrantyUrl = `${process.env.API_URL}/checkout/warranty-section`;

/**
 * Activates an offer by updating its status to "active" and associating it with a checkout.
 * @param offerId - The ID of the offer to activate.
 * @param checkoutId - The ID of the checkout to associate the offer with.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export const activateOffer = async (
  offerId: number,
  checkoutId: number
): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}/${offerId}/activate`, "PATCH");
    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};

/**
 * Archives an offer by updating its status to "disabled" in the backend.
 * @param offerId - The ID of the offer to be archived.
 * @param checkoutId - The ID of the checkout associated with the offer.
 * @returns A Promise that resolves to the JSON response from the backend.
 */
export const archiveOffer = async (
  offerId: number,
  redirectIfDisabled: string
): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}/${offerId}/disable`, "PATCH", {
      redirectIfDisabled: redirectIfDisabled,
    });
    return res;
  } catch (err) {
    console.error("cannot archiveOffer: ", err);
  }
};

/**
 * Creates an offer by sending a POST request to the backend API.
 * @param values - The values to be sent in the request body.
 * @returns A Promise that resolves to the JSON response from the API.
 */
export const createOffer = async (values: any): Promise<any> => {
  const requestQuery = `${baseUrl}`;
  const res = await osRequest(requestQuery, "POST", values);

  return res;
};

/**
 * Sends a POST request to create offer products.
 * @param values - The values to be sent in the request body.
 * @param offerID - The ID of the offer.
 * @returns A Promise that resolves to the HTTP status code of the response.
 */
export const createOfferProducts = async (
  params: CreateOfferProducts
): Promise<any> => {
  let requestQuery = `${baseUrl}/${params.id}/config`;
  const res = await osRequest(requestQuery, "POST", params.values, {}, false);

  return res.status;
};

/**
 * Duplicate an offer by making a POST request to the backend API.
 * @param offer - The offer to be duplicated.
 * @returns A Promise that resolves to the response from the backend API.
 */
export const duplicateOffer = async (
  offer: Offer,
  includeProductDelivery: boolean
): Promise<any> => {
  try {
    let tempOffer = {
      offerInfo: {
        name: offer.name + " (COPY)",
        externalName: offer.externalName,
        cardImage: offer.cardImage,
        bannerImage: offer.bannerImage,
        description: offer.description,
        slug: offer.slug + "-" + Math.random().toString(36).slice(2, 7),
        usp: offer.usp,
        status: offer.status,
        currency: offer.currency,
        fiscalEntity: offer.fiscalEntity,
        paymentGateway: offer.paymentGateway,
        isForever: offer.isForever,
        trialDaysMonthly: offer.trialDaysMonthly,
        trialDaysYearly: offer.trialDaysYearly,
        withProductDelivery: includeProductDelivery ? true : false,
        suspendable: offer.suspendable,
        delayPeriod: offer.delayPeriod,
        image: offer.image,
        redirectUrl: offer.redirectUrl,
        lmsIDs: includeProductDelivery ? offer.lmsIds : null,
        communityIds: includeProductDelivery ? offer.communityIds : null,
      },
      checkoutInfo: {
        bannerImage: offer.checkoutPage?.bannerImage,
        template: offer.checkoutPage?.template,
        mainColor: offer.checkoutPage?.mainColor,
        url: offer.checkoutPage?.url,
        cancellationPolicy: offer.checkoutPage?.cancellationPolicy,
        testimony: offer.checkoutPage?.testimony,
        abandonedCartFlow: offer.checkoutPage?.abandonedCartFlow,
      },
    };
    const res = await osRequest(`${baseUrl}`, "POST", tempOffer);
    if (offer?.config) {
      await createOfferProducts({
        id: res.id,
        values: offer.config,
      });
    }
    if (
      offer?.checkoutPage?.warrantySections &&
      offer.checkoutPage.warrantySections.length > 1
    ) {
      for (var warranty of offer.checkoutPage.warrantySections) {
        await createWarranty({
          checkoutPageId: res.checkoutPage.id,
          icon: warranty.icon,
          message: warranty.message,
        });
      }
    }
    return res;
  } catch (err) {
    console.error("cannot duplicateOffer: ", err);
  }
};

/**
 * Retrieves an offer from the backend server.
 * @param chargebeeId - The Chargebee Id
 * @returns A Promise that resolves to the retrieved offer.
 */
export const getOffer = async (chargebeeId: string): Promise<Offer | null> => {
  try {
    const res = await osRequest(
      `${baseUrl}/by-chargebee-id/${chargebeeId}`,
      "GET"
    );

    return res;
  } catch (err) {
    console.error("cannot getOffer: ", err);
    return null;
  }
};

/**
 * Retrieves the details of an offer including configured products.
 * @param id - The ID of the offer to retrieve.
 * @returns A Promise that resolves to the retrieved offer details.
 */
export const getOfferDetails = async (id: number): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}/downsell/${id}`, "GET");
    return res;
  } catch (err) {
    console.error("cannot getOfferDetails: ", err);
    return null;
  }
};

/**
 * Retrieves offers based on the provided parameters.
 *
 * @param params - The parameters for filtering the offers.
 * @returns A Promise that resolves to the JSON response containing the offers.
 */
export const getOffers = async (
  params: GetOffersParams = {}
): Promise<OfferListResponse> => {
  let requestQuery = new URL(`${baseUrl}/search?`);
  const query = new URLSearchParams();
  if (Object.keys(params).length) {
    if (params.name) query.append("name", params.name);
    if (params.line) query.append("line", params.line);
    if (params.family) query.append("family", params.family);
    if (params.plan) query.append("plan", params.plan);
    if (params.product) query.append("product", params.product);
    if (params.status) query.append("status", params.status);
  }
  query.append("order", "DESC");
  requestQuery.search = query.toString();

  const res = await osRequest(requestQuery.toString(), "GET");

  return res;
};

/**
 * Updates an offer by sending a PATCH request to the backend API.
 * @param params - The parameters for the offer update.
 * @returns A Promise that resolves to the JSON response from the API.
 */
export const patchOffer = async (params: any): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}`, "PATCH", params);

    return res;
  } catch (err) {
    console.error("cannot patchOffer: ", err);
  }
};

/**
 * Updates the products of an offer by sending a PATCH request to the backend API.
 * @param values - The updated values for the offer products.
 * @param offerID - The ID of the offer to update.
 * @returns A Promise that resolves to the HTTP status code of the response.
 */
export const patchOfferProducts = async (
  params: UpdateOfferProducts
): Promise<any> => {
  const url = `${baseUrl}/${params.id}/config`;
  const res: Response = await osRequest(url, "PATCH", params.values, {}, false);

  return res.status;
};

/**
 * Publishes an offer with the specified offer ID.
 * @param offerID - The ID of the offer to publish.
 * @returns A Promise that resolves to the HTTP status code of the response.
 */
export const publishOffer = async (offerID: string): Promise<any> => {
  const url = `${baseUrl}/${offerID}/publish`;
  const res = await osRequest(url, "POST", {}, {}, false);
  return res.status;
};

export const getOfferWarranties = async (
  id: number
): Promise<WarrantyListResponse> => {
  try {
    const res = await osRequest(
      `${process.env.API_URL}/checkout/${id}/warranty-section`,
      "GET"
    );
    return res;
  } catch (err) {
    console.error("cannot getOfferWarranties:", err);
    return { items: [], total: 0, currentPage: 0 };
  }
};
export const createWarranty = async (values: any): Promise<any> => {
  let requestQuery = warrantyUrl;
  const res = await osRequest(requestQuery, "POST", values);

  return res;
};

export const patchWarranty = async (params: any): Promise<any> => {
  try {
    const res = await osRequest(`${warrantyUrl}/${params.id}`, "PATCH", params);

    return res;
  } catch (err) {
    console.error("charge result: ", err);
  }
};

export const deleteWarranty = async (id: number): Promise<any> => {
  try {
    const res = await osRequest(`${warrantyUrl}/${id}`, "DELETE");
    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};

/**
 * Updates the access configuration of an offer by sending a POST request to the backend API.
 * @param params - The updated access configuration for the offer.
 * @returns A Promise that resolves to the JSON of the response returned from API.
 */
export const updateOfferAccesses = async (
  params: UpdateOfferAccessConfig
): Promise<any> => {
  const url = `${baseUrl}/${params.id}/access`;
  const res: Response = await osRequest(url, "POST", params, {}, true);
  return res;
};
