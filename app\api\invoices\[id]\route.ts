import { getInvoice, getInvoices, initChargebee } from "@/server/chargebee";
import { NextRequest, NextResponse } from "next/server";

initChargebee();

/**
 * Handles the GET request for retrieving an invoice.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved invoice or an error message.
 */
export async function GET(req: NextRequest) {
  try {
    const id = req.url?.split("/").pop();
    if (id) {
      const response = await getInvoice(id);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RETRIEVE_INVOICE" },
      { status: 400 }
    );
  }
}
