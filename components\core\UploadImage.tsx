import { App, Form, Image, Upload, UploadProps } from "antd";
import { InboxOutlined } from "@ant-design/icons";
import { v4 as uuidv4 } from "uuid";

import { getBase64 } from "@/lib/helper";
import { uploadFile } from "@/lib/s3";

const { Dragger } = Upload;
const rules = [{ required: true, message: "This value is required!" }];

type UploadImageProps = {
  name: string;
  label: string;
  required?: boolean;
  image?: string;
  url?: string;
  setImage: (img: string) => void;
};

const UploadImage = ({
  name,
  label,
  required = true,
  url,
  setImage,
}: UploadImageProps) => {
  const { message } = App.useApp();
  const uploadProps: UploadProps = {
    name: "file",
    multiple: false,
    accept: "image/*",
    maxCount: 1,
    fileList: [],
    async customRequest(data: any) {
      let imageBase64 = await getBase64(data.file);
      let filename = uuidv4();
      filename += "." + data.file.name.split(".").pop();
      try {
        const response = await uploadFile({
          filename: filename,
          image: imageBase64,
          contentType: data.file.type,
        });
        if (response?.$metadata.httpStatusCode === 200) {
          data.onSuccess();
          let imageURL = process.env
            .NEXT_PUBLIC_AWS_BUCKET_PUBLIC_URL as string;
          imageURL += filename;
          setImage(imageURL);
        }
      } catch (err) {
        console.error(err);
        message.open({
          type: "error",
          content: "An error occurred while uploading the image",
        });
      }
    },
  };

  return (
    <>
      <Form.Item
        label={label}
        name={name}
        style={{ marginBottom: "0px" }}
        rules={required ? rules : []}
      >
        <Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">
            Click or drag an image to this area to upload
          </p>
        </Dragger>
      </Form.Item>
      {url && <Image src={url} width={100} height={100} />}
    </>
  );
};

export default UploadImage;
