"use server";

import { auth } from "@clerk/nextjs/server";

const DL_API_KEY = "C5723DA0-CB54-4C51-8E6E-1631F9549244";

export const generateHeaders = async () => {
  const { getToken, sessionId } = await auth();

  return {
    Authorization: `Bear<PERSON> ${await getToken()}`,
    "x-api-key": DL_API_KEY,
    "x-session-id": sessionId,
  } as RequestInit["headers"];
};

export const getAPI_URL = async () => {
  return process.env.API_URL as string;
};

/**
 * Makes an HTTP request to the specified URL.
 *
 * @param url - The URL to make the request to.
 * @param method - The HTTP method to use for the request.
 * @param body - The request body (optional).
 * @param headers - The request headers (optional).
 * @returns A Promise that resolves to the JSON response from the server.
 */
export const osRequest = async (
  url: string,
  method: string,
  body?: any,
  headers?: Record<string, string>,
  sendJson: boolean = true
) => {
  const withCors: RequestInit =
    process.env.NODE_ENV === "production"
      ? { credentials: "same-origin", mode: "cors" }
      : {};
  let requestInit: RequestInit = {
    method,
    headers: {
      ...(await generateHeaders()),
      ...headers,
    },
    ...withCors,
  };
  if (method !== "GET" && body) {
    // @ts-ignore
    requestInit.headers["Content-Type"] = "application/json";
    requestInit.body = JSON.stringify(body);
  }
  let res;
  try {
    res = await fetch(url, requestInit);
  } catch (error) {
    console.error("[osRequest] Error:", error);
    throw error;
  }

  return sendJson ? res.json() : (res as Response);
};
