"use client";

import { Descriptions, Typography } from "antd";
import { useEffect, useMemo, useState } from "react";
import { Estimate } from "chargebee";

import CartItem from "./CartItem";
import { formatCents } from "@/lib/currency";
import { EstimateSubscriptionProps } from "@/types";
import { estimateSubscription } from "@/lib/services/chargebee";

const { Text } = Typography;

const EstimateSubscription = ({
  billingCycle,
  coupons,
  customerData,
  discounts,
  offer,
  products,
  handleProductQtyChange,
}: EstimateSubscriptionProps) => {
  const [result, setResult] = useState<Estimate>();

  // compute subtotal as a memoized value
  const subTotal = useMemo(() => {
    return (
      result?.invoice_estimate?.line_items?.reduce((acc: any, curr: any) => {
        return (acc += curr?.amount);
      }, 0) * billingCycle
    );
  }, [result, billingCycle]);

  // compute discountAmount as a memoized value
  const discountAmount = useMemo(() => {
    if (!result?.invoice_estimate?.discounts) {
      return 0;
    } else {
      const discounts = result?.invoice_estimate?.discounts;
      return (
        discounts.reduce((acc, curr) => {
          return (acc += curr?.amount);
        }, 0) * billingCycle
      );
    }
  }, [result?.created_at, billingCycle]);

  const handleEstimate = async () => {
    if (!offer || !products.length) return;
    const estimate = await estimateSubscription({
      offer,
      customerData,
      coupons,
      discounts,
      products,
    });
    console.log("ℹ️ handleEstimate", estimate, {
      offerName: offer.name,
      products,
    });
    setResult(estimate);
  };

  useEffect(() => {
    handleEstimate();
  }, [billingCycle, coupons, customerData, discounts, offer, products]);

  return (
    <Descriptions title="Order summary" bordered column={1} size="small">
      <Descriptions.Item label="Product(s)">
        {products
          .filter((p: any) => p.isChecked)
          .map((product: any, index: number) => (
            <CartItem
              estimation={result}
              itemData={product}
              billingCycle={billingCycle}
              offer={offer}
              key={index}
              handleQtyChange={(qty: number) => {
                handleProductQtyChange(product, qty);
              }}
            />
          ))}
      </Descriptions.Item>
      <Descriptions.Item label="Offer">
        {!offer ? (
          <Text type="secondary">Choose an offer</Text>
        ) : (
          <Text>{offer.name}</Text>
        )}
      </Descriptions.Item>
      <Descriptions.Item label="Subtotal">
        {result?.invoice_estimate ? formatCents(subTotal) : null}
      </Descriptions.Item>
      <Descriptions.Item label="Discount">
        {result?.invoice_estimate ? "- " + formatCents(discountAmount) : null}
      </Descriptions.Item>
      <Descriptions.Item label="Total order">
        {result?.invoice_estimate
          ? formatCents(subTotal - discountAmount)
          : null}
      </Descriptions.Item>
      <Descriptions.Item label="Taxes (included)">
        {!result?.invoice_estimate?.taxes?.length ? (
          <Text type="secondary">Computed after step 2</Text>
        ) : (
          <Text>
            {formatCents(
              result?.invoice_estimate?.taxes?.reduce((acc: any, curr: any) => {
                return (acc += curr?.amount);
              }, 0)
            )}
          </Text>
        )}
      </Descriptions.Item>
    </Descriptions>
  );
};

export default EstimateSubscription;
