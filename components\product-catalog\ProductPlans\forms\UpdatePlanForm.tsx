"use client";

import { Form, Input, Select, Typography } from "antd";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { PlanDTO } from "@/types";

type UpdateProps = {
  isModalOpen: boolean;
  formValues: PlanDTO | undefined;
  handleUpdate: Function;
  handleCancel: Function;
};

const { Title, Text } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

const UpdatePlanForm = ({
  isModalOpen,
  formValues,
  handleUpdate,
  handleCancel,
}: UpdateProps) => {
  const [planUpdateForm] = Form.useForm();
  planUpdateForm.setFieldsValue(formValues);
  planUpdateForm.setFieldValue("amount", formValues?.prices[0].amount);
  planUpdateForm.setFieldValue(
    "billingCycle",
    formValues?.prices[0].periodUnit
  );
  planUpdateForm.setFieldValue(
    "totalBillingCycles",
    formValues?.prices[0].totalBillingCycles
  );
  planUpdateForm.setFieldValue(
    "amountPerBillingCycle",
    formValues?.prices[0].amountPerBillingCycle
  );
  planUpdateForm.setFieldValue(
    "description",
    formValues?.prices[0].description
  );

  const computeAmountPerBillingCycle = () => {
    if (formValues?.product?.isForever) {
      let amount = planUpdateForm.getFieldValue("amount");
      planUpdateForm.setFieldValue("amountPerBillingCycle", amount);
      return;
    }
    let billingCycles = planUpdateForm.getFieldValue("totalBillingCycles");
    let amount = planUpdateForm.getFieldValue("amount");

    if (billingCycles && amount) {
      let tempAmount = Number(amount) / Number(billingCycles);
      planUpdateForm.setFieldValue(
        "amountPerBillingCycle",
        tempAmount.toFixed(3)
      );
    }
  };

  const computePlanInternalName = (changedValues: any, allValues: any) => {
    if (
      changedValues.internalName === undefined &&
      allValues.billingCycle &&
      allValues.totalBillingCycles &&
      allValues.amount
    ) {
      planUpdateForm.setFieldValue(
        "internalName",
        `${formValues?.product?.externalName} - ${allValues.billingCycle} - ${allValues.totalBillingCycles} - ${allValues.amount}`
      );
    }
  };

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      onCancel={() => handleCancel()}
      onClose={() => handleCancel()}
      onSubmit={() => planUpdateForm.submit()}
      submitText="Update"
      title="Update Plan"
    >
      <Form
        layout="vertical"
        onFinish={(values) => {
          handleUpdate(values);
        }}
        form={planUpdateForm}
        onValuesChange={computePlanInternalName}
      >
        <Title level={5}>Price settings</Title>
        <br />
        <Form.Item label="Product price" name="amount" rules={rules}>
          <Input
            placeholder="10,500€"
            addonAfter="€"
            onChange={computeAmountPerBillingCycle}
          />
        </Form.Item>
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Billing cycles"
          name="billingCycle"
          rules={rules}
        >
          <Select
            placeholder="Monthly"
            options={
              formValues?.product?.isForever
                ? [
                    {
                      value: "monthly-forever",
                      label: <span>Monthly - Forever</span>,
                    },
                    {
                      value: "yearly-forever",
                      label: <span>Yearly - Forever</span>,
                    },
                  ]
                : [
                    {
                      value: "monthly",
                      label: <span>Monthly - Fixed</span>,
                    },
                  ]
            }
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          {`Choose if you'd like this plan to be billed forever, for a
                  fixed period or one-time payment.`}
        </Text>
        <br />
        <br />
        {!formValues?.product?.isForever && (
          <div>
            <Form.Item
              style={{ marginBottom: "0px" }}
              label="# Billing cycles"
              name="totalBillingCycles"
              rules={rules}
            >
              <Input placeholder="12" onChange={computeAmountPerBillingCycle} />
            </Form.Item>
            <Text style={{ fontSize: 13, color: "darkgray" }}>
              Number of billing cycles for this plan
            </Text>
            <br />
            <br />
          </div>
        )}
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Amount per billing cycle"
          name="amountPerBillingCycle"
          rules={rules}
        >
          <Input disabled placeholder="1,000€" addonAfter="€" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This number is automatically computed based on the product price & #
          installments.
        </Text>
        <br />
        <br />

        <br />
        <Title level={5}>Plan settings</Title>
        <br />
        <Form.Item
          label="Plan internal name"
          name="internalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="EDEC Certificate x12" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this plan internally.
        </Text>
        <br />
        <br />
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Stock Keeping Unit (SKU)"
          name="crmSku"
          rules={rules}
        >
          <Input placeholder="SKU" disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Used to have a unique identifier per plan. It&apos;s also used in
          Hubspot at the product level for the property of the same name.
        </Text>
        <br />
        <br />
        <Form.Item
          style={{ marginBottom: "0px" }}
          label="Status"
          name="status"
          rules={rules}
        >
          <Select
            placeholder="Active"
            options={[
              { value: "active", label: <span>Active</span> },
              { value: "draft", label: <span>Draft</span> },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Setting a plan to ‘Draft’ status will unpublished all the checkout
          page linked to this plan.
        </Text>
      </Form>
    </DrawerFormContainer>
  );
};

export default UpdatePlanForm;
