"use client";

import { ArrowLeftOutlined } from "@ant-design/icons";
import { Button, Col, Row } from "antd";
import { useRouter } from "next/navigation";

type PageHeaderProps = React.PropsWithChildren<{
  title: string | React.ReactNode;
  actions?: React.ReactNode;
}>;

export default function PageHeader({
  title,
  children,
  actions,
}: PageHeaderProps) {
  const router = useRouter();

  return (
    <>
      <Row
        style={{
          display: "flex",
          justifyContent: "flex-start",
          alignItems: "center",
        }}
      >
        <Button
          icon={<ArrowLeftOutlined style={{ color: "#5B5B5D" }} />}
          onClick={() => {
            router.back();
          }}
          style={{ marginRight: 10 }}
        />
        <div style={{ fontSize: 24, margin: 10 }}>{title}</div>
      </Row>
      <Row
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Col span={18}>{children ? <Row>{children}</Row> : null}</Col>
        <Col span={6} style={{ paddingLeft: 10 }}>
          {actions ? <Row>{actions}</Row> : null}
        </Col>
      </Row>
    </>
  );
}
