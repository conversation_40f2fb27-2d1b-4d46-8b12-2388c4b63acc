"use client";

import useSWR, { SWRConfiguration } from "swr";

import { getProductPlan, getProductPlans } from "@/lib/services";
import { PlanDTO, PlanListResponse } from "@/types";

export const useFetchProductPlans = (
  productId: number,
  options?: SWRConfiguration
) => {
  return useSWR<PlanListResponse>(
    `useFetchProductPlans-${productId}`,
    () => getProductPlans(productId),
    options
  );
};

export const useFetchProductPlan = (id: string) => {
  return useSWR<PlanDTO>(`useFetchProductPlan-${id}`, () =>
    getProductPlan({ id: Number(id) })
  );
};
