"use client";

import {
  App,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Typography,
} from "antd";
import dayjs from "dayjs";
import { EditOutlined, InfoCircleTwoTone } from "@ant-design/icons";
import { useEffect } from "react";

import { formatCents } from "@/lib/currency";
import { recordOfflineRefund } from "@/lib/services/chargebee";
import { markAsRefundedAndCancel } from "@/lib/services";
import { Invoice } from "@/types";

const { Text } = Typography;

type OfflineRefundProps = {
  amountRefundable: number;
  isOpen: boolean;
  setIsOpen: Function;
  invoice: Invoice;
  refetchInvoice: Function;
  showMarkAsRefunded: boolean;
};

const refundReasonOptions = [
  {
    value: "refund_during_retractation_period",
    label: "Refund during retractation period",
  },
  {
    value: "offer_error",
    label: "Offer error",
  },
  { value: "other", label: "Other" },
];

export const OfflineRefundModal = ({
  amountRefundable,
  isOpen,
  setIsOpen,
  invoice,
  refetchInvoice,
  showMarkAsRefunded,
}: OfflineRefundProps) => {
  const { message, modal } = App.useApp();
  const [offlineRefundForm] = Form.useForm();

  useEffect(() => {
    offlineRefundForm.setFieldValue(
      "amountRefunded",
      amountRefundable > 0 ? amountRefundable / 100 : 0
    );
  }, []);

  const offlineRefundInvoice = async (values: any) => {
    const refundReasonLabel = refundReasonOptions.find(
      (option) => option.value === values.refundReason
    )?.label as string;
    modal.warning({
      title: `Are you sure you want to record a refund of ${formatCents(values.amountRefunded * 100)}?`,
      content: `By clicking proceed, a credit note of ${formatCents(values.amountRefunded * 100)} will be generated 
      against and applied to this invoice.`,
      okText: "Proceed",
      okType: "primary",
      cancelText: "Close",
      okCancel: true,
      onCancel: () => {},
      onOk: async () => {
        if (!invoice || !invoice.date) return;
        let convertedDate = dayjs(values.date);
        let invoiceDate = dayjs.unix(invoice.date);

        if (convertedDate.diff(invoiceDate, "day") < 0) {
          message.open({
            type: "error",
            content: "Refund date cannot be before invoice date!",
          });
          return;
        }
        const res = await recordOfflineRefund({
          id: String(invoice.chargebeeId),
          reason: refundReasonLabel,
          amount: values.amountRefunded * 100,
          date: convertedDate.toDate().getTime() / 1000,
          paymentMethod: values.paymentMethod,
          reference: values.reference,
          customPaymentMethodId: values.customPaymentMethodId,
        });
        if (res?.error) {
          message.open({
            type: "error",
            content: "Cannot record offline refund!",
          });
        } else {
          // Call to backend to mark as refunded.
          if (values.markRefund) {
            await markAsRefundedAndCancel(invoice?.subscriptionId as string);
          }
          message.open({
            type: "success",
            content: "Offline refund issued successfully!",
          });
          setIsOpen(false);
          await refetchInvoice();
          offlineRefundForm.resetFields();
        }
      },
    });
  };

  return (
    <Modal
      open={isOpen}
      closable={false}
      onOk={() => {
        offlineRefundForm.submit();
      }}
      onCancel={() => {
        setIsOpen(false);
      }}
      okText="Proceed"
    >
      <Text style={{ fontSize: 15 }} strong>
        Record an offline refund for invoice #{invoice.id}
      </Text>
      <br />
      <Text style={{ fontSize: 13, color: "gray" }}>
        It is mandatory to manually refund this invoice via a bank transfer
        before doing this action.
      </Text>
      <br />
      <br />
      <Row className="paradox-card">
        <Col span={1}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <InfoCircleTwoTone style={{ fontSize: 34 }} />
          </div>
        </Col>
        <Col span={22} offset={1}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "gray",
            }}
          >
            Creating an offline refund will automaticaly create a Credit note
            and a transaction attached to this invoice/credit note.
          </div>
        </Col>
      </Row>
      <br />
      <Form
        layout="vertical"
        form={offlineRefundForm}
        onFinish={offlineRefundInvoice}
      >
        <Form.Item
          name="amountRefunded"
          label="Amount refunded"
          style={{ marginBottom: "5px" }}
          rules={[
            {
              required: true,
              message: "Please enter the amount to refund",
            },
          ]}
        >
          <InputNumber
            style={{ width: "250px" }}
            min={1}
            addonBefore="EUR"
            addonAfter={<EditOutlined />}
            placeholder="Amount to refund"
            max={amountRefundable > 0 ? amountRefundable / 100 : 0}
          />
        </Form.Item>
        <Text style={{ color: "gray", fontSize: 13 }}>
          Maximum refundable amount:{" "}
          {formatCents(amountRefundable > 0 ? amountRefundable : 0)}
        </Text>
        <br />
        <br />
        <Form.Item
          name="date"
          label="Refund date"
          rules={[
            {
              required: true,
              message: "Please enter the refund date",
            },
          ]}
        >
          <DatePicker style={{ width: "250px" }} showTime={false} />
        </Form.Item>
        <Form.Item
          name="refundReason"
          label="Refund reason"
          rules={[
            {
              required: true,
              message: "Please select a refund reason",
            },
          ]}
        >
          <Select
            style={{ width: "250px" }}
            placeholder="Refund reason"
            options={refundReasonOptions}
          />
        </Form.Item>
        <Form.Item
          name="paymentMethod"
          label="Payment method"
          rules={[
            {
              required: true,
              message: "Please select a payment method",
            },
          ]}
        >
          <Select
            style={{ width: "250px" }}
            placeholder="Payment method"
            options={[
              { value: "bank_transfer", label: <span>Bank transfer</span> },
              { value: "check", label: <span>Check</span> },
              { value: "cash", label: <span>Cash</span> },
              { value: "other", label: <span>Other</span> },
              { value: "custom", label: <span>Custom</span> },
            ]}
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.paymentMethod !== currentValues.paymentMethod
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("paymentMethod") === "custom" ? (
              <Form.Item
                name="customPaymentMethodId"
                label="Custom Payment Method"
                rules={[
                  {
                    required: true,
                    message: "Please select a custom payment method",
                  },
                ]}
              >
                <Select
                  style={{ width: "250px" }}
                  placeholder="Payment method"
                  options={[
                    { value: "AirWallex", label: <span>AirWallex</span> },
                    { value: "IbanFirst", label: <span>IbanFirst</span> },
                    { value: "Revolut", label: <span>Revolut</span> },
                  ]}
                />
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <Form.Item
          name="reference"
          label="Reference number"
          rules={[
            {
              required: true,
              message: "Please enter a reference number",
            },
          ]}
        >
          <Input style={{ width: "250px" }} placeholder="Reference number" />
        </Form.Item>
        <Form.Item name="markRefund">
          <Checkbox
            disabled={!showMarkAsRefunded}
            onChange={(e) => {
              offlineRefundForm.setFieldValue("markRefund", e.target.checked);
            }}
          >
            Change the order status to refund{" "}
            <span style={{ color: "red" }}>- danger zone</span>
          </Checkbox>
          <br />
          <Text style={{ color: "gray", fontSize: 13 }}>
            By clicking, we will stop the future invoices & set the subscription
            as refunded. It will also automatically update the deal in Hubspot
            (Closed lost & Refund status).{" "}
            <span style={{ color: "red" }}>
              This action can&apos;t be reverted.
            </span>
          </Text>
        </Form.Item>
      </Form>
    </Modal>
  );
};
