"use client";

import { Typo<PERSON>, Button, Descriptions } from "antd";
import { EditOutlined } from "@ant-design/icons";

import { SelectedProductCard } from "../tabs/SelectedProductCard";
import { useUserRole } from "@/hooks/roles";
import { Offer, Product } from "@/types";

const { Text } = Typography;

type OfferSettingsProductsProps = {
  products: Product[];
  offer: Offer;
  attachedForeverProduct: Product | undefined;
  setEditingOfferProducts: (value: boolean) => void;
};

const NoProducts = () => {
  return (
    <div style={{ marginBottom: "10px", marginTop: "20px" }}>
      <Text style={{ color: "gray" }}>
        No products attached to this offer yet.
      </Text>
    </div>
  );
};

export default function OfferSettingsProducts({
  offer,
  products,
  attachedForeverProduct,
  setEditingOfferProducts,
}: OfferSettingsProductsProps) {
  const { canAccess } = useUserRole();

  return (
    <div className="paradox-card">
      <Descriptions
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <span>Products & recommended products</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Configure all the products & recommended products
              </small>
            </div>
            <Button
              icon={<EditOutlined />}
              style={{ marginLeft: "auto" }}
              onClick={() => {
                if (canAccess("offer-update")) {
                  setEditingOfferProducts(true);
                }
              }}
            >
              Edit
            </Button>
          </div>
        }
        bordered
        column={1}
      >
        {products && offer.config && offer.config?.products ? (
          <>
            <Descriptions.Item label="Products">
              {Object.keys(offer.config?.products).map((key, index) => {
                return (
                  <div key={index} style={{ width: "80%", margin: "10px" }}>
                    <SelectedProductCard
                      key={index}
                      selectedBillingCycles={
                        Object.keys(offer.config?.products[key]).length
                      }
                      product={products.find(
                        (prod) => prod.id.toString() === key
                      )}
                    />
                  </div>
                );
              })}
            </Descriptions.Item>
            <Descriptions.Item label="Recommended">
              {Object.keys(offer.config?.recommended).map((key, index) => {
                return (
                  <div key={index} style={{ width: "80%", margin: "10px" }}>
                    <SelectedProductCard
                      key={index}
                      selectedBillingCycles={
                        Object.keys(offer.config?.recommended[key]).length
                      }
                      product={products.find(
                        (prod) => prod.id.toString() === key
                      )}
                    />
                  </div>
                );
              })}
            </Descriptions.Item>
          </>
        ) : offer.isForever && offer.config ? (
          <Descriptions.Item label="Products">
            <SelectedProductCard
              selectedBillingCycles={
                offer.config?.monthly?.length > 0 &&
                offer.config?.yearly?.length > 0
                  ? 2
                  : 1
              }
              product={attachedForeverProduct}
            />
          </Descriptions.Item>
        ) : (
          <Descriptions.Item>
            <NoProducts />
          </Descriptions.Item>
        )}
      </Descriptions>
    </div>
  );
}
