"use client";

import { CalendarOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { App, Table, Tag, Typography, Divider } from "antd";
import React, { useEffect, useState } from "react";

import ProductsFilterTool from "./ProductsFilterTool";
import CreateProductForm from "./forms/CreateProductForm";
import paginationConfig from "../../form/paginationConfig";
import { useFetchProducts } from "@/hooks/product-catalog/products";
import { useFetchProductFamiliesByLine } from "@/hooks/product-catalog/productFamilies";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchProductLines } from "@/hooks/product-catalog/productLines";
import { useProtectPage } from "@/hooks/roles";
import { formatDate } from "@/lib/date";
import { createProduct } from "@/lib/services";
import { Product, ProductParams, ProductsFilter } from "@/types";

const { Title, Text } = Typography;

const ProductTable = () => {
  const router = useRouter();
  const { canAccess } = useProtectPage("products-list");
  const { message } = App.useApp();
  const [filters, setFilters] = useState<ProductsFilter>({
    search: "",
    status: "active",
    entity: null,
    productFamily: null,
    productLine: null,
  });
  const searchTerm = useDebounce(filters.search);
  // modals
  const [creatingNewProduct, setCreatingNewProduct] = useState(false);

  // Queries
  const {
    isLoading,
    data: products,
    mutate: refetchProducts,
  } = useFetchProducts({
    filters: {
      ...filters,
      search: searchTerm,
    },
  });
  const { data: productLines } = useFetchProductLines({
    filters: {
      status: "active",
      search: "",
    },
  });
  const { data: productFamilies } = useFetchProductFamiliesByLine({
    filters: {
      productLine: filters.productLine,
    },
  });

  // Move columns outside of component or memoize it
  const columns = React.useMemo(
    () => [
      {
        title: "ID",
        dataIndex: "code",
        key: "code",
        render: (data: string) => (
          <Text type="secondary" copyable>
            {data}
          </Text>
        ),
      },
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
        render: (data: string, record: Product) => (
          <div style={{ display: "flex", alignItems: "center" }}>
            <img
              src={record.image}
              width={64}
              height={64}
              style={{ marginRight: 10 }}
            />
            <Text>{record.externalName}</Text>
          </div>
        ),
      },
      {
        title: "Family",
        key: "productFamily",
        render: (data: any) => <Tag>{data?.productFamily?.name}</Tag>,
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        render: (data: string) =>
          data === "active" ? (
            <Tag color="green">Active</Tag>
          ) : (
            <Tag color="orange">Draft</Tag>
          ),
      },
      {
        title: "# Plan(s)",
        key: "plans",
        render: (data: any) => <Text>{data.plans.length}</Text>,
      },
      {
        title: (
          <>
            <CalendarOutlined />
            &nbsp;Created At
          </>
        ),
        dataIndex: "createdAt",
        key: "createdAt",
        render: (data: string) => <Text>{formatDate(data)}</Text>,
      },
    ],
    [] // Empty dependency array since columns don't depend on any props or state
  );

  const handleAdd = async (values: ProductParams) => {
    let params = {
      ...values,
    };
    try {
      let result = await createProduct(params);
      // Currently we are either returning a status code + message for failed requests
      // Or just returning the object from the server. This makes it difficult to set
      // conditions on the response status or other criteria. Our whole request response
      // system needs to be changed on the client and server side to utilize both the status
      // + message along with the returned object.
      if (result?.message) {
        message.open({
          type: "error",
          content: result?.message,
        });
      } else if (result?.code) {
        message.open({
          type: "success",
          content: "Product created successfully",
        });
        router.push(`/dashboard/products/${result.code}`);
      }
    } catch (err) {
      message.open({
        type: "error",
        content: "An error occurred while creating the product",
      });
    }
  };

  useEffect(() => {
    (async () => {
      await refetchProducts();
    })();
  }, [filters.status, filters.productFamily, filters.productLine, searchTerm]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Products</Title>
      <ProductsFilterTool
        filters={filters}
        productFamilies={productFamilies}
        productLines={productLines}
        setFilters={setFilters}
        onCreateProduct={() => {
          if (canAccess("product-create")) {
            setCreatingNewProduct(true);
          }
        }}
        onProductFamilyChange={(id: number) =>
          setFilters({ ...filters, productFamily: id })
        }
        onProductLineChange={(id: number) => {
          let payload = { ...filters, productLine: id };
          if (!id) {
            payload.productFamily = null;
          }
          setFilters(payload);
        }}
      />
      <Divider />
      <Table
        dataSource={products?.items}
        loading={isLoading}
        columns={columns}
        pagination={paginationConfig}
        size="small"
        rowClassName={() => {
          return "paradox-table-row";
        }}
        onRow={(data) => {
          return {
            onClick: () => {
              router.push(`/dashboard/products/${data.code}`);
            },
          };
        }}
        rowKey="id"
      />
      {creatingNewProduct && (
        <CreateProductForm
          isOpen={creatingNewProduct}
          productLines={productLines}
          onCreateProduct={handleAdd}
          onClose={() => setCreatingNewProduct(false)}
        />
      )}
    </div>
  );
};

export default ProductTable;
