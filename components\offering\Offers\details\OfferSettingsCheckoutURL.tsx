"use client";

import { Descriptions, Space, Typography } from "antd";

import { Offer } from "@/types";

const { Text } = Typography;

type OfferSettingsCheckoutURLProps = {
  offer: Offer;
};

export default function OfferSettingsCheckoutURL({
  offer,
}: OfferSettingsCheckoutURLProps) {
  return (
    <Descriptions column={2} size="small" bordered title="Checkout URL">
      <Descriptions.Item label="Status">
        <Space direction="horizontal">
          <div
            style={{
              width: "15px",
              borderRadius: "100%",
              height: "15px",
              backgroundColor: offer.checkoutPage?.isLive
                ? "#5B8C00"
                : "#FF4D4F",
            }}
          >
            {" "}
          </div>
          <div
            style={{
              color: offer.checkoutPage?.isLive ? "#5B8C00" : "#FF4D4F",
            }}
          >
            {offer.checkoutPage?.isLive ? "Live" : "Off"}
          </div>
        </Space>
      </Descriptions.Item>
      <Descriptions.Item label="URL">
        <Text
          copyable
          type="secondary"
          style={{ cursor: "pointer" }}
          onClick={() => {
            window.open(
              `${process.env.NEXT_PUBLIC_CHECKOUT_BASEURL}${offer.slug}`,
              "_blank"
            );
          }}
        >
          {`${process.env.NEXT_PUBLIC_CHECKOUT_BASEURL}${offer.slug}`}
        </Text>
      </Descriptions.Item>
    </Descriptions>
  );
}
