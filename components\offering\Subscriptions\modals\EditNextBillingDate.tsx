"use client";

import { App, DatePicker, Divider, Form, Input, Modal, Typography } from "antd";
import dayjs from "dayjs";

import { useUser } from "@/hooks/utils/useUser";
import { formatDateFromUnix } from "@/lib/date";
import { editNextBillingDate } from "@/lib/services/chargebee";

const { Text } = Typography;

type EditNextBillingDateModalProps = {
  isOpen: boolean;
  nextBillingAt: number;
  subscriptionId: string;
  handleEdit: (shouldReload: boolean) => void;
};

const rules = [{ required: true, message: "This value is required!" }];

export default function EditNextBillingDateModal({
  isOpen,
  nextBillingAt,
  subscriptionId,
  handleEdit,
}: EditNextBillingDateModalProps) {
  const { isSignedIn, emailAddress } = useUser();
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const disabledDate = (current: any) => {
    return (
      (current && current < dayjs().startOf("day")) ||
      current < dayjs.unix(nextBillingAt) ||
      current > dayjs.unix(nextBillingAt).add(3, "month")
    );
  };

  const onFormSubmit = async (values: any) => {
    let response;
    if (!isSignedIn) {
      window.location.href = "/";
      return;
    }
    try {
      response = await editNextBillingDate({
        id: subscriptionId,
        date: dayjs(values.date).unix(),
        comments: values.comments,
        user_email: emailAddress as string,
      });
      if (response.error) {
        message.open({
          type: "error",
          content: response.details.message,
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      handleEdit(!response.error);
    }
  };

  return (
    <Modal
      open={isOpen}
      closeIcon={null}
      width="40%"
      cancelText="Dismiss"
      cancelButtonProps={{ type: "link" }}
      okText="Update"
      okButtonProps={{
        type: "primary",
      }}
      onCancel={() => {
        handleEdit(false);
      }}
      onOk={() => {
        form.submit();
      }}
    >
      <Text strong>Edit the billing date</Text>
      <Divider />

      <Text>
        The next billing date is&nbsp;
        <Text strong>{formatDateFromUnix(nextBillingAt)}</Text>
      </Text>

      <Form
        form={form}
        layout="vertical"
        style={{ marginTop: "20px", marginBottom: "20px" }}
        onFinish={onFormSubmit}
      >
        <Form.Item
          label="Pick a date"
          name="date"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <DatePicker disabledDate={disabledDate} showTime />
        </Form.Item>
        <Form.Item
          label="Comments"
          name="comments"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="Add your comments here" />
        </Form.Item>
        <Text type="secondary">
          Explain why you change the next billing date for this subscription
        </Text>
      </Form>
    </Modal>
  );
}
