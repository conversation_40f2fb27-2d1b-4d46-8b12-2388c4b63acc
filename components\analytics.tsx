"use client";

import { useEffect, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import {
  identifyUser,
  resetAnalytics,
  trackEvent,
  trackPage,
} from "@/lib/segment";
import { useUser } from "@clerk/nextjs";

export default function Analytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { user, isSignedIn, isLoaded } = useUser();
  const hasIdentifiedRef = useRef(false);

  // 🔐 Identify
  useEffect(() => {
    if (!isLoaded) return;

    if (user && isSignedIn && !hasIdentifiedRef.current) {
      identifyUser(user.id, {
        email: user.primaryEmailAddress?.emailAddress,
        firstName: user.firstName,
      });
      hasIdentifiedRef.current = true;
    }

    if (!isSignedIn && hasIdentifiedRef.current) {
      trackEvent("User Logged Out", {}, () => {
        resetAnalytics();
      });
      hasIdentifiedRef.current = false;
    }
  }, [user, isSignedIn, isLoaded]);

  // 📈 Track page views
  useEffect(() => {
    console.debug("track page", pathname, searchParams.toString());
    trackPage();
  }, [pathname, searchParams]);

  return null;
}
