"use client";

import {
  Row,
  Tag,
  Typography,
  Divider,
  Skeleton,
  Descriptions,
  Col,
} from "antd";
import { useRouter } from "next/navigation";
import React from "react";

import { computeTransactionStatusColor } from "./utils/styles";
import { getTransactionDetailsSummary } from "./details/TransactionDetailsSummary";
import { getTransactionDetailsPaymentMethod } from "./details/TransactionDetailsPaymentMethod";
import PageHeader from "../../core/PageHeader";
import { useFetchTransaction } from "@/hooks/accounting/transactions";
import { useProtectPage } from "@/hooks/roles";
import { formatDateFromUnix } from "@/lib/date";
import { LinkStripe } from "./LinkStripe";

type TransactionIdProps = {
  transactionId: string;
};

const { Text } = Typography;

const TransactionDetails = (params: TransactionIdProps) => {
  if (!params.transactionId) {
    throw new Error("Invalid Transaction ID");
  }
  const router = useRouter();
  useProtectPage("transaction-details");
  const { data: transaction, isLoading } = useFetchTransaction(
    params.transactionId
  );

  const transactionSummaryItems = getTransactionDetailsSummary({
    transaction: transaction,
    onSubscriptionClick: (subscriptionId: string) => {
      router.push(`/dashboard/subscriptions/${subscriptionId}`);
    },
    onInvoiceClick: (invoiceId: string) => {
      router.push(`/dashboard/invoices/${invoiceId}`);
    },
    onCreditNoteClick: (creditNoteId: string) => {
      router.push(`/dashboard/credit-notes/${creditNoteId}`);
    },
  });
  const paymentMethodItems = getTransactionDetailsPaymentMethod({
    transaction,
  });

  const bankTransferItems = [
    {
      key: "1",
      label: "Payment method",
      children: <span>Bank Transfer</span>,
    },
  ];

  return isLoading || !transaction ? (
    <Skeleton />
  ) : (
    <div>
      <PageHeader title="Transaction Details">
        <Descriptions
          title=""
          column={3}
          bordered
          size="small"
          style={{ width: "100%" }}
        >
          <Descriptions.Item label="ID">
            <Text copyable={{ text: transaction?.chargebeeId }}>
              {transaction?.chargebeeId}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Date">
            {transaction?.date && formatDateFromUnix(transaction?.date)}
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag color={computeTransactionStatusColor(transaction.status)}>
              <Text style={{ fontSize: 13 }}>{transaction?.status}</Text>
            </Tag>
          </Descriptions.Item>
          {transaction?.idAtGateway &&
            transaction?.idAtGateway.startsWith("ch_") && (
              <Descriptions.Item label="Stripe link">
                <LinkStripe idAtGateway={transaction?.idAtGateway} />
              </Descriptions.Item>
            )}
        </Descriptions>
      </PageHeader>
      <Divider />
      <Row>
        <Col span={12}>
          <Descriptions
            title="Transaction Summary"
            items={transactionSummaryItems}
            column={1}
            bordered
            size="small"
          />
        </Col>
        <Col span={12} style={{ paddingLeft: "20px" }}>
          {transaction.paymentMethod === "card" ? (
            <Descriptions
              title="Payment Method"
              items={paymentMethodItems}
              column={1}
              bordered
              size="small"
            />
          ) : (
            <Descriptions
              title="Bank Transfer"
              items={bankTransferItems}
              column={1}
              bordered
              size="small"
            />
          )}
        </Col>
      </Row>
    </div>
  );
};

export default TransactionDetails;
