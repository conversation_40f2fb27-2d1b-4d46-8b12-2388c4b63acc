"use client";

import { useState } from "react";

import { getLWCoursesOfUser } from "@/lib/services/product-delivery/learnWorlds";
import { useProtectPage } from "@/hooks/roles";
import {
  getComunityMemberSpacesAccesses,
  getComunityMemberSpacesByEmail,
} from "@/lib/services/product-delivery";
import DebugAccessesTabs from "./DebugAccessesTabs";
import DebugAccessesLW from "./DebugAccessesLW";
import DebugAccessesCircle from "./DebugAccessesCircle";

export type TabState = {
  lwDetails: any;
  lwLoading: boolean;
  circleDetails: any;
  circleLoading: boolean;
  currentTab: "lw" | "circle";
  current_community: "pxl" | "pxs";
};

export default function DebugAccesses({ email }: { email: string }) {
  const { canAccess } = useProtectPage("customer-details");
  const [tabState, setTabState] = useState<TabState>({
    lwDetails: {},
    lwLoading: false,
    circleDetails: {},
    circleLoading: false,
    currentTab: "lw",
    current_community: "pxl",
  });

  const handleTabChange = (value: TabState["currentTab"]) => {
    setTabState({ ...tabState, currentTab: value });
  };

  const handleGetCoursesOfUser = async () => {
    if (canAccess("customer-verify-access")) {
      setTabState((prev) => ({ ...prev, lwLoading: true }));
      const coursesData = await getLWCoursesOfUser(email);
      setTabState((prev) => ({
        ...prev,
        lwDetails: coursesData,
        lwLoading: false,
      }));
    }
  };

  const handleGetSpaceGroupUserAccesses = async () => {
    if (canAccess("customer-verify-access")) {
      setTabState((prev) => ({ ...prev, circleLoading: true }));
      const { pxl, pxs } = await getComunityMemberSpacesAccesses(email);
      setTabState((prev) => ({
        ...prev,
        circleDetails: { pxl, pxs },
        circleLoading: false,
      }));
    }
  };

  const handleCirclePageChange = async (page: number, _: number) => {
    setTabState((prev) => ({
      ...prev,
      circleLoading: true,
    }));
    const data = await getComunityMemberSpacesByEmail(
      email,
      tabState.current_community,
      page
    );

    setTabState((prev) => ({
      ...prev,
      circleDetails: {
        ...prev.circleDetails,
        [tabState.current_community]: data,
      },
      circleLoading: false,
    }));
  };

  return (
    <div>
      <DebugAccessesTabs
        currentTab={tabState.currentTab}
        onTabChange={(value) =>
          handleTabChange(value as TabState["currentTab"])
        }
      />
      <div style={{ marginTop: 16 }}></div>
      {tabState.currentTab === "lw" && (
        <DebugAccessesLW
          state={tabState}
          handleGetCoursesOfUser={handleGetCoursesOfUser}
        />
      )}
      {tabState.currentTab === "circle" && (
        <DebugAccessesCircle
          state={tabState}
          onChangeCommunity={(value: TabState["current_community"]) =>
            setTabState((prev) => ({ ...prev, current_community: value }))
          }
          onGetSpaceGroupUserAccesses={handleGetSpaceGroupUserAccesses}
          onCirclePageChange={handleCirclePageChange}
        />
      )}
    </div>
  );
}
