"use client";

import { EditOutlined, MailOutlined, SyncOutlined } from "@ant-design/icons";
import { <PERSON>ton, Card, Col, Form, Input, Row, Select, Typography } from "antd";
import FormItem from "antd/es/form/FormItem";
import { Customer } from "chargebee";
import { FormProps } from "rc-field-form";
import countryList from "react-select-country-list";
import { useEffect, useMemo, useState } from "react";

import InputPhone from "@/components/form/InputPhone";
import {
  CustomerInfo,
  DealDetailsCustomers,
  DealDetailsResponse,
  WorkflowStep2Props,
} from "@/types";
import { getCustomerByDealID } from "@/lib/services";

const { Text } = Typography;

const rules = [{ required: true, message: "This value is required!" }];

type FieldType = {
  dealID?: string;
  customer?: any;
  customerInfo?: CustomerInfo;
};

const initialValuesForDeal: DealDetailsResponse = {
  total: 0,
  customers: [],
  message: "",
};

const initialValuesCustomerForm: CustomerInfo = {
  id: "",
  email: "",
  first_name: "",
  last_name: "",
  phone: "",
  country: "",
  city: "",
  line1: "",
  line2: "",
  zip: "",
};

const formatCustomerData = (data: any) => {
  if (!data) return initialValuesCustomerForm;
  return {
    id: data.id,
    email: data.email,
    first_name: data.first_name,
    last_name: data.last_name,
    phone: data.phone,
    country: data?.country || data?.billing_address?.country,
    line1: data?.line1 || data?.billing_address?.line1,
    line2: data?.line2 || data?.billing_address?.line2,
    city: data?.city || data?.billing_address?.city,
    zip: data?.zip || data?.billing_address?.zip,
  };
};

type StepData = {
  customerFormIsVisible: boolean;
  selectedCustomer: string | null;
  dealDetails: DealDetailsResponse;
  customerDetails: any | null;
};

const WorkflowStep2 = ({
  customerData,
  dealID,
  setDealID,
  handleCustomerChange,
}: WorkflowStep2Props) => {
  const [form] = Form.useForm();
  const [stepData, setStepData] = useState<StepData>({
    customerFormIsVisible: false,
    selectedCustomer: null,
    dealDetails: initialValuesForDeal,
    customerDetails: null,
  });
  const countryListOptions = useMemo(() => countryList().getData(), []);

  /**
   * 1. Handle the submit of the deal ID
   */
  const handleDealID = async () => {
    if (!dealID) return;
    setStepData({
      ...stepData,
      selectedCustomer: null,
      customerFormIsVisible: false,
      dealDetails: initialValuesForDeal,
    });
    handleCustomerChange(formatCustomerData(null));
    const response = await getCustomerByDealID(dealID as string);
    console.log("ℹ️ handleDealID", { dealID, response });
    setStepData({
      ...stepData,
      dealDetails: response,
      customerFormIsVisible: response.customers.length > 0,
      customerDetails:
        response.customers.length > 0 ? response.customers[0] : null,
    });
    if (response.error) {
      setDealID(null);
    }
  };

  /**
   * Validate the form and submit the customer data
   */
  const onValuesChange: FormProps<FieldType>["onValuesChange"] = (
    _: any,
    values: any
  ) => {
    if (!values) return;
    if (
      values.email &&
      values.first_name &&
      values.last_name &&
      values.country
    ) {
      handleCustomerChange(formatCustomerData(values));
      form.submit();
    }
  };

  useEffect(() => {
    if (
      stepData.customerDetails === undefined ||
      stepData.dealDetails.total === 0
    )
      return;
    // Determine if the customer exists in Chargebee (based on dealDetails)
    console.log("ℹ️ useEffect", { stepData });
    let customerExists = false;
    let dealDetailsCustomer = {} as unknown as DealDetailsCustomers;
    let chargebeeCustomer = {} as unknown as Customer;
    if (stepData.dealDetails.customers?.length) {
      dealDetailsCustomer = stepData.dealDetails.customers[0];
      customerExists = dealDetailsCustomer.wasFoundInChargeBee;
    }
    if (stepData.customerDetails) {
      chargebeeCustomer = stepData.customerDetails;
    }
    // Set values for the form
    let formData = {};
    if (customerExists) {
      formData = {
        ...chargebeeCustomer,
        id: chargebeeCustomer?.id,
        first_name: chargebeeCustomer?.firstName,
        last_name: chargebeeCustomer?.lastName,
        country:
          chargebeeCustomer?.country ||
          chargebeeCustomer?.billing_address?.country,
        line1:
          chargebeeCustomer?.line1 || chargebeeCustomer?.billing_address?.line1,
        line2:
          chargebeeCustomer?.line2 || chargebeeCustomer?.billing_address?.line2,
        city:
          chargebeeCustomer?.city || chargebeeCustomer?.billing_address?.city,
        zip: chargebeeCustomer?.zip || chargebeeCustomer?.billing_address?.zip,
      };
    } else {
      // fixes a bug when a customer does not exist in chargebee, the country
      // dropdown is populated with the wrong value. We will let the user
      // select the country using the form.
      dealDetailsCustomer.country = undefined;
      formData = {
        ...dealDetailsCustomer,
        id: undefined,
        line1: dealDetailsCustomer?.address,
        city: dealDetailsCustomer?.city,
        zip: dealDetailsCustomer?.zip,
      };
    }

    console.log("ℹ️ useEffect", {
      chargebeeCustomer,
      dealDetailsCustomer,
      formData,
    });
    // Fill the form with the customer data
    form.setFieldsValue(formData);

    // Update the Workflow State
    handleCustomerChange(formatCustomerData(formData));

    console.log("ℹ️", { customerExists, formData });

    setStepData({
      ...stepData,
      customerFormIsVisible: true,
    });
  }, [stepData.dealDetails, stepData.customerDetails, form]);

  return (
    <>
      <Card
        style={{ marginBottom: 16, padding: 16 }}
        title={
          <>
            <Text strong>Deal hubspot</Text>
            <Text type="secondary">
              &nbsp;We will update the Subscription information in Paradox OS in
              Hubspot
            </Text>
          </>
        }
      >
        <Row style={{ marginBottom: 6, display: "flex", width: 550 }}>
          <Col span={19}>
            <Input
              placeholder="Deal id"
              size="large"
              suffix={<EditOutlined />}
              value={dealID as string}
              onChange={(e) => setDealID(e.target.value)}
            />
          </Col>
          <Col span={4} offset={1}>
            <Button
              type="primary"
              icon={<SyncOutlined />}
              size="large"
              onClick={() => handleDealID()}
              disabled={!dealID}
            >
              Sync from Hubspot
            </Button>
          </Col>
        </Row>
        <Text type="secondary">
          This field is mandatory. If you don’t have a deal id, go in Hubspot
          and create one before mooving forward.
        </Text>
      </Card>

      <Form
        form={form}
        onValuesChange={onValuesChange}
        initialValues={customerData}
        layout="vertical"
        wrapperCol={{ span: 24 }}
        style={{
          marginTop: 16,
          marginBottom: 16,
          display: !stepData.customerFormIsVisible ? "none" : "block",
        }}
      >
        <Card title="Customer's information">
          <FormItem name="id" style={{ display: "none" }}>
            <Input type="hidden" value={stepData.selectedCustomer || ""} />
          </FormItem>
          <FormItem
            name="email"
            label="Email"
            rules={[
              ...rules,
              {
                type: "email",
                message: "This value must be a valid email",
              },
            ]}
            style={{ width: "100%" }}
          >
            <Input
              type="email"
              placeholder="Email"
              size="large"
              suffix={<MailOutlined />}
              value={form?.getFieldValue("email")}
            />
          </FormItem>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <FormItem
                name="first_name"
                rules={rules}
                label="First name"
                style={{ width: "100%" }}
              >
                <Input
                  placeholder="First name"
                  size="large"
                  suffix={<EditOutlined />}
                  value={form?.getFieldValue("first_name")}
                />
              </FormItem>
            </Col>
            <Col span={12} style={{ width: 500 }}>
              <FormItem
                name="last_name"
                rules={rules}
                label="Last name"
                style={{ width: "100%" }}
              >
                <Input
                  placeholder="Last name"
                  size="large"
                  suffix={<EditOutlined />}
                  value={form?.getFieldValue("last_name")}
                />
              </FormItem>
            </Col>
          </Row>
          <FormItem
            name="country"
            style={{ width: "50%" }}
            rules={rules}
            label="Country"
          >
            <Select
              placeholder="Country"
              options={countryListOptions}
              showSearch
              optionFilterProp="label"
            />
          </FormItem>
          <FormItem name="zip" style={{ display: "none" }} label="Zip">
            <Input
              type="hidden"
              placeholder="Zip"
              size="large"
              suffix={<EditOutlined />}
              value={form?.getFieldValue("zip")}
            />
          </FormItem>
          <FormItem name="phone" label="Phone number" style={{ width: "100%" }}>
            <InputPhone value={form?.getFieldValue("phone")} />
          </FormItem>
        </Card>
      </Form>

      {stepData.dealDetails.message ? (
        <Card
          color="danger"
          title="❌ Error"
          style={{
            marginTop: 16,
            marginBottom: 16,
          }}
        >
          {stepData.dealDetails.message}
        </Card>
      ) : null}
    </>
  );
};

export default WorkflowStep2;
