"use client";

import { App, FormInstance } from "antd";
import { Comment, Subscription } from "chargebee";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import useSWR, { SWRConfiguration } from "swr";

import {
  getSubscriptionsParadoxApi,
  resyncSubscription,
  getSubscriptionDataFromPxApi,
  exportSubscriptionData,
} from "@/lib/services";
import {
  getComments,
  getChargebeeSubscription,
} from "@/lib/services/chargebee";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchOffers } from "@/hooks/offering/offers";
import { useFetchProducts } from "@/hooks/product-catalog/products";
import { useProtectPage } from "@/hooks/roles";
import {
  GetSubscriptionsParadoxApi,
  SubscriptionListResponse,
  SubscriptionsFilter,
} from "@/types";

type UseFetchSubscriptionsParams = {
  filters?: SubscriptionsFilter;
  options?: SWRConfiguration;
  page?: number;
};

/**
 * Prepares filter options for fetching subscriptions
 * @param filters - Optional subscription filters including name, status, product, offer, and isForever
 * @returns GetSubscriptionsParadoxApi options object
 */
function prepareFetchSubscriptions(filters?: SubscriptionsFilter) {
  let options: GetSubscriptionsParadoxApi = {};
  options.searchQuery = (filters?.name as string) || "";
  if (filters) {
    if (filters.orderStatus) options.status = filters.orderStatus as string;
    if (filters.product) options.productId = filters.product as string;
    if (filters.offer) options.offerId = filters.offer as string;
    if (filters.isForever !== undefined)
      options.isForever = filters.isForever.toString();
  }
  return options;
}

/**
 * Hook to fetch a paginated list of subscriptions with optional filters
 * @param params - Optional parameters including filters, ReactQuery options, and page number
 * @returns Query result containing subscription list data
 */
export const useFetchSubscriptions = (params?: UseFetchSubscriptionsParams) => {
  const options: GetSubscriptionsParadoxApi = prepareFetchSubscriptions(
    params?.filters
  );
  return useSWR<SubscriptionListResponse>(
    `useFetchSubscriptions-${JSON.stringify(options)}`,
    () => getSubscriptionsParadoxApi(options),
    params?.options
  );
};

/**
 * Hook to fetch a single subscription by ID
 * @param subscriptionId - The ID of the subscription to fetch
 * @param options - Optional ReactQuery configuration options
 * @returns Query result containing subscription data
 */
export const useFetchSubscription = (subscriptionId: string) => {
  const { data, error, isLoading } = useSWR<Subscription>(
    `useFetchSubscription`,
    () => getChargebeeSubscription(subscriptionId)
  );

  return {
    data,
    error,
    isLoading,
  };
};

/**
 * Hook to fetch subscription comments
 * @param subscriptionId - The ID of the subscription to fetch
 * @returns Query result containing subscription comments data
 */
export const useFetchSubscriptionComments = (subscriptionId: string) => {
  return useSWR<Comment.ListResponse>(`useFetchSubscriptionComments`, () =>
    getComments(subscriptionId)
  );
};

type PageState = {
  page: number;
  filters: SubscriptionsFilter;
  search: string;
  manualAddModal: boolean;
  exportModal: boolean;
  importJsonModal: boolean;
};

const initialFilters = {
  name: "",
  status: "",
  product: "",
  offer: "",
};

type UseSubscriptionsTableParams = {
  displayForever: boolean;
  filterForm: FormInstance;
};

/**
 * Hook to fetch subscriptions table data
 * @param displayForever - Whether to display forever subscriptions
 * @param filterForm - Form instance for filtering subscriptions
 * @returns Hook state and functions for subscriptions table
 */
export const useSubscriptionsTable = ({
  displayForever,
  filterForm,
}: UseSubscriptionsTableParams) => {
  // use params so set initial state for status
  const searchParams = useSearchParams();
  const status = searchParams.get("status");
  const [pageState, setPageState] = useState<PageState>({
    page: 1,
    filters: {
      ...initialFilters,
      orderStatus: (status as SubscriptionsFilter["orderStatus"]) || "",
    },
    search: "",
    manualAddModal: false,
    exportModal: false,
    importJsonModal: false,
  });
  const debouncedSearch = useDebounce(pageState.search);

  // Queries
  const {
    isLoading,
    data: subscriptions,
    mutate: refetchSubscriptions,
  } = useFetchSubscriptions({
    page: pageState.page,
    filters: {
      ...pageState.filters,
      name: debouncedSearch,
      isForever: displayForever,
    },
  });
  const { data: offers } = useFetchOffers({
    filters: { status: "active", name: "" },
  });
  const { data: products } = useFetchProducts({
    filters: {
      page: 1,
      status: "active",
      search: "",
    },
  });

  const clearFilters = async () => {
    setPageState({
      ...pageState,
      search: "",
      filters: initialFilters,
    });
    filterForm.resetFields();
  };

  useEffect(() => {
    if (
      status &&
      ["active", "paid", "refunded", "stopped", "overdue"].includes(status)
    ) {
      filterForm.setFieldsValue({
        orderStatus: status,
      });
    }
  }, []);

  return {
    isLoading,
    offers,
    pageState,
    products,
    subscriptions,
    clearFilters,
    refetchSubscriptions,
    setPageState,
  };
};

type Modals = {
  cancelSubIsOpen: boolean;
  editNextBillingDateIsOpen: boolean;
  changePaymentMethodIsOpen: boolean;
  editSubscriptionIsOpen: boolean;
  downsellIsOpen: boolean;
};

export const useSubscriptionDetails = (id: string) => {
  const { message } = App.useApp();
  const { canAccess } = useProtectPage("subscription-details");
  // States
  const [data, setData] = useState<any>({
    api: undefined,
    chargebee: undefined,
    history: undefined,
    lineItems: undefined,
    events: undefined,
  });
  const [modals, setModals] = useState<Modals>({
    cancelSubIsOpen: false,
    editNextBillingDateIsOpen: false,
    changePaymentMethodIsOpen: false,
    editSubscriptionIsOpen: false,
    downsellIsOpen: false,
  });

  const handleRefreshSubscription = async () => {
    try {
      const resyncedSubscription = await resyncSubscription(id);
      setData({
        ...data,
        api: resyncedSubscription,
      });
      message.open({
        type: "success",
        content: "Subscription refreshed successfully.",
      });
    } catch (error) {
      console.error("Error refreshing subscription:", error);
      message.open({
        type: "error",
        content: "Failed to refresh subscription. Please try again.",
      });
    }
  };

  const handleChangePaymentMethod = () => {
    if (canAccess("subscription-change-payment-method")) {
      setModals({ ...modals, changePaymentMethodIsOpen: true });
    }
  };
  const handleEditNextBillingCycle = () => {
    if (canAccess("subscription-update-next-billing-date")) {
      setModals({ ...modals, editNextBillingDateIsOpen: true });
    }
  };
  const handleEditSubscription = () => {
    if (canAccess("subscription-edit")) {
      setModals({ ...modals, editSubscriptionIsOpen: true });
    }
  };
  const handleDownsellSubscription = () => {
    if (canAccess("subscription-downsell")) {
      setModals({ ...modals, downsellIsOpen: true });
    }
  };
  const handleCancelSubscription = () => {
    if (canAccess("subscription-cancel")) {
      setModals({ ...modals, cancelSubIsOpen: true });
    }
  };

  const handleExportSubscriptionData = async () => {
    if (canAccess("subscription-debug-export")) {
      const data = await exportSubscriptionData(id);
      // Download the data
      const a = document.createElement("a");
      a.href = data;
      a.download = `${id}-data.json`;
      a.click();
    }
  };

  const fetchSubscriptionData = async (subscriptionId: string) => {
    const subscriptionQueries = await Promise.all([
      getChargebeeSubscription(subscriptionId),
      getSubscriptionDataFromPxApi(subscriptionId),
    ]);
    setData({
      chargebee: subscriptionQueries[0],
      api: subscriptionQueries[1].subscription,
      history: subscriptionQueries[1].history,
      lineItems: subscriptionQueries[1].lineItems,
      events: subscriptionQueries[1].events,
    });
  };

  useEffect(() => {
    if (!id) return;
    fetchSubscriptionData(id);
  }, [id]);

  return {
    subscriptionCBInfo: data.chargebee,
    subscriptionPX: data.api,
    historicalDataPX: data.history,
    subscriptionLineItems: data.lineItems,
    subscriptionEvents: data.events,
    modals,
    fetchSubscriptionData,
    handleChangePaymentMethod,
    handleEditNextBillingCycle,
    handleEditSubscription,
    handleDownsellSubscription,
    handleCancelSubscription,
    handleRefreshSubscription,
    handleExportSubscriptionData,
    setModals,
  };
};
