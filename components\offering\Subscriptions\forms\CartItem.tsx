"use client";

import { Col, InputN<PERSON>ber, Row, Space, Typography } from "antd";
import { InvoiceEstimate } from "chargebee";
import { useState } from "react";

import { formatCents } from "@/lib/currency";
import { CartItemProps, Offer } from "@/types";

const { Text } = Typography;

const displayNonDiscountedAmount = (
  lineItem: InvoiceEstimate.LineItem,
  item: any
) => {
  const lineItemDiscount = lineItem?.item_level_discount_amount;
  const documentLevelDiscount = lineItem?.discount_amount;

  if (documentLevelDiscount || lineItemDiscount) {
    return (
      <Text delete>
        {formatCents(item.itemPrice.price * item.quantity)}
        &nbsp;
      </Text>
    );
  } else {
    return null;
  }
};

const displayDiscountedAmount = (
  lineItem: InvoiceEstimate.LineItem,
  item: any
) => {
  const lineItemDiscount = lineItem?.item_level_discount_amount;
  const documentLevelDiscount = lineItem?.discount_amount;

  if (lineItemDiscount) {
    lineItem?.item_level_discount_amount;
    return formatCents((lineItem.amount as number) - lineItemDiscount);
  } else if (documentLevelDiscount) {
    return formatCents((lineItem.amount as number) - documentLevelDiscount);
  } else {
    return formatCents(lineItem?.amount as number);
  }
};

export default function CartItem({
  billingCycle,
  itemData,
  offer,
  estimation,
  handleQtyChange,
}: CartItemProps) {
  const [_, setQty] = useState<number>(itemData.quantity);

  if (!itemData || !estimation || !Object.keys(offer as Offer).length)
    return null;

  const lines = estimation?.invoice_estimate
    ?.line_items as InvoiceEstimate["line_items"];
  if (!lines) return null;

  const currentLineItem = lines.find(
    (lineItem) => lineItem?.entity_id === itemData.itemPrice.id
  );
  if (!currentLineItem) return null;

  const handleChange = (value: number | null) => {
    handleQtyChange(value as number);
    setQty(value as number);
  };

  return (
    <Row style={{ width: "100%", marginTop: 10 }}>
      <Space
        direction="vertical"
        style={{
          display: "flex",
          flexDirection: "row",
          width: "100%",
          alignItems: "center",
          justifyContent: "flex-start",
        }}
      >
        {itemData?.image ? (
          <div style={{ display: "flex", marginRight: 10 }}>
            <img
              src={itemData?.image}
              key={itemData?.image}
              width={32}
              height={32}
            />
          </div>
        ) : null}
        <div
          style={{ display: "flex", flexDirection: "column", marginRight: 10 }}
        >
          <Col style={{ marginBottom: 5 }}>&nbsp;{itemData.name}</Col>
          <InputNumber
            controls={true}
            onChange={handleChange}
            min={1}
            value={itemData.quantity}
            style={{ width: 120 }}
            disabled={!itemData.canChangeQuantity}
            size="small"
          />
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            marginLeft: "auto",
          }}
        >
          <Row style={{ fontSize: "0.8rem" }}>
            {displayNonDiscountedAmount(currentLineItem, itemData)}
            <Text style={{ color: "#096DD9", fontSize: "0.95rem" }}>
              {displayDiscountedAmount(currentLineItem, itemData)}
            </Text>
            <span>&nbsp;{billingCycle > 1 ? "per month" : ""}</span>
          </Row>
          {billingCycle > 1 ? (
            <Row style={{ fontSize: "0.8rem" }}>
              <Text type="secondary">during {billingCycle} months</Text>
            </Row>
          ) : null}
        </div>
      </Space>
    </Row>
  );
}
