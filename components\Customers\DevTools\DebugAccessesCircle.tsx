"use client";

import {
  <PERSON><PERSON>,
  Divider,
  Segmented,
  Collapse,
  List,
  Tag,
  Typography,
  Pagination,
  Row,
} from "antd";
import Link from "next/link";

import { TabState } from "./DebugAccesses";

const { Text } = Typography;

type DebugAccessesCircleProps = {
  state: TabState;
  onCirclePageChange: (page: number, pageSize: number) => void;
  onChangeCommunity: (value: TabState["current_community"]) => void;
  onGetSpaceGroupUserAccesses: () => void;
};

export default function DebugAccessesCircle({
  state,
  onChangeCommunity,
  onGetSpaceGroupUserAccesses,
  onCirclePageChange,
}: DebugAccessesCircleProps) {
  const items = Object.keys(
    state.circleDetails[state.current_community]?.records || {}
  ).map((key) => ({
    key,
    label: `${
      state.circleDetails[state.current_community]?.records[key][0]
        .space_group_name
    } (${state.circleDetails[state.current_community]?.records[key].length})`,
    children: (
      <List
        size="small"
        dataSource={state.circleDetails[state.current_community]?.records[key]}
        loading={state.circleLoading}
        renderItem={(item: any) => (
          <List.Item>
            <List.Item.Meta
              title={
                <Link passHref={true} target="_blank" href={item.url}>
                  {item.name} &nbsp;
                  <Tag color="blue">{item.slug}</Tag>
                  <Text copyable>{item.id}</Text>
                </Link>
              }
              avatar={item.emoji}
            />
          </List.Item>
        )}
      />
    ),
  }));

  return (
    <>
      <Button
        type="primary"
        onClick={onGetSpaceGroupUserAccesses}
        loading={state.circleLoading}
      >
        Verify Space Group Access
      </Button>
      <Divider />
      <Segmented
        options={[
          { label: "PXL", value: "pxl" },
          { label: "PXS", value: "pxs" },
        ]}
        onChange={(value) => {
          onChangeCommunity(value as TabState["current_community"]);
        }}
      />
      {state.circleDetails && (
        <>
          {Object.keys(
            state.circleDetails[state.current_community]?.records || {}
          ).length > 0 ? (
            <>
              <Pagination
                style={{ marginTop: 16 }}
                total={state.circleDetails[state.current_community]?.count}
                pageSize={
                  state.circleDetails[state.current_community]?.per_page
                }
                current={
                  state.circleDetails[state.current_community]?.current_page
                }
                hideOnSinglePage={true}
                showSizeChanger={false}
                onChange={onCirclePageChange}
              />
              <Divider />
              <Collapse items={items} />
            </>
          ) : (
            <Row justify="center">
              <Text>No records found</Text>
            </Row>
          )}
        </>
      )}
    </>
  );
}
