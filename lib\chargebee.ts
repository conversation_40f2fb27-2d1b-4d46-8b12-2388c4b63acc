"use client";

/**
 * Returns the Chargebee link to the subscription with the given ID.
 * @param subscriptionCBId - The ID of the subscription.
 * @returns The Chargebee link to the subscription.
 */
export const getSubscriptionLink = (subscriptionCBId: string) => {
  if (!subscriptionCBId) return "";
  return `https://${process.env.NEXT_PUBLIC_CHARGEBEE_DOMAIN}.chargebee.com/d/subscriptions/${subscriptionCBId}`;
};

/**
 * Returns the Chargebee link to the customer with the given ID.
 * @param customerCBId - The ID of the customer.
 * @returns The Chargebee link to the customer.
 */
export const getCustomerLink = (customerCBId: string) => {
  if (!customerCBId) return "";
  return `https://${process.env.NEXT_PUBLIC_CHARGEBEE_DOMAIN}.chargebee.com/d/customers/${customerCBId}`;
};

/**
 * Returns the Chargebee link to the plan with the given ID.
 * @param planId - The ID of the plan.
 * @returns The Chargebee link to the plan.
 */
export const getPlanLink = (planId: string) => {
  if (!planId) return "";
  return `https://${process.env.NEXT_PUBLIC_CHARGEBEE_DOMAIN}.chargebee.com/d/plans/${planId}`;
};

/**
 * Returns the Chargebee link to the addon with the given ID.
 * @param addonId - The ID of the addon.
 * @returns The Chargebee link to the addon.
 */
export const getAddonLink = (addonId: string) => {
  if (!addonId) return "";
  return `https://${process.env.NEXT_PUBLIC_CHARGEBEE_DOMAIN}.chargebee.com/d/addons/${addonId}`;
};
