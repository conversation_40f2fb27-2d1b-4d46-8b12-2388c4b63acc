"use client";

import { CalendarOutlined } from "@ant-design/icons";
import { memo, useCallback, useMemo, useState } from "react";
import {
  App,
  <PERSON><PERSON>,
  Col,
  Divider,
  Drawer,
  Form,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
} from "antd";
import { JsonEditor } from "json-edit-react";

import { useProtectPage } from "@/hooks/roles";
import { useFetchCronLogs } from "@/hooks/dev/cronLogs";
import { formatDuration } from "@/lib/date";
import { reprocessCronLog } from "@/lib/services/cronLogs";
import { AlteredCronLog, CronLog, CronLogType, CronLogsFilter } from "@/types";

const { Text, Title } = Typography;

// Memoized constant values
const INITIAL_FILTERS: CronLogsFilter = {
  type: "OVERDUE_INVOICE_CHECK",
  status: undefined,
  page: 1,
  limit: 25,
};

const TYPE_OPTIONS = [
  {
    value: "ACCESS_SUSPENSION_CHECK",
    label: <span>ACCESS_SUSPENSION_CHECK</span>,
  },
  {
    value: "ERRORED_CUSTOMER_ACCESS_CHECK",
    label: <span>ERRORED_CUSTOMER_ACCESS_CHECK</span>,
  },
  {
    value: "EXCHANGE_RATE_CRAWLER",
    label: <span>EXCHANGE_RATE_CRAWLER</span>,
  },
  {
    value: "OVERDUE_INVOICE_CHECK",
    label: <span>OVERDUE_INVOICE_CHECK</span>,
  },
  {
    value: "FAILED_WEBHOOKS",
    label: <span>FAILED_WEBHOOKS</span>,
  },
];

const STATUS_OPTIONS = [
  { value: "SUCCESS", label: <span>SUCCESS</span> },
  { value: "FAILED", label: <span>FAILED</span> },
  { value: "RUNNING", label: <span>RUNNING</span> },
];

// Memoized Status Tag component
const StatusTag = memo(({ status }: { status: string }) => {
  switch (status) {
    case "SUCCESS":
      return <Tag color="green">{status}</Tag>;
    case "FAILED":
      return <Tag color="red">{status}</Tag>;
    case "RUNNING":
      return <Tag color="blue">{status}</Tag>;
    default:
      return <Tag>{status}</Tag>;
  }
});
StatusTag.displayName = "StatusTag";

// Memoized Filter Form component
const FilterForm = memo(({ form, onValuesChange, initialType }: any) => (
  <Form
    form={form}
    onValuesChange={onValuesChange}
    initialValues={{ type: initialType }}
  >
    <div style={{ display: "flex", justifyContent: "space-between" }}>
      <Form.Item name="type" style={{ width: "25%" }}>
        <Select placeholder="Type" options={TYPE_OPTIONS} />
      </Form.Item>
      <Form.Item name="status" style={{ width: "25%" }}>
        <Select allowClear placeholder="Status" options={STATUS_OPTIONS} />
      </Form.Item>
      <Button type="link" onClick={() => form.resetFields()}>
        Clear Filters
      </Button>
    </div>
  </Form>
));
FilterForm.displayName = "FilterForm";

// Memoized Drawer Content component
const DrawerContent = memo(
  ({ current, onEdit, processing, canReprocess, onReprocess }: any) => (
    <>
      <Space direction="vertical">
        <Tag color="blue">
          <Text copyable>{current?.id}</Text>
        </Tag>
      </Space>
      <Divider />
      <Title level={3}>Payload</Title>
      {current?.result && current.result.input ? (
        <>
          <Title level={4}>Input</Title>
          <JsonEditor data={current?.result.input} onEdit={onEdit} />
        </>
      ) : null}
      {current?.result && current.result.message ? (
        <>
          <Title level={4}>Message</Title>
          <blockquote>{current?.result.message}</blockquote>
        </>
      ) : null}
      {current?.result && current.result.output ? (
        <>
          <Title level={4}>Output (readonly)</Title>
          <JsonEditor
            data={current?.result.output}
            restrictAdd={true}
            restrictDelete={true}
            restrictDrag={true}
            restrictEdit={true}
          />
        </>
      ) : null}
      {!current?.result && <Text type="secondary">No payload</Text>}
      {!current?.result.input && !current?.result.output && (
        <Text type="secondary">Not a valid payload</Text>
      )}
      <Divider />
      <Title level={3}>Error</Title>
      {current?.error && <JsonEditor data={current?.error} />}
      {!current?.error && <Text type="secondary">No error object</Text>}
      <Divider />
      <Space direction="vertical">
        {current?.modified && (
          <Tag color="orange">{`⚠️ You've modified the payload`}</Tag>
        )}
        <Button
          type="primary"
          disabled={
            processing ||
            current?.status === "SUCCESS" ||
            current?.status === "RUNNING" ||
            !current?.result.input
          }
          onClick={onReprocess}
        >
          Re-process
        </Button>
      </Space>
    </>
  )
);
DrawerContent.displayName = "DrawerContent";

type PageState = {
  isOpen: boolean;
  filters: CronLogsFilter;
  current: AlteredCronLog | null;
  processing: boolean;
};

export default function CronLogsTable() {
  const { message } = App.useApp();
  const { canAccess } = useProtectPage("cron-logs-list");
  const [pageState, setPageState] = useState<PageState>({
    isOpen: false,
    filters: INITIAL_FILTERS,
    current: null,
    processing: false,
  });
  const [filterForm] = Form.useForm();

  const {
    isLoading,
    data: cronLogs,
    mutate: refresh,
  } = useFetchCronLogs({ filters: pageState.filters });

  // Memoized columns definition
  const columns = useMemo(
    () => [
      { title: "Id", dataIndex: "id", key: "id" },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        render: (text: string) => <Tag>{text}</Tag>,
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        render: (text: string) => <StatusTag status={text} />,
      },
      {
        title: "Duration",
        dataIndex: "duration",
        render: (_: string, record: CronLog) => {
          const startTime = new Date(record.startTime);
          const endTime = record.endTime
            ? new Date(record.endTime)
            : new Date();
          return formatDuration(startTime.toString(), endTime.toString());
        },
      },
      {
        title: (
          <>
            <CalendarOutlined />
            &nbsp;Created At
          </>
        ),
        dataIndex: "createdAt",
        key: "createdAt",
      },
      {
        title: (
          <>
            <CalendarOutlined />
            &nbsp;Updated At
          </>
        ),
        dataIndex: "updatedAt",
        key: "updatedAt",
      },
    ],
    []
  );

  const filterCronLogs = useCallback((changedValues: any) => {
    setPageState((prev) => ({
      ...prev,
      filters: { ...prev.filters, ...changedValues },
    }));
  }, []);

  const handleRowClick = useCallback((data: CronLog) => {
    setPageState((prev) => ({ ...prev, isOpen: true, current: data }));
  }, []);

  const reProcess = useCallback(async () => {
    if (!canAccess("cron-log-reprocess")) return;

    setPageState((prev) => ({ ...prev, processing: true }));
    try {
      const computedInput =
        pageState.current?.modified || pageState.current?.result.input;
      await reprocessCronLog({
        id: pageState.current?.id as string,
        type: pageState.current?.type as CronLogType,
        input: computedInput,
        retryCount: pageState?.current?.retryCount as number,
      });
      message.success("Cron log reprocessed successfully");
      await refresh();
    } catch (error) {
      console.error("reProcess", error);
      message.error("Failed to reprocess cron log");
    } finally {
      setPageState((prev) => ({ ...prev, processing: false }));
    }
  }, [canAccess, pageState.current, refresh]);

  const onEdit = useCallback((data: any) => {
    setPageState((prev) => ({
      ...prev,
      current: { ...prev.current, modified: data.newData },
    }));
  }, []);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Cron Logs</Title>
      <Row style={{ lineHeight: "10px" }}>
        <Col span={24}>
          <FilterForm
            form={filterForm}
            onValuesChange={filterCronLogs}
            initialType={pageState.filters.type}
          />
        </Col>
      </Row>
      <Table
        dataSource={cronLogs?.items}
        columns={columns}
        size="small"
        pagination={{
          total: cronLogs?.total,
          pageSize: pageState.filters.limit,
          onChange: (page) =>
            setPageState((prev) => ({
              ...prev,
              filters: { ...prev.filters, page },
            })),
        }}
        loading={isLoading}
        rowClassName="paradox-table-row"
        onRow={(data) => ({
          onClick: () => handleRowClick(data),
        })}
        rowKey="id"
      />
      <Drawer
        title="Cron Log Details"
        placement="right"
        size="large"
        closable={true}
        onClose={() => setPageState((prev) => ({ ...prev, isOpen: false }))}
        open={pageState.isOpen}
      >
        <DrawerContent
          current={pageState.current}
          onEdit={onEdit}
          processing={pageState.processing}
          canReprocess={canAccess("cron-log-reprocess")}
          onReprocess={reProcess}
        />
      </Drawer>
    </div>
  );
}
