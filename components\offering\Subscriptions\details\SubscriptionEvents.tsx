"use client";

import { CalendarOutlined } from "@ant-design/icons";
import { Table, Tag, Typography } from "antd";

type SubscriptionEvent = {
  createdAt: string;
  deletedAt: string | null;
  id: number;
  occurredAt: string;
  outcome: any;
  sender: string;
  senderWebhookId: string;
  status: string;
  updatedAt: string;
};

type SubscriptionEventsProps = {
  subscriptionEvents: SubscriptionEvent[];
};

const columns = [
  {
    title: "Webhook Id",
    dataIndex: "id",
    key: "id",
  },
  {
    title: (
      <>
        <CalendarOutlined />
        &nbsp;Created At
      </>
    ),
    dataIndex: "createdAt",
    key: "createdAt",
  },
  {
    title: "Sender",
    dataIndex: "sender",
    key: "sender",
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (status: string) => {
      return <Tag color={status === "failed" ? "red" : "green"}>{status}</Tag>;
    },
  },
  {
    title: "Outcome",
    dataIndex: "outcome",
    key: "outcome",
    render: (outcome: any) => {
      return (
        <pre style={{ whiteSpace: "break-spaces" }}>
          {JSON.stringify(outcome.message, null, 2)}
        </pre>
      );
    },
  },
];

const { Title } = Typography;
export default function SubscriptionEvents({
  subscriptionEvents,
}: SubscriptionEventsProps) {
  return (
    <Table
      dataSource={subscriptionEvents}
      columns={columns}
      title={() => <Title level={5}>Events (for dev purposes only)</Title>}
      pagination={false}
      size="small"
      rowKey={(record) => record.id}
    />
  );
}
