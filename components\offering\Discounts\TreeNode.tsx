"use client";

import { EuroTwoTone } from "@ant-design/icons";
import { Typography } from "antd";

import { formatter } from "@/lib/currency";

type TreeNodeParams = {
  name: string;
  currency: string;
  amount: number;
  billingCycles: number;
  disabled: boolean;
};

const { Text } = Typography;

const TreeNode = ({
  name,
  currency,
  amount,
  billingCycles,
  disabled,
}: TreeNodeParams) => {
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-evenly",
        width: "100%",
      }}
    >
      <Text strong>
        {name} {disabled ? "(Inactive)" : ""}
      </Text>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      <EuroTwoTone /> &nbsp;&nbsp;&nbsp;
      <Text italic>
        {`${formatter.format(amount)}
          -
          ${billingCycles}
          Month(s)`}
      </Text>
    </div>
  );
};

export default TreeNode;
