"use client";

import { InfoCircleTwoTone } from "@ant-design/icons";
import {
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Typography,
} from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { Discount } from "@/types";

type AddProps = {
  isModalOpen: boolean;
  handleAdd: Function;
  handleCancel: Function;
  data: Discount;
};

const { Title, Text } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

const UpdateDiscountForm = ({
  isModalOpen,
  handleAdd,
  handleCancel,
  data,
}: AddProps) => {
  const [addOnAfter, setAddOnAfter] = useState("");
  const [informationVisible, setInformationVisible] = useState(false);
  const [duration, setDuration] = useState<string>("");

  const [form] = Form.useForm();

  useEffect(() => {
    let date = undefined;
    if (data.validUntil) {
      date = dayjs(data.validUntil);
      data.validUntil = date.format("YYYY-MM-DD");
    }
    form.setFieldsValue(data);
    form.setFieldValue("validUntil", date);
    data.applyOn && handleDiscountAppliedChange(data.applyOn);
    data.type && handleDiscountTypeChange(data.type);
    data.durationType && changeDurationType(data.durationType);
  }, [data]);

  const handleDiscountTypeChange = (value: string) => {
    if (value === "percentage") {
      setAddOnAfter("%");
    } else if (value === "fixed_amount") {
      setAddOnAfter("EUR");
    }
  };

  const handleDiscountAppliedChange = (value: string) => {
    if (value === "each_specified_item") {
      setInformationVisible(true);
    }
    if (value === "invoice_amount") {
      setInformationVisible(false);
    }
  };

  const changeDurationType = (value: string) => {
    setDuration(value);
  };

  const disabledDate: any = (current: any) => {
    // Can not select days before today and today
    return current && current < dayjs().endOf("day");
  };

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      onClose={() => {
        handleCancel();
      }}
      onCancel={() => {
        handleCancel();
      }}
      title="Update Discount"
      submitText="Update"
      onSubmit={() => {
        form.submit();
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={(values) => {
          handleAdd(values);
        }}
      >
        <Title level={5}>Discount settings</Title>
        <br />
        <Form.Item hidden name="id">
          <Input />
        </Form.Item>
        <Form.Item
          label="Discount name "
          name="name"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          A descriptive name for this coupon. Please note that this will be
          displayed to customers, in case &quot;Invoice Name&quot; field is not
          provided.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Discount code"
          name="code"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This will be the reference used by Paradox OS to identify this coupon.
          And this code will be used by our Customers during checkout.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Status"
          name="status"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "active", label: "Active" },
              { value: "disabled", label: "Disabled" },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Setting a discount code to ‘Disabled’ will make it impossible to use
          this discount code on the checkout page.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Invoice name (optional)"
          name="invoiceName"
          style={{ marginBottom: "0px" }}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name displayed to your custotmers on the invoices and hosted payment
          pages.
        </Text>
        <Divider />
        <Title level={5}>Discount price</Title>
        <br />
        <Text>
          <span style={{ color: "red" }}>*</span> Discount type
        </Text>
        <div style={{ marginBottom: "10px" }}></div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            flexDirection: "row",
            width: "100wv",
          }}
        >
          <Form.Item
            name="type"
            style={{ marginBottom: "0px", width: "47%" }}
            rules={rules}
          >
            <Select
              onChange={handleDiscountTypeChange}
              disabled
              options={[
                { value: "percentage", label: "Percentage" },
                {
                  value: "fixed_amount",
                  label: "Fixed amount",
                  disabled: true,
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="amount"
            style={{ marginBottom: "0px", width: "47%" }}
            rules={rules}
          >
            <InputNumber
              disabled
              addonAfter={addOnAfter}
              style={{ width: "100%" }}
            />
          </Form.Item>
        </div>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The specified {addOnAfter} will be deducted.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Apply on"
          name="applyOn"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            onChange={handleDiscountAppliedChange}
            options={[
              {
                value: "each_specified_item",
                label: "Each specified item",
              },
              {
                value: "invoice_amount",
                label: "Invoice amount",
                disabled: true,
              },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Where do you want this discount code to be applied on, at invoice
          amount or per plan.
        </Text>
        <br />
        <br />
        <div
          className="paradox-card-white"
          style={{
            borderRadius: "10px",
            display: informationVisible ? "block" : "none",
          }}
        >
          <Row>
            <Col span={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <InfoCircleTwoTone style={{ fontSize: 34 }} />
              </div>
            </Col>
            <Col span={22} offset={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                You can associate product families, product lines, products,
                plans and offers from the details page after creating the
                discount.
              </div>
            </Col>
          </Row>
        </div>
        <Divider />
        <Title level={5}>Duration & validity</Title>
        <br />
        <Form.Item
          label="Duration type"
          name="durationType"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            placeholder="Forever"
            onChange={changeDurationType}
            disabled
            options={[
              {
                value: "one_time",
                label: <span>One time</span>,
                disabled: true,
              },
              {
                value: "limited_period",
                label: <span>Limited period</span>,
                disabled: true,
              },
              { value: "forever", label: <span>Forever</span> },
            ]}
          />
        </Form.Item>
        <div
          style={{
            display: duration === "limited_period" ? "block" : "none",
          }}
        >
          <div style={{ marginBottom: "10px" }}></div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              flexDirection: "row",
              width: "100wv",
            }}
          >
            <Form.Item
              name="durationPeriodAmount"
              style={{ marginBottom: "0px", width: "47%" }}
            >
              <InputNumber placeholder="1" style={{ width: "100%" }} disabled />
            </Form.Item>
            <Form.Item
              name="durationPeriodUnit"
              style={{ marginBottom: "0px", width: "47%" }}
            >
              <Select
                placeholder="Month"
                disabled
                options={[{ value: "month", label: "Month" }]}
                defaultValue="month"
              />
            </Form.Item>
          </div>
        </div>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          How long should this coupon be applied to the subscription?
        </Text>
        <br />
        <br />
        <Form.Item
          label="Valid Till (optional)"
          name="validUntil"
          style={{ marginBottom: "0px" }}
        >
          <DatePicker
            placeholder="End Date"
            style={{ width: "100%" }}
            disabledDate={disabledDate}
            showTime
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Date (along with Time) upto which the coupon can be applied to new
          subscriptions.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Maximum Redemptions (optional)"
          name="maxRedemptions"
          style={{ marginBottom: "0px" }}
        >
          <InputNumber placeholder="0" style={{ width: "100%" }} />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Maximum number of times this coupon can be redeemed. If not specified,
          the coupon can be redeemed an indefinite number of times
        </Text>
        <br />
      </Form>
    </DrawerFormContainer>
  );
};

export default UpdateDiscountForm;
