"use client";

import { Modal, Select, Typography } from "antd";

import { ProductDropDownSelect } from "./ProductDropdownSelect";
import { FilterOfferProps } from "../forms/ConfigureOfferProductsForm";
import { Product } from "@/types";
import { useMemo } from "react";

const { Text } = Typography;

type AddProductModalProps = {
  products: Product[];
  filters: FilterOfferProps;
  isOpen: boolean;
  setFilters: (p: any) => void;
  setIsOpen: (s: boolean) => void;
};
export const AddProductModal = ({
  filters,
  isOpen,
  products,
  setFilters,
  setIsOpen,
}: AddProductModalProps) => {
  const currentProducts = useMemo(
    () => filters.selectedProducts?.map((item) => item.id),
    [filters.selectedProducts]
  );
  const baseOptions = useMemo(() => {
    if (filters.selectedProducts && filters.selectedProducts?.length > 0) {
      return [
        {
          label: <span>Selected Products</span>,
          options: filters.selectedProducts.map((item) => ({
            label: item.externalName,
            value: item.id,
          })),
        },
      ];
    } else {
      return [];
    }
  }, [filters.selectedProducts]);

  const allProductsOpts = useMemo(() => {
    if (!products) {
      return {};
    } else {
      return {
        label: <span>All Products</span>,
        options: products
          .filter((x: Product) => !filters.selectedProducts?.includes(x))
          .map((item: Product) => {
            return {
              label: item.externalName,
              value: item.id,
              one: filters.filteredProducts,
              disabled:
                !filters.filteredProducts.includes(item) ||
                item === filters?.selectedRecommended,
            };
          }),
      };
    }
  }, [
    products.length,
    filters.filteredProducts.length,
    filters.selectedProducts,
    filters.selectedRecommended,
  ]);

  const handleProductSelection = (values: number[]) => {
    if (products) {
      let productsSelected: Product[] = [];
      if (values.length > filters.selectedProducts.length) {
        // check filters.selectedProducts to see what value from values is not present
        // and then push that product in the productsSelecrted array
        const selectedProductIds =
          filters.selectedProducts?.map((product) => product.id) || [];
        const newProductIds = values.filter(
          (value) => !selectedProductIds.includes(value)
        );
        const newProducts = products.filter((product) =>
          newProductIds.includes(product.id)
        );
        productsSelected = [
          ...(filters.selectedProducts || []),
          ...newProducts,
        ];

        setFilters((prev: FilterOfferProps) => ({
          ...prev,
          selectedProducts: productsSelected,
        }));
      } else {
        // check filters.selectedProducts to see which product was removed and
        // then remove it from the state
        const selectedProductIds =
          filters.selectedProducts?.map((product) => product.id) || [];
        const removedProductIds = selectedProductIds.filter(
          (value) => !values.includes(value)
        );
        productsSelected = filters.selectedProducts.filter(
          (product) => !removedProductIds.includes(product.id)
        );
        let selectedPlans = filters.selectedProductPlans;
        removedProductIds.forEach((id) => {
          selectedPlans.delete(id);
        });
        setFilters((prev: FilterOfferProps) => ({
          ...prev,
          selectedProducts: productsSelected,
          selectedRecommendedPlans: selectedPlans,
        }));
      }
      let tempMap = new Map<number, Map<string, string>>();
      productsSelected.forEach((item: Product) => {
        let planMap = new Map<string, string>();
        filters.selectedBC.forEach((element) => {
          let currentPlan = item.plans.find(
            (x) => element === x.prices[0].totalBillingCycles.toString()
          );
          currentPlan?.id && planMap.set(element, currentPlan.id.toString());
        });
        tempMap.set(item.id, new Map<string, string>(planMap));
      });
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        selectedProductPlans: tempMap,
      }));
    }
  };

  return (
    <Modal
      onCancel={() => setIsOpen(false)}
      open={isOpen}
      width="50%"
      closable={false}
      footer={[]}
    >
      <br />
      {!filters.filteredProducts && (
        <Text>Please select a billing cycle first</Text>
      )}
      {filters.filteredProducts && products && (
        <Select
          style={{ width: "100%" }}
          showSearch
          allowClear
          placeholder="Search product ..."
          mode="multiple"
          autoFocus={true}
          onChange={handleProductSelection}
          value={currentProducts}
          options={[
            ...(filters.selectedProducts && filters.selectedProducts?.length > 0
              ? [
                  {
                    label: <span>Selected Products</span>,
                    options: filters.selectedProducts.map((item) => ({
                      label: item.externalName,
                      value: item.id,
                    })),
                  },
                ]
              : []),
            allProductsOpts,
          ]}
          optionRender={(option) => (
            <ProductDropDownSelect
              selectedBillingCycles={filters.selectedBC}
              product={products.find((x: Product) => x.id === option.key)}
              showDisabledTooltip={(option.data as any).disabled}
              alreadySelected={
                filters.selectedRecommended &&
                filters.selectedRecommended ===
                  products.find((x: Product) => x.id === option.key)
              }
            />
          )}
        />
      )}
    </Modal>
  );
};
