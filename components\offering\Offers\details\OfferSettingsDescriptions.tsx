"use client";

import { Descriptions, Typography } from "antd";
import { getPaymentGatewayName } from "@/lib/paymentGateways";
import { Offer } from "@/types";

const { Text } = Typography;

type OfferSettingsDescriptionsProps = {
  offer: Offer;
};

export default function OfferSettingsDescriptions(
  params: OfferSettingsDescriptionsProps
) {
  return (
    <Descriptions title="Offer settings" bordered column={2} size="small">
      <Descriptions.Item
        label={
          <>
            <span>Offer name</span>
            <br />
            <small style={{ color: "darkgray" }}>
              A descriptive name for this offer.
            </small>
          </>
        }
      >
        {params.offer.name}
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Offer external name</span>
            <br />
            <small style={{ color: "darkgray" }}>
              This name is visible to the customer, on the portal and checkout
              page.
            </small>
          </>
        }
      >
        {params.offer.externalName}
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Currency</span>
            <br />
            <small style={{ color: "darkgray" }}>
              For which currency is this offer created?
            </small>
          </>
        }
      >
        {params.offer.currency}
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Billing Entity</span>
            <br />
            <small style={{ color: "darkgray" }}>
              By selecting an entity, all offers will be attached to this
            </small>
          </>
        }
      >
        {params.offer.fiscalEntity}
      </Descriptions.Item>

      <Descriptions.Item
        label={
          <>
            <span>Description</span>
            <br />
            <small style={{ color: "darkgray" }}>
              The offer description diplayed on the customer portal.
            </small>
          </>
        }
      >
        {params.offer.description}
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Payment Gateway</span>
            <br />
            <small style={{ color: "darkgray" }}>
              By selecting a payment gateway, all payments will go through it.
            </small>
          </>
        }
      >
        {getPaymentGatewayName(params.offer?.paymentGateway)}
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Status</span>
            <br />
            <small style={{ color: "darkgray" }}>
              Setting a offer to Disabled will disable all the related checkout
              pages.
            </small>
          </>
        }
      >
        {params.offer.status
          ?.charAt(0)
          .toUpperCase()
          .concat(params.offer.status.slice(1))}
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Offer slug</span>
            <br />
            <small style={{ color: "darkgray" }}>
              This will be used to create the unique url for this offer.
            </small>
          </>
        }
      >
        <Text copyable>{params.offer.slug}</Text>
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Redirect URL</span>
            <br />
            <small style={{ color: "darkgray" }}>
              The customer will be redirected to this URL after the purchase.
            </small>
          </>
        }
      >
        <Text copyable>{params.offer.redirectUrl}</Text>
      </Descriptions.Item>
      <Descriptions.Item
        label={
          <>
            <span>Periodicity</span>
            <br />
            <small style={{ color: "darkgray" }}>
              Wether this is a forever offer or a one time purchase
            </small>
          </>
        }
      >
        {params.offer.isForever ? "Forever" : "One time purchase"}
      </Descriptions.Item>
      {params.offer.isForever ? (
        <>
          <Descriptions.Item
            label={
              <>
                <span>Trial period monthly</span>
                <br />
                <small style={{ color: "darkgray" }}>
                  The number of trial days for monthly subscription
                </small>
              </>
            }
          >
            <small style={{ color: "darkgray" }}>
              {params.offer.trialDaysMonthly}
            </small>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <span>Trial period yearly</span>
                <br />
                <small style={{ color: "darkgray" }}>
                  The number of trial days for yearly subscription
                </small>
              </>
            }
          >
            <small style={{ color: "darkgray" }}>
              {params.offer.trialDaysYearly}
            </small>
          </Descriptions.Item>
        </>
      ) : null}
      <Descriptions.Item
        label={
          <>
            <span>USP</span>
            <br />
            <small style={{ color: "darkgray" }}>
              The unique selling point of this offer.
            </small>
          </>
        }
      >
        {params.offer.usp}
      </Descriptions.Item>
    </Descriptions>
  );
}
