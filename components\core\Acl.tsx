"use client";

import { Card, List, Tag, Typography, Input } from "antd";
import { useState } from "react";

import { acl } from "@/lib/acl";
import { getRoleColor, roles } from "@/lib/roles";
import { RoleType } from "@/types";

const rolesList: RoleType[] = roles.map((role) => role.value as RoleType);

export default function ACL() {
  const [searchQuery, setSearchQuery] = useState("");

  // Reorganize data to be permission-centric instead of role-centric
  const permissionsList = acl
    .filter((permission) =>
      permission.key.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .map((permission) => ({
      key: permission.key,
      errorMsg: permission.errorMsg,
      forbiddenFor: permission.forbiddenFor,
      authorizedRoles: permission.authorizedRoles || [],
    }));

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <Typography.Title level={2}>Access Control List</Typography.Title>

      <Input.Search
        placeholder="Search permissions..."
        className="mb-4"
        onChange={(e) => setSearchQuery(e.target.value)}
        allowClear
      />

      <List
        dataSource={permissionsList}
        renderItem={(permission) => (
          <List.Item>
            <Card size="small" style={{ width: "100%" }}>
              <div className="space-y-2">
                <Typography.Text strong style={{ cursor: "pointer" }}>
                  {permission.key}
                </Typography.Text>
                <div className="flex flex-wrap gap-2 mt-2">
                  {rolesList.map((role) => (
                    <Tag
                      key={role}
                      color={
                        permission.authorizedRoles.includes(role)
                          ? getRoleColor(role)
                          : "default"
                      }
                      className="capitalize"
                      style={{
                        display: permission.authorizedRoles.includes(role)
                          ? "inline"
                          : "none",
                      }}
                    >
                      {role}
                    </Tag>
                  ))}
                </div>
                {permission.forbiddenFor && (
                  <Typography.Text
                    style={{
                      color: "gray",
                      fontSize: "10px",
                      display: "block",
                    }}
                  >
                    Forbidden for: {permission.forbiddenFor}
                  </Typography.Text>
                )}
              </div>
            </Card>
          </List.Item>
        )}
      />
    </div>
  );
}
