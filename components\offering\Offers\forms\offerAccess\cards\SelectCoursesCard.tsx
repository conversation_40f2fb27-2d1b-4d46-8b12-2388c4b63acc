"use client";

import { Button, Space, Typography } from "antd";
import { EditOutlined } from "@ant-design/icons";

import { CourseInfo } from "@/types";

const { Text } = Typography;

type SelectCoursesCardProps = {
  selectedCourses: CourseInfo[];
  onOpenModal: () => void;
};

export default function SelectCoursesCard({
  selectedCourses,
  onOpenModal,
}: SelectCoursesCardProps) {
  return (
    <div className="paradox-dotted-card" style={{ height: "100%" }}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-evenly",
          flexDirection: "row",
          width: "100%",
        }}
      >
        <Text style={{ paddingTop: "15px", fontWeight: "bold" }}>
          Courses (LW)
        </Text>
        <br />
        <Button
          onClick={() => onOpenModal()}
          style={{ marginTop: "15px" }}
          icon={<EditOutlined />}
        >
          Edit
        </Button>
      </div>
      {!selectedCourses || selectedCourses.length === 0 ? (
        <div className="paradox-display-card">
          <span style={{ width: "100%" }}>
            <Text>No course selected</Text>
          </span>
        </div>
      ) : (
        <div style={{
          display: "flex",
          flexDirection: "row",
          flexWrap: "wrap",
          gap: "5px",
          marginTop: "8px"
        }}>
          {selectedCourses.map((course: CourseInfo) => {
            return (
              <div
                key={course.id}
                style={{
                  display: "inline-block",
                  backgroundColor: "#f5f5f5",
                  borderRadius: "4px",
                  padding: "4px 12px",
                  margin: "0",
                  border: "1px solid #d9d9d9",
                }}
              >
                <Text>{course?.name}</Text>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
