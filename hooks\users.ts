"use client";

import useSWR, { SWRConfiguration } from "swr";

// import { getAllUsers } from "@/server/clerk";
import { UsersFilters } from "@/types";
import { getAllUsers } from "@/lib/services/clerk/users";

type UseFetchUsersParams = {
  filters?: UsersFilters;
  options?: SWRConfiguration;
};

/**
 * Custom hook for fetching users.
 *
 * @param params - The parameters for fetching users.
 * @returns The result of the query.
 */
export const useFetchUsers = (params: UseFetchUsersParams) => {
  return useSWR(
    `useFetchUsers-${JSON.stringify(params?.filters)}`,
    () => getAllUsers(params?.filters),
    params?.options
  );
};
