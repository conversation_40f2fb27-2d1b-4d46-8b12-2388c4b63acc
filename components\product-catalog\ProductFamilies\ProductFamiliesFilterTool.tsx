"use client";

import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Col, Input, Row, Select } from "antd";
import { ProductFamilyFilter } from "@/types";

type ProductFamiliesFilterParams = {
  filters: ProductFamilyFilter;
  setFilters: Function;
  onCreateProduct: Function;
};

export default function ProductFamiliesFilterTool({
  filters,
  setFilters,
  onCreateProduct,
}: ProductFamiliesFilterParams) {
  return (
    <>
      <Row
        justify="space-between"
        gutter={[0, 0]}
        style={{ lineHeight: "10px" }}
      >
        <Col xs={12} sm={12} md={16} lg={16} xl={17} xxl={19}>
          <Input
            prefix={<SearchOutlined />}
            placeholder="Search for name"
            onChange={(event) =>
              setFilters({ ...filters, search: event.target.value })
            }
            value={filters.search}
          />
        </Col>
        <Col offset={1} xs={11} sm={11} md={7} lg={7} xl={6} xxl={4}>
          <Button
            type="primary"
            style={{ width: "100%" }}
            icon={<PlusOutlined />}
            onClick={() => onCreateProduct()}
            data-testid="create-product-family-btn"
          >
            Create Product Family
          </Button>
        </Col>
      </Row>
      <div
        style={{
          display: "flex",
          marginBottom: "30px",
          marginTop: "10px",
        }}
      >
        <Select
          value={filters.status}
          allowClear
          style={{ width: "200px" }}
          onChange={(values) => setFilters({ ...filters, status: values })}
          placeholder="Status"
          options={[
            { value: "active", label: <span>Active</span> },
            { value: "draft", label: <span>Draft</span> },
          ]}
        />
        <Button
          type="link"
          onClick={() => {
            setFilters((prev: ProductFamilyFilter) => ({
              ...prev,
              search: "",
              status: "",
            }));
          }}
        >
          Clear Filters
        </Button>
      </div>
    </>
  );
}
