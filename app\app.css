.sider-submenu-items {
  background-color: #f7f8f9;
}

.paradox-card {
  /* background-color: #f7f8f9; */
  background-color: white;
  padding: 20px;
  box-shadow:
    rgba(0, 0, 0, 0.12) 0px 1px 3px,
    rgba(0, 0, 0, 0.24) 0px 1px 2px;
  border-radius: 5px;
  /* padding-left: "30px";
  padding-right: "30px"; */
}

.paradox-card-mini {
  /* background-color: #f7f8f9; */
  background-color: whitesmoke;
  padding: 5px;
  box-shadow:
    rgba(0, 0, 0, 0.12) 0px 1px 3px,
    rgba(0, 0, 0, 0.24) 0px 1px 2px;
  border-radius: 5px;
  /* padding-left: "30px";
  padding-right: "30px"; */
}

.paradox-card-white {
  background-color: #fff;
  padding: 20px;
  box-shadow:
    rgba(0, 0, 0, 0.12) 0px 1px 3px,
    rgba(0, 0, 0, 0.24) 0px 1px 2px;
  /* padding-left: "30px";
  padding-right: "30px"; */
}

.paradox-table-row {
  cursor: pointer;
}

.paradox-richtext-editor-input {
  background-color: white;
  padding-left: 10px;
  border: 1px solid lightgray;
  border-radius: 0px 10px 10px 0px;
  height: 38px;
}

.paradox-disabled-input {
  background-color: white !important;
  color: black !important;
}

.paradox-dotted-card {
  border: 1px dashed lightgray;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  padding-left: 20px;
  padding-right: 20px;
}

.paradox-disabled-div {
  pointer-events: none;
  opacity: 0.4;
}

.paradox-rounded-box {
  border: 1px solid #f0f0f0;
  border-radius: 5px;
  padding-left: 10px;
  padding-right: 10px;
}

.paradox-plan-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  border: 1px solid lightgray;
  padding: 5px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.paradox-plan-image-container {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.paradox-subtext {
  font-size: 13;
  color: "darkgray";
}

.paradox-planitem-arrow {
  width: 10%;
  padding: 10px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.paradox-tag-green {
  padding: 10px;
  background-color: #f6ffec;
  border: 1px solid #b8ea8f;
  color: #389e0e;
  border-radius: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
}

.paradox-tag-red {
  padding: 10px;
  background-color: #fff0f0;
  border: 1px solid #ffa39e;
  color: #cf1421;
  border-radius: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
}

.paradox-tag-orange {
  padding: 10px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  color: #ad6800;
  border-radius: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
}

.ant-tree-treenode {
  width: "100%" !important;
}

.ant-tree-node-content-wrapper {
  width: "100%" !important;
}

.ant-tree-title {
  width: "100%" !important;
  display: flex !important;
}

.paradox-checkbox-group .ant-checkbox-group {
  display: flex;
  flex-direction: column;
}

.mdxeditor-popup-container {
  z-index: 10000 !important;
}

.mdxeditor-root-contenteditable {
  background-color: white !important;
  height: 70px !important;
  border: 1px solid lightgray !important;
  border-radius: 10px !important;
}

.ProseMirror :focus-visible {
  outline-color: red !important;
}

.ProseMirror-focused {
  outline: none !important;
}

.paradox-warranty-select .ant-select-selector {
  border-radius: 10px 0px 0px 10px !important;
}

.centered {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.paradox-display-card {
  width: 100%;
  background-color: #fafafa;
  margin-top: 30px;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid lightgray;
}
