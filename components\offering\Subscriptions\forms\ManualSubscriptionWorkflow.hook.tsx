"use client";

import { useState } from "react";
import { Coupon, Discount, Item } from "chargebee";

import WorkflowStep1 from "./WorkflowStep1";
import WorkflowStep2 from "./WorkflowStep2";
import WorkflowStep3 from "./WorkflowStep3";
import { getItem, searchItemPrices } from "@/lib/services/chargebee";
import { Offer, WorkflowState } from "@/types";

const initialState: WorkflowState = {
  bc: 0,
  billingAddress: {
    first_name: "",
    last_name: "",
    email: "",
    line1: "",
    line2: "",
    city: "",
    zip: "",
    country: "",
  },
  billingCycles: [],
  coupons: [],
  current: 0,
  customerData: {
    email: "",
    first_name: "",
    last_name: "",
    country: "",
    line1: "",
    line2: "",
    city: "",
    zip: "",
  },
  dealID: null,
  discounts: [],
  isNewCustomer: false,
  offer: null,
  products: [],
  startDate: "",
  shippingAddress: {
    first_name: "",
    last_name: "",
    email: "",
    company: "",
    phone: "",
    line1: "",
    line2: "",
    city: "",
    country: "",
  },
};

export type WorkflowForSubscriptionCreationParams = {
  handleCancel: () => void;
  handleSubmit: () => void;
};

const productsToItems = async (
  payload: any,
  type = "mandatory",
  offer: any,
  fetchFn: any
) => {
  if (!payload || !offer) return [];
  const config = offer.config;

  const tmp = Object.values(payload);
  let promiseArray: Promise<Item>[] = [];
  tmp.forEach((p: any) => {
    p.map((r: any) => {
      const kv = Object.entries(r)[0];
      const id = kv[1] as string;
      promiseArray.push(fetchFn(id));
    });
  });

  const items = await Promise.all([...promiseArray]);
  const container = type === "mandatory" ? config.products : config.recommended;
  const intIds = Object.keys(container);

  return items.map((r) => {
    let productId: any = null;
    let image = null;
    intIds.map((intId: any) => {
      const e = container[intId];
      return e.map((f: any) => {
        return Object.entries(f).forEach((kv) => {
          const [bc, id] = kv;
          if (id === r.id) {
            productId = Number(intId);
            image = offer.products.find((p: any) => p.id === productId)?.image;
          }
        });
      });
    });

    const canChangeQuantityCtn =
      offer.config.quantityInfo[
        type === "mandatory" ? "products" : "recommended"
      ];
    return {
      ...r,
      productType: type,
      productId,
      image,
      attachedId: config.attachmentMap[r.id],
      quantity: 1,
      canChangeQuantity: canChangeQuantityCtn[productId],
    };
  });
};

const prepareProducts = async (offer: Offer, fetchFn: any, searchFn: any) => {
  if (!offer.config) return;
  const [_products, _recommended] = await Promise.all([
    productsToItems(offer.config.products, "mandatory", offer, fetchFn),
    productsToItems(offer.config.recommended, "recommended", offer, fetchFn),
  ]);

  // map to get the ids
  const resultsIds = _products.map((r: any) => r.id);
  const recommendedIds = _recommended.map((r: any) => r.id);
  const allIds = [...resultsIds, ...recommendedIds];
  // Retrieve the item prices for each id
  const itemPrices = await searchFn({ itemIds: allIds });
  // merge the products with the item prices
  const mergedProducts = _products.map((r: any) => {
    const itemPrice = itemPrices.find((it: any) => {
      return it.item_id === r.id;
    });
    return {
      ...r,
      itemPrice: itemPrice || null,
      isMandatory: true,
      isChecked: true,
    };
  });
  // merge the recommended with the item prices
  const mergedRecommended = _recommended.map((r: any) => {
    const itemPrice = itemPrices.find((t: any) => {
      return t.item_id === r.id;
    });
    return {
      ...r,
      itemPrice: itemPrice || null,
      isMandatory: false,
      isChecked: false,
    };
  });
  return [...mergedProducts, ...mergedRecommended];
};

export default function useManualSubscriptionWorkflow({
  handleCancel,
  handleSubmit,
}: WorkflowForSubscriptionCreationParams) {
  const [data, setData] = useState<WorkflowState>(initialState);

  const handleBillingCycleChanged = async (value: number) => {
    setData((prev) => ({ ...prev, bc: value }));
    const preparedProducts = await prepareProducts(
      data.offer as Offer,
      getItem,
      searchItemPrices
    );
    if (!preparedProducts) return;
    const filteredProducts = preparedProducts.filter(
      (p: any) => p.itemPrice?.cf_billing_cycles === value
    );
    setData((prev) => ({ ...prev, products: filteredProducts }));
  };

  const handleOfferChanged = async (offerObject: any) => {
    setData((prev) => ({ ...prev, offer: offerObject }));
    const { config } = offerObject;
    if (config && config.defaultBillingCycle) {
      // set the selected billing cycle
      const newBc = Number(config.defaultBillingCycle);
      setData((prev) => ({ ...prev, bc: newBc }));
      // set the available billing cycles
      const products = Object.values(config.products)[0] as any;
      const bcProducts = Object.values(products).map((p: any) =>
        Number(Object.entries(p)[0][0])
      );
      setData((prev) => ({ ...prev, billingCycles: [...bcProducts] }));

      // prepare the products
      const preparedProducts = await prepareProducts(
        offerObject,
        getItem,
        searchItemPrices
      );
      if (preparedProducts) {
        const filteredProducts = preparedProducts.filter(
          (p: any) => p.itemPrice?.cf_billing_cycles === newBc
        );
        setData((prev) => ({ ...prev, products: filteredProducts }));
      }
    }
  };

  const handleCustomerChange = (customerData: any) => {
    console.log("ℹ️ Workflow:handleCustomerChange", customerData);
    setData((prev) => ({ ...prev, customerData }));
  };

  const handleStartDateChanged = (date: any) => {
    setData((prev) => ({ ...prev, startDate: date }));
  };

  const handleProductSelection = (product: any, event: any) => {
    const newProducts = data.products.map((p: any) => {
      if (p.id === product.id) {
        return {
          ...p,
          isChecked: event.target.checked,
        };
      }
      return p;
    });
    setData((prev) => ({ ...prev, products: newProducts }));
  };

  const handleProductQtyChange = (product: any, value: number) => {
    const newP = data.products.map((p: any) => {
      if (p.id === product.id) {
        return {
          ...p,
          quantity: value,
        };
      }
      return p;
    });
    setData((prev) => ({ ...prev, products: newP }));
  };

  const next = () => {
    const StepsLength = steps.length;
    if (data.current === StepsLength - 1) {
      return handleSubmit();
    } else {
      setData((prev) => ({ ...prev, current: prev.current + 1 }));
    }
  };

  const prev = () => {
    if (data.current === 0) {
      handleCancel();
      return;
    }
    setData((prev) => ({ ...prev, current: prev.current - 1 }));
  };

  const resetCurrent = () => {
    setData((prev) => ({ ...prev, current: 0 }));
  };

  const setError = (message: string | null) => {
    setData((prev) => ({ ...prev, error: message }));
  };

  const steps = [
    {
      title: "Configure subscription",
      status: "wait",
      content: (
        <WorkflowStep1
          offer={data.offer as Offer}
          billingCycle={data.bc}
          billingCycles={data.billingCycles}
          coupons={[...data.coupons]}
          setCoupons={(value: Coupon[]) =>
            setData((prev) => ({ ...prev, coupons: value }))
          }
          discounts={data.discounts}
          setDiscounts={(value: Discount[]) =>
            setData((prev) => ({ ...prev, discounts: value }))
          }
          products={data.products}
          handleOfferChange={handleOfferChanged}
          handleBillingCycleChange={handleBillingCycleChanged}
          handleProductSelection={handleProductSelection}
          handleProductQtyChange={handleProductQtyChange}
          handleStartDateChanged={handleStartDateChanged}
        />
      ),
    },
    {
      title: "Customer information",
      status: "process",
      content: (
        <WorkflowStep2
          dealID={data.dealID}
          customerData={data.customerData}
          setDealID={(value: string) =>
            setData((prev) => ({ ...prev, dealID: value }))
          }
          handleCustomerChange={handleCustomerChange}
          isNewCustomer={data.isNewCustomer}
          setIsNewCustomer={(value: boolean) =>
            setData((prev) => ({ ...prev, isNewCustomer: value }))
          }
        />
      ),
    },
    {
      title: "Billing information",
      status: "wait",
      content: (
        <WorkflowStep3
          customerData={data.customerData}
          error={data?.error || null}
          handleShippingInfoChange={(_: any, values: any) => {
            setData((prev) => ({
              ...prev,
              shippingAddress: values,
            }));
          }}
          handleBillingInfoChange={(_: any, values: any) => {
            setData((prev) => ({
              ...prev,
              billingAddress: values,
            }));
          }}
        />
      ),
    },
  ];

  return {
    ...data,
    steps,
    handleProductQtyChange,
    next,
    prev,
    resetCurrent,
    setError,
  };
}
