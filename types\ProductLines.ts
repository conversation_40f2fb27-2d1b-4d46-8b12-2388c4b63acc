import { ProductFamily } from "./ProductFamilies";

export type ProductLine = {
  id?: number;
  name: string;
  description: string;
  status: string;
  chargebeeId?: string;
  productLine?: any;
  families?: ProductFamily[];
};

export type ProductLineFilters = {
  search?: string;
  status?: string | null;
};

export type ProductLineListResponse = {
  items: ProductLine[];
  total: number;
  currentPage: number;
};

export type ProductLineParams = {
  id?: number;
  name: string;
  description: string;
  status: string;
};
