"use client";

import useSWR, { SWRConfiguration } from "swr";

import { getDiscount, searchDiscounts } from "@/lib/services";
import {
  Discount,
  DiscountListResponse,
  DiscountsFilter,
  SearchDiscountsParams,
} from "@/types";

type UseFetchDiscountsParams = {
  filters?: DiscountsFilter;
  options?: SWRConfiguration;
};

function prepareFetchDiscounts(filters?: DiscountsFilter) {
  let options: SearchDiscountsParams = { name: "", status: "active" };
  if (filters) {
    if (filters.name) options.name = filters.name as string;
    if (filters.productLine) options.line = filters.productLine as string;
    if (filters.productFamily) options.family = filters.productFamily as string;
    if (filters.productPlan) options.plan = filters.productPlan as string;
    if (filters.product) options.product = filters.product as string;
    if (filters.status) options.status = filters.status as string;
  }
  return options;
}

export const useSearchDiscounts = (params?: UseFetchDiscountsParams) => {
  const options: SearchDiscountsParams = prepareFetchDiscounts(params?.filters);
  return useSWR<DiscountListResponse>("useSearchDiscounts", () =>
    searchDiscounts(options)
  );
};

export const useFetchDiscount = (id: string) => {
  return useSWR<Discount>(`useFetchDiscount-${id}`, () => getDiscount(id));
};
