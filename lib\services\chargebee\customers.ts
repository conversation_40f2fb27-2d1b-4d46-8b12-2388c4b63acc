import { GetCustomersParams } from "@/types";
import { requestChargebee } from ".";

export async function fetchCustomer(id: string) {
  if (!id) return;
  return requestChargebee(`/api/customer/${id}`, "GET");
}

/**
 * Get list of all customers
 *
 * @returns A Promise that resolves to the response data from Chargebee.
 */
export async function fetchCustomers(params: GetCustomersParams = {}) {
  return requestChargebee(`/api/customers`, "POST", params);
}

/**
 * Get list of all customers (search)
 *
 * @returns A Promise that resolves to the response data from Chargebee.
 */
export async function searchCustomers(params: GetCustomersParams = {}) {
  return requestChargebee(`/api/customers/search`, "POST", params);
}

export type UpdateBillingInfoParams = {
  first_name: string;
  last_name: string;
  line1: string;
  city: string;
  state: string;
  country: string;
};

/**
 * Updates the billing information for a customer.
 * @param id - The ID of the customer.
 * @param params - The parameters for the update.
 * @returns A Promise that resolves to the updated billing information.
 */
export async function updateBillingInfo(
  id: string,
  params: UpdateBillingInfoParams
) {
  return requestChargebee(`/api/customers/${id}/update-billing-info`, "POST", {
    ...params,
    id,
  });
}
