"use server";

import {
  CreditNote,
  CreditNoteListResponse,
  GetCreditNotesParams,
} from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/credit-notes`;

/**
 * Retrieves credit notes based on the specified parameters.
 *
 * @param params - The optional parameters for filtering the credit notes.
 * @returns A Promise that resolves to the JSON response containing the credit notes.
 */
export const getCreditNotes = async (
  params?: GetCreditNotesParams
): Promise<CreditNoteListResponse> => {
  let requestQuery = new URL(`${baseUrl}?`);
  const query = new URLSearchParams();
  if (params?.searchQuery) query.append("query", params.searchQuery);
  if (params?.subscriptionId)
    query.append("subscriptionId", params.subscriptionId);
  if (params?.status) query.append("status", params.status);
  if (params?.entity) query.append("fiscalEntity", params.entity);
  if (params?.customerId) query.append("customerId", params.customerId);
  query.append("limit", "1000");
  query.append("page", "1");
  query.append("orderBy", "DESC");
  requestQuery.search = query.toString();
  const res = await osRequest(requestQuery.toString(), "GET");

  return res;
};

/**
 * Retrieves a credit note by its ID.
 * @param id - The ID of the credit note to retrieve.
 * @returns A Promise that resolves to the credit note object.
 */
export const getCreditNote = async (id: string): Promise<CreditNote> => {
  const res = await osRequest(`${baseUrl}/${id}`, "GET");
  return res;
};
