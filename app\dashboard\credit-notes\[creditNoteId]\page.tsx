export const dynamic = "force-dynamic";

import { Metadata } from "next";

import CreditNoteDetails from "@/components/accounting/CreditNotes/CreditNoteDetails";

export const metadata: Metadata = {
  title: "Credit note details",
};

type CreditNoteDetailsProps = {
  creditNoteId: string;
};

export default async function OfferDetailsPage(
  props: {
    params: Promise<CreditNoteDetailsProps>;
  }
) {
  const params = await props.params;
  return <CreditNoteDetails creditNoteId={params.creditNoteId} />;
}
