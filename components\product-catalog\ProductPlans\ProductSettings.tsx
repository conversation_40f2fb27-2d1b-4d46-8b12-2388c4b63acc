"use client";

import { Row, Col, Typography, Form, Input } from "antd";

const { Text } = Typography;

const ProductSettings = () => {
  return (
    <div>
      <Row>
        <Col span={11} style={{ margin: "10px" }}>
          <div className="paradox-card">
            <Text strong>Product settings</Text>
            <br />
            <br />
            <Form layout="vertical" style={{ maxWidth: 800 }}>
              <Form.Item label="Product name">
                <Input value="EDEC Certficiate" />
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  Name that helps you identify this product internally.
                </Text>
              </Form.Item>
              <Form.Item label="Product code">
                <Input value="PXS-EDEC-EDEC-CERTIFICATE" disabled />
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  This is the unique product code identifier for this product.
                </Text>
              </Form.Item>
              <Form.Item label="Status">
                <Input value="Active" />
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  Switching a product to Draft will disable all the plans &
                  offers.
                </Text>
              </Form.Item>
            </Form>
          </div>
          <div className="paradox-card" style={{ marginTop: "20px" }}>
            <Text strong>Fiscality</Text>
            <br />
            <br />
            <Form layout="vertical" style={{ maxWidth: 800 }}>
              <Form.Item label="Fiscal entity">
                <Input value="Paradox Institute" />
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  By selecting an entity, all offers will be attached to this
                  entity.
                </Text>
              </Form.Item>
            </Form>
          </div>
        </Col>
        <Col span={11} style={{ margin: "10px" }}>
          <div className="paradox-card">
            <Text strong>Other settings</Text>
            <br />
            <br />
            <Form layout="vertical" style={{ maxWidth: 800 }}>
              <Form.Item label="Image">
                <Input value="only local file" />
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  This image will be display on the checkout, user portal &
                  invoices.
                </Text>
              </Form.Item>
            </Form>
          </div>
          <div className="paradox-card" style={{ marginTop: "20px" }}>
            <Text strong>Product classification</Text>
            <br />
            <br />
            <Form layout="vertical" style={{ maxWidth: 800 }}>
              <Form.Item label="Product line">
                <Input value="PXS" disabled />
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The product line attached to this product.
                </Text>
              </Form.Item>
              <Form.Item label="Product family">
                <Input value="EDEC" disabled />
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The product family attached to this product.
                </Text>
              </Form.Item>
            </Form>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default ProductSettings;
