export const dynamic = "force-dynamic";

import { Metadata } from "next";

import TransactionDetails from "@/components/accounting/Transactions/TransactionDetails";

export const metadata: Metadata = {
  title: "Transaction details",
};

type TxnDetailsProps = {
  transactionId: string;
};

export default async function OfferDetailsPage(
  props: {
    params: Promise<TxnDetailsProps>;
  }
) {
  const params = await props.params;
  return <TransactionDetails transactionId={params.transactionId} />;
}
