"use client";

import React, { useMemo } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button, Flex, Image, Menu, MenuProps, Tag } from "antd";
import {
  BankOutlined,
  CodeOutlined,
  HomeOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ProductOutlined,
  ShoppingCartOutlined,
  UserOutlined,
} from "@ant-design/icons";

import UserButton from "@/components/core/UserButton";
import { currentConfig } from "@/core/envConfig";
import { useUserRole } from "@/hooks/roles";

type SidebarProps = {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
};

const menuElClass = (pathname: string, key: string) => {
  return pathname === key
    ? "bg-graydark dark:bg-meta-4"
    : "sider-submenu-items";
};

const styles = {
  container: {
    overflowY: "auto" as const,
  },
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    margin: "0 10px",
  },
  logo: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    margin: "10px 0",
  },
  logoImage: {
    cursor: "pointer",
    minWidth: 20,
  },
  menu: {
    height: "auto",
    backgroundColor: "#F7F8F9",
    textAlign: "left" as const,
  },
};

const Sidebar = ({ collapsed, setCollapsed }: SidebarProps) => {
  const pathname = usePathname();
  const { canAccess } = useUserRole();

  const userItems = useMemo<MenuProps["items"]>(
    () => [
      {
        key: "3",
        label: <Link href="/dashboard/customers">Customers</Link>,
        className: menuElClass(pathname, "/dashboard/customers"),
      },
      canAccess("employees-list", false)
        ? {
            key: "4",
            label: <Link href="/dashboard/users">Employees</Link>,
            className: menuElClass(pathname, "/dashboard/users"),
          }
        : null,
    ],
    [pathname, canAccess]
  );

  const productCatalogItems = useMemo<MenuProps["items"]>(
    () => [
      {
        key: "6",
        label: <Link href="/dashboard/product_lines">Product Lines</Link>,
        className: menuElClass(pathname, "/dashboard/product_lines"),
      },
      {
        key: "7",
        label: <Link href="/dashboard/product_families">Product Families</Link>,
        className: menuElClass(pathname, "/dashboard/product_families"),
      },
      {
        key: "8",
        label: <Link href="/dashboard/products">Products</Link>,
        className: menuElClass(pathname, "/dashboard/products"),
      },
    ],
    [pathname]
  );

  const offeringItems = useMemo<MenuProps["items"]>(
    () => [
      {
        key: "10",
        label: <Link href="/dashboard/discounts">Discounts</Link>,
        className: menuElClass(pathname, "/dashboard/discounts"),
      },
      {
        key: "11",
        label: <Link href="/dashboard/offers">Offers</Link>,
        className: menuElClass(pathname, "/dashboard/offers"),
      },
      {
        key: "12",
        label: <Link href="/dashboard/sales">Sales</Link>,
        className: menuElClass(pathname, "/dashboard/sales"),
      },
      {
        key: "20",
        label: <Link href="/dashboard/subscriptions">Subscriptions</Link>,
        className: menuElClass(pathname, "/dashboard/subscriptions"),
      },
    ],
    [pathname]
  );

  const accountingItems = useMemo<MenuProps["items"]>(
    () => [
      {
        key: "14",
        label: <Link href="/dashboard/invoices">Invoices</Link>,
        className: menuElClass(pathname, "/dashboard/invoices"),
      },
      {
        key: "15",
        label: <Link href="/dashboard/credit-notes">Credit Notes</Link>,
        className: menuElClass(pathname, "/dashboard/credit-notes"),
      },
      {
        key: "16",
        label: (
          <Link href="/dashboard/transactions">Chargebee transactions</Link>
        ),
        className: menuElClass(pathname, "/dashboard/transactions"),
      },
      {
        key: "17",
        label: (
          <Link href="/dashboard/unmatched-transactions">
            Unmatched transactions
          </Link>
        ),
        className: menuElClass(pathname, "/dashboard/unmatched-transactions"),
      },
    ],
    [pathname]
  );

  const devItems = useMemo<MenuProps["items"]>(
    () => [
      {
        key: "19",
        label: <Link href="/dashboard/cron-logs">Cron-logs</Link>,
        className: menuElClass(pathname, "/dashboard/cron-logs"),
      },
      {
        key: "22",
        label: <Link href="/dashboard/webhooks">Webhooks</Link>,
        className: menuElClass(pathname, "/dashboard/webhooks"),
      },
    ],
    [pathname]
  );

  const items = useMemo<MenuProps["items"]>(
    () => [
      {
        key: "1",
        icon: <HomeOutlined />,
        label: <Link href="/dashboard">Dashboard</Link>,
        className:
          pathname === "/" || pathname.includes("dashboard")
            ? "bg-graydark dark:bg-meta-4"
            : "",
      },
      {
        key: "2",
        icon: <UserOutlined />,
        label: "User",
        children: userItems,
      },
      {
        key: "5",
        icon: <ProductOutlined />,
        label: "Product Catalog",
        children: productCatalogItems,
      },
      {
        key: "9",
        icon: <ShoppingCartOutlined />,
        label: "Offering",
        children: offeringItems,
      },
      canAccess("accounting", false)
        ? {
            key: "13",
            icon: <BankOutlined />,
            label: "Accounting",
            children: accountingItems,
          }
        : null,
      canAccess("dev-tool", false)
        ? {
            key: "18",
            icon: <CodeOutlined />,
            label: "Dev Tools",
            children: devItems,
          }
        : null,
    ],
    [
      pathname,
      canAccess,
      userItems,
      productCatalogItems,
      offeringItems,
      accountingItems,
      devItems,
    ]
  );

  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <div style={styles.logo}>
          <Link href="/">
            <Image
              preview={false}
              style={styles.logoImage}
              height="100%"
              alt="logo"
              src="/images/logo/logo.png"
            />
          </Link>
        </div>
        <Button type="text" onClick={() => setCollapsed(!collapsed)}>
          {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </Button>
      </div>
      <Flex align="center" justify="center">
        <Tag color={currentConfig.color}>{currentConfig.text}</Tag>
      </Flex>
      <Menu
        mode="inline"
        style={styles.menu}
        items={items}
        defaultOpenKeys={["2", "5", "9", "13", "18"]}
      />
      <Flex align="center" justify="center" style={{ marginTop: 5 }}>
        <UserButton />
      </Flex>
    </div>
  );
};

export default React.memo(Sidebar);
