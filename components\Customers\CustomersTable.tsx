"use client";

import { Col, Divider, Form, Input, Row, Table, Tag, Typography } from "antd";
import { CalendarOutlined, SearchOutlined } from "@ant-design/icons";
import { Customer } from "chargebee";
import { useCallback, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";

import { useProtectPage } from "@/hooks/roles";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchCustomers } from "@/hooks/customers";
import { formatDateFromUnix } from "@/lib/date";
import { GetCustomersParams } from "@/types";
import paginationConfig from "../form/paginationConfig";

const { Text, Title } = Typography;

const initialFilters = {
  searchQuery: "",
  offset: null,
};

interface CustomerRecord {
  customer: Customer;
  id: string;
}

const columns = [
  {
    title: "Chargebee Id",
    key: "id",
    dataIndex: ["customer", "id"],
    render: (id: string) => (
      <Text type="secondary" copyable>
        {id}
      </Text>
    ),
  },
  {
    title: "Email",
    dataIndex: ["customer", "email"],
    render: (email: string) => (
      <Text type="secondary" copyable>
        {email}
      </Text>
    ),
  },
  {
    title: "First Name",
    dataIndex: ["customer", "first_name"],
  },
  {
    title: "Last Name",
    dataIndex: ["customer", "last_name"],
  },
  {
    title: "Business Entity",
    dataIndex: ["customer", "business_entity_id"],
    render: (id: string) => <Tag>{id}</Tag>,
  },
  {
    title: (
      <>
        <CalendarOutlined />
        &nbsp;Created at
      </>
    ),
    dataIndex: ["customer", "created_at"],
    render: (timestamp: number) => formatDateFromUnix(timestamp),
  },
  {
    title: (
      <>
        <CalendarOutlined />
        &nbsp;Updated at
      </>
    ),
    dataIndex: ["customer", "updated_at"],
    render: (timestamp: number) => formatDateFromUnix(timestamp),
  },
];

const CustomersTable = () => {
  const { canAccess } = useProtectPage("customers-list");
  const router = useRouter();
  const pathname = usePathname();
  const [filterForm] = Form.useForm();

  const [filters, setFilters] = useState<GetCustomersParams>(initialFilters);
  const searchTerm = useDebounce(filters.searchQuery);

  const {
    data,
    isLoading,
    mutate: refetchCustomers,
  } = useFetchCustomers({
    filters: {
      ...filters,
      searchQuery: searchTerm,
    },
  });

  const filterCustomers = (changedValues: any, allValues: any) => {
    if ("searchQuery" in changedValues) {
      setFilters({ ...allValues, offset: null });
    } else {
      setFilters(allValues);
    }
  };

  const handleRowClick = useCallback(
    (record: CustomerRecord) => {
      if (canAccess("customer-details")) {
        router.push(
          `${pathname}?customer=${record.customer.id}&email=${record.customer.email}`
        );
      }
    },
    [pathname, router, canAccess]
  );

  useEffect(() => {
    refetchCustomers();
  }, [refetchCustomers, searchTerm]);

  return (
    <div>
      <Title level={3}>Customers</Title>
      <Row style={{ lineHeight: "10px" }}>
        <Col span={24}>
          <Form form={filterForm} onValuesChange={filterCustomers}>
            <Form.Item name="searchQuery">
              <Input
                prefix={<SearchOutlined />}
                placeholder="Search by customer email"
              />
            </Form.Item>
          </Form>
        </Col>
      </Row>
      <Divider />
      <Table<CustomerRecord>
        dataSource={data?.list ?? data}
        loading={isLoading}
        columns={columns}
        pagination={{
          ...paginationConfig,
          defaultPageSize: 25,
          showSizeChanger: false,
        }}
        rowClassName="paradox-table-row"
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
        })}
        rowKey={(record) => record.customer.id}
        size="small"
      />
    </div>
  );
};

export default CustomersTable;
