"use client";

import { Image } from "antd";
import Link from "next/link";

export default function LinkChargebee({
  children,
  url,
}: {
  url: string;
  children: React.ReactNode;
}) {
  return (
    <Link passHref={true} target="_blank" href={url}>
      <Image
        src="/images/logo/cb.png"
        alt="Chargebee"
        width={16}
        height={16}
        preview={false}
      />
      &nbsp;
      {children}
    </Link>
  );
}
