"use client";

import { useEffect, useMemo, useState } from "react";
import countryList from "react-select-country-list";
import { EditOutlined, MailOutlined } from "@ant-design/icons";
import {
  Checkbox,
  Descriptions,
  Divider,
  Form,
  Input,
  Select,
  Tag,
} from "antd";

import InputPhone from "@/components/form/InputPhone";
import { WorkflowStep3Props } from "@/types";

const rules = [{ required: true, message: "This value is required!" }];

const WorkflowStep3 = ({
  customerData,
  error,
  handleBillingInfoChange,
  handleShippingInfoChange,
}: WorkflowStep3Props) => {
  const [formBilling] = Form.useForm();
  const [formShipping] = Form.useForm();
  const [sameAsBilling, setSameAsBilling] = useState<boolean>(true);
  const countryListOptions = useMemo(() => countryList().getData(), []);

  const updateBillingInfo = (values: any) => {
    formBilling.submit();
    handleBillingInfoChange(null, values);
  };

  const updateShippingInfo = (values: any) => {
    formShipping.submit();
    handleShippingInfoChange(null, values);
  };

  useEffect(() => {
    formBilling.setFieldsValue(customerData);
  }, [customerData]);

  useEffect(() => {
    if (sameAsBilling) {
      formShipping.setFieldsValue(formBilling.getFieldsValue());
      handleShippingInfoChange(null, formBilling.getFieldsValue());
    }
  }, [sameAsBilling]);

  useEffect(() => {
    updateBillingInfo(formBilling.getFieldsValue());
    updateShippingInfo(formShipping.getFieldsValue());
  }, [formBilling, formShipping]);

  return (
    <>
      {!customerData.id ? (
        <Tag color="orange" style={{ width: "100%", marginBottom: 20 }}>
          We will create a new customer using the following info
        </Tag>
      ) : null}

      <Form
        form={formBilling}
        onValuesChange={(_, values) => {
          handleBillingInfoChange(_, values);
          if (sameAsBilling) {
            formShipping.setFieldsValue(values);
            handleShippingInfoChange(_, values);
          }
        }}
        autoComplete="off"
        layout="vertical"
      >
        <Descriptions
          title={
            <>
              <span style={{ fontSize: 16, fontWeight: "bold" }}>
                Customer - Billing info
              </span>
              <br />
              {error ? <Tag color="red">{error}</Tag> : null}
            </>
          }
          bordered
          column={2}
          size="small"
        >
          <Descriptions.Item label="First name">
            <Form.Item name="first_name" rules={rules}>
              <Input
                placeholder="First name"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Last name">
            <Form.Item name="last_name" rules={rules}>
              <Input
                placeholder="Last name"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Email">
            <Form.Item
              name="email"
              rules={[
                ...rules,
                {
                  type: "email",
                  message: "This value must be a valid email",
                },
              ]}
            >
              <Input
                placeholder="Email"
                style={{ width: "100%" }}
                suffix={<MailOutlined />}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Phone number">
            <Form.Item name="phone">
              <InputPhone value={formBilling.getFieldValue("phone")} />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Company">
            <Form.Item name="company">
              <Input
                placeholder="Company"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Address">
            <Form.Item name="line1" rules={rules} label="Address line 1">
              <Input
                placeholder="line 1"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
              />
            </Form.Item>
            <Form.Item name="line2" label="Address line 2">
              <Input
                placeholder="line 2"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
              />
            </Form.Item>
            <Form.Item name="country" rules={rules} label="Country">
              <Select
                placeholder="Country"
                showSearch
                options={countryListOptions}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item name="city" rules={rules} label="City">
              <Input
                placeholder="City"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
              />
            </Form.Item>
            <Form.Item
              name="zip"
              label="Zip code"
              rules={[
                ...rules,
                () => ({
                  validator(_, value) {
                    if (!value) {
                      return Promise.reject();
                    }
                    if (value.length < 1) {
                      return Promise.reject(
                        "Zip code can't be less than 1 digits"
                      );
                    }
                    if (value.length > 20) {
                      return Promise.reject(
                        "Zip code can't be more than 20 digits"
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input
                placeholder="Zip code"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
              />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
      </Form>
      <Divider />
      <Form
        form={formShipping}
        onValuesChange={handleShippingInfoChange}
        autoComplete="off"
        layout="vertical"
      >
        <Descriptions
          title={
            <>
              <span style={{ fontSize: 16, fontWeight: "bold" }}>
                Shipping info
              </span>
              <br />
              <Checkbox
                checked={sameAsBilling}
                onChange={() => setSameAsBilling((prev) => !prev)}
              >
                Same as billing info
              </Checkbox>
            </>
          }
          bordered
          column={2}
          size="small"
        >
          <Descriptions.Item label="First name">
            <Form.Item name="first_name">
              <Input
                placeholder="First name"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Last name">
            <Form.Item name="last_name">
              <Input
                placeholder="Last name"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
                defaultValue={formBilling.getFieldValue("firstName")}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Email">
            <Form.Item name="email">
              <Input
                placeholder="Email"
                style={{ width: "100%" }}
                suffix={<MailOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Phone number">
            <Form.Item name="phone">
              <InputPhone
                value={formShipping.getFieldValue("phone")}
                disabled={sameAsBilling}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Company">
            <Form.Item name="company">
              <Input
                placeholder="Company"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
              />
            </Form.Item>
          </Descriptions.Item>
          <Descriptions.Item label="Address">
            <Form.Item name="line1" label="Address line 1">
              <Input
                placeholder="line 1"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
              />
            </Form.Item>
            <Form.Item name="line2" label="Address line 2">
              <Input
                placeholder="line 2"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
              />
            </Form.Item>
            <Form.Item name="country" label="Country">
              <Select
                placeholder="Country"
                options={countryListOptions}
                disabled={sameAsBilling}
                style={{ width: "100%" }}
              />
            </Form.Item>
            <Form.Item name="city" label="City">
              <Input
                placeholder="City"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
              />
            </Form.Item>
            <Form.Item name="zip" label="Zip code">
              <Input
                placeholder="Zip code"
                style={{ width: "100%" }}
                suffix={<EditOutlined />}
                readOnly={sameAsBilling}
                disabled={sameAsBilling}
              />
            </Form.Item>
          </Descriptions.Item>
        </Descriptions>
      </Form>
      {error ? <Tag color="red">{error}</Tag> : null}
    </>
  );
};

export default WorkflowStep3;
