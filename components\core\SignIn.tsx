"use client";

import { SignIn as SignIn<PERSON><PERSON>k, useAuth } from "@clerk/nextjs";
import { Flex } from "antd";
import { redirect } from "next/navigation";

export default function SignIn() {
  const { isSignedIn } = useAuth();
  if (isSignedIn) {
    redirect("/dashboard");
  } else {
    return (
      <Flex align="center" justify="center" style={{ height: "100%" }}>
        <SignInClerk />
      </Flex>
    );
  }
}
