"use client";

import { <PERSON>pp, Col, Divider, <PERSON>, Skeleton } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

import { RefundModal } from "./modals/RefundModal";
import { WriteOffModal } from "./modals/WriteOffModal";
import { CollectNowModal } from "./modals/CollectNowModal";
import { OfflineRefundModal } from "./modals/OfflineRefund";
import { CustomerAddress } from "./modals/CustomerAddress";
import InvoiceAddress from "./InvoiceAddress";
import InvoicePayments from "./InvoicePayments";
import InvoiceCreditNotes from "./InvoiceCreditNotes";
import { InvoiceHeader } from "./details/InvoiceHeader";
import { InvoiceSideMenu } from "./details/InvoiceSideMenu";
import { InvoiceInfo } from "./details/InvoiceInfo";
import { getInvoiceColumns } from "./details/InvoiceColumns";
import { useProtectPage } from "@/hooks/roles";
import {
  useFetchInvoice,
  useFetchInvoicePayments,
} from "@/hooks/accounting/invoices";
import { getInvoicePDF } from "@/lib/services/chargebee";
import { getSubscriptionParadoxApi } from "@/lib/services";
import { PX_Subscription } from "@/types";

type InvoiceIdProps = {
  invoiceId: string;
};

type Modals = {
  refundModalVisible: boolean;
  writeOffModalVisible: boolean;
  collectNowModalVisible: boolean;
  offlineRefundModalVisible: boolean;
  customerAddressModalVisible: boolean;
};

type PageData = {
  invoiceInfo: any;
  showRefund: boolean;
  showOfflineRefund: boolean;
  amountRefundable: number;
  subscription: PX_Subscription | undefined;
};
const InvoiceDetails = ({ invoiceId }: InvoiceIdProps) => {
  if (!invoiceId) {
    throw new Error("Invalid Invoice ID");
  }

  const router = useRouter();
  const { message } = App.useApp();
  useProtectPage("invoice-details");

  const [pageData, setPageData] = useState<PageData>({
    invoiceInfo: null,
    showRefund: true,
    showOfflineRefund: true,
    amountRefundable: 0,
    subscription: undefined,
  });
  const [modals, setModals] = useState<Modals>({
    refundModalVisible: false,
    writeOffModalVisible: false,
    collectNowModalVisible: false,
    offlineRefundModalVisible: false,
    customerAddressModalVisible: false,
  });

  // Queries
  const { data: invoice, mutate: refetchInvoice } = useFetchInvoice(invoiceId);
  const { data: payments } = useFetchInvoicePayments(
    invoice?.chargebeeId as string
  );

  const mappedPayments = useMemo(() => {
    return payments?.list.map((record) => {
      return record.transaction;
    });
  }, [payments]);

  const hasCardPayment = useMemo(() => {
    return mappedPayments
      ? mappedPayments.some((payment: any) => payment.payment_method === "card")
      : false;
  }, [mappedPayments]);

  const fetchSubscription = async () => {
    if (!invoice?.subscriptionId) return;
    const response = await getSubscriptionParadoxApi(
      invoice?.subscriptionId as string
    );
    setPageData({
      ...pageData,
      subscription: response,
    });
  };

  useEffect(() => {
    if (!invoice) return;

    // Initialize totals for VAT calculations
    let totalExclVatAllLineItems = 0; // Total amount excluding VAT
    let allVat = 0; // Total VAT amount
    let totalInclVat = 0; // Total amount including VAT

    invoice.lineItems &&
      invoice.lineItems.forEach((record) => {
        // Calculate discount amount excluding tax
        let discountAmountTaxExcluded = 0;
        if (
          invoice.priceType === "tax_inclusive" &&
          record.discount_amount &&
          record.tax_rate
        ) {
          // For tax-inclusive prices, we need to back out the VAT from the discount
          discountAmountTaxExcluded =
            record.discount_amount / (1 + record.tax_rate / 100);
        } else if (record.discount_amount) {
          // For tax-exclusive prices, discount amount is already tax-excluded
          discountAmountTaxExcluded = record.discount_amount;
        }

        // Calculate unit amount excluding tax
        let unitAmountTaxExcluded = 0;
        if (
          invoice?.priceType === "tax_inclusive" &&
          record.amount &&
          record.tax_rate
        ) {
          // For tax-inclusive prices, we need to back out the VAT from the unit amount
          unitAmountTaxExcluded = record.amount / (1 + record.tax_rate / 100);
        } else if (record.amount) {
          // For tax-exclusive prices, amount is already tax-excluded
          unitAmountTaxExcluded = record.amount;
        }

        // Add tax amount to total VAT
        if (record.tax_amount) {
          allVat += record.tax_amount;
        }

        // Calculate net amount (excluding VAT) for this line item and add to total
        totalExclVatAllLineItems +=
          unitAmountTaxExcluded - discountAmountTaxExcluded;
      });

    // Calculate final total including VAT
    totalInclVat = totalExclVatAllLineItems + allVat;

    // Update invoice info state with calculated totals
    let invoiceInf = {
      totalExclVatAllLineItems,
      allVat,
      totalInclVat,
    };
    setPageData({
      ...pageData,
      invoiceInfo: invoiceInf,
    });
  }, [invoice]);

  useEffect(() => {
    if (!invoice) return;

    let refundedAmount = 0;

    // Check if invoice has valid payments and a positive amount paid
    if (
      invoice.linkedPayments &&
      invoice.linkedPayments.length > 0 &&
      invoice.amountPaid &&
      invoice.amountPaid > 0
    ) {
      // Calculate total refunded amount from credit notes
      if (invoice.issuedCreditNotes && invoice.issuedCreditNotes.length > 0) {
        invoice.issuedCreditNotes.forEach((creditNote) => {
          if (creditNote?.cn_status === "refunded") {
            if (creditNote?.cn_total) {
              refundedAmount += creditNote?.cn_total;
            }
          }
        });
      }

      // Calculate written off amount (only use amountAdjusted)
      const writtenOffAmount = invoice.amountAdjusted || 0;

      // Calculate remaining refundable amount and determine if refunds should be shown
      const isFullyWrittenOff =
        invoice.total && writtenOffAmount >= invoice.total;
      const isFullyRefunded = refundedAmount >= invoice.amountPaid;
      const shouldShowRefunds = !isFullyWrittenOff && !isFullyRefunded;

      setPageData({
        ...pageData,
        showRefund: shouldShowRefunds,
        showOfflineRefund: shouldShowRefunds,
        amountRefundable: Math.max(0, invoice.amountPaid - refundedAmount),
      });
    } else {
      // If no valid payments, hide all refund options
      setPageData({
        ...pageData,
        showOfflineRefund: false,
        showRefund: false,
      });
    }
  }, [invoice]);

  useEffect(() => {
    if (!invoiceId) return;
    fetchSubscription();
  }, [invoiceId]);

  const columns = getInvoiceColumns(invoice);

  const downloadInvoicePDF = async () => {
    const res = await getInvoicePDF(invoiceId);
    if (res.download?.download_url) {
      window.open(res.download.download_url, "_blank");
    } else {
      message.open({
        type: "error",
        content: "Error - Failed to download invoice PDF",
      });
    }
  };

  return !invoice ? (
    <Skeleton />
  ) : (
    <div>
      <InvoiceHeader
        invoice={invoice}
        invoiceId={invoiceId}
        onBack={() => router.back()}
        onDownloadPDF={downloadInvoicePDF}
      />
      <Divider />
      <Row>
        <Col span={20} style={{ paddingRight: "20px" }}>
          <InvoiceInfo
            invoice={invoice}
            subscription={pageData.subscription}
            invoiceInfo={pageData.invoiceInfo}
            columns={columns}
          />
          <Divider />
          <Row>
            <Col span={12}>
              <InvoiceAddress invoice={invoice} />
            </Col>
            <Col span={12} style={{ paddingLeft: "20px" }}>
              <InvoicePayments invoice={invoice} payments={mappedPayments} />
              <Divider />
              <InvoiceCreditNotes invoice={invoice} />
              <br />
              <br />
            </Col>
          </Row>
        </Col>
        {/* Side menu items */}
        <Col span={4}>
          <InvoiceSideMenu
            invoice={invoice}
            subscription={pageData.subscription}
            showOfflineRefund={pageData.showOfflineRefund}
            showRefund={pageData.showRefund}
            hasCardPayment={hasCardPayment}
            setCustomerAddressModalVisible={() =>
              setModals((prev) => ({
                ...prev,
                customerAddressModalVisible: true,
              }))
            }
            setCollectNowModalVisible={() =>
              setModals((prev) => ({ ...prev, collectNowModalVisible: true }))
            }
            setOfflineRefundModalVisible={() =>
              setModals((prev) => ({
                ...prev,
                offlineRefundModalVisible: true,
              }))
            }
            setRefundModalVisible={() =>
              setModals((prev) => ({ ...prev, refundModalVisible: true }))
            }
            setWriteOffModalVisible={() =>
              setModals((prev) => ({ ...prev, writeOffModalVisible: true }))
            }
          />
        </Col>
      </Row>
      {modals.refundModalVisible && (
        <RefundModal
          amountRefundable={pageData.amountRefundable}
          isOpen={modals.refundModalVisible}
          invoice={invoice}
          refetchInvoice={refetchInvoice}
          setIsOpen={(state: boolean) =>
            setModals((prev) => ({ ...prev, refundModalVisible: state }))
          }
          showMarkAsRefunded={
            pageData.subscription?.orderStatus !== "stopped" &&
            pageData.subscription?.orderStatus !== "refunded"
          }
        />
      )}
      {modals.writeOffModalVisible && (
        <WriteOffModal
          isOpen={modals.writeOffModalVisible}
          invoice={invoice}
          setIsOpen={(state: boolean) =>
            setModals((prev) => ({ ...prev, writeOffModalVisible: state }))
          }
        />
      )}
      {modals.collectNowModalVisible && (
        <CollectNowModal
          amountRefundable={pageData.amountRefundable}
          isOpen={modals.collectNowModalVisible}
          invoice={invoice}
          isAutoCollectionOff={pageData.subscription?.autoCollection === "off"}
          refetchInvoice={refetchInvoice}
          setIsOpen={(state: boolean) =>
            setModals((prev) => ({ ...prev, collectNowModalVisible: state }))
          }
        />
      )}
      {modals.offlineRefundModalVisible && (
        <OfflineRefundModal
          amountRefundable={pageData.amountRefundable}
          isOpen={modals.offlineRefundModalVisible}
          invoice={invoice}
          refetchInvoice={refetchInvoice}
          setIsOpen={(state: boolean) =>
            setModals((prev) => ({ ...prev, offlineRefundModalVisible: state }))
          }
          showMarkAsRefunded={
            pageData.subscription?.orderStatus !== "stopped" &&
            pageData.subscription?.orderStatus !== "refunded"
          }
        />
      )}
      {modals.customerAddressModalVisible && (
        <CustomerAddress
          isOpen={modals.customerAddressModalVisible}
          invoice={invoice}
          setIsOpen={(state: boolean) =>
            setModals((prev) => ({
              ...prev,
              customerAddressModalVisible: state,
            }))
          }
        />
      )}
    </div>
  );
};

export default InvoiceDetails;
