"use client";

import { CalendarOutlined, RobotFilled } from "@ant-design/icons";
import { Tag, Descriptions } from "antd";
import { Subscription } from "chargebee";
import Link from "next/link";

import { calculateTagBgColorWithStatus } from "../utils/styles";
import PageHeader from "@/components/core/PageHeader";
import { formatDateFromUnix } from "@/lib/date";
import { getDealLink } from "@/lib/hubspot";
import { capitalizeWord } from "@/lib/text";
import { PX_Subscription } from "@/types";
import { getCustomerLink, getSubscriptionLink } from "@/lib/chargebee";
import LinkChargebee from "@/components/core/LinkChargebee";

type SubscriptionDetailsHeaderProps = {
  subscriptionCBInfo: Subscription;
  subscriptionPX: PX_Subscription;
};

export default function SubscriptionDetailsHeader({
  subscriptionCBInfo,
  subscriptionPX,
}: SubscriptionDetailsHeaderProps) {
  return (
    <PageHeader
      title={
        <>
          {subscriptionPX.chargebeeId.indexOf("PX_DBG") > -1 ? (
            <Tag color="orange" style={{ fontSize: 16 }}>
              <RobotFilled />
            </Tag>
          ) : null}
          &nbsp;
          <span>Subscription Details</span>
        </>
      }
    >
      <Descriptions title="" bordered size="small" column={3}>
        <Descriptions.Item label="Order status">
          <Tag
            color={calculateTagBgColorWithStatus(
              subscriptionPX?.orderStatus as PX_Subscription["orderStatus"]
            )}
          >
            {capitalizeWord(subscriptionPX?.orderStatus as string)}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Chargebee Subscription">
          <LinkChargebee
            url={getSubscriptionLink(subscriptionPX?.chargebeeId ?? "")}
          >
            {subscriptionPX?.chargebeeId}
          </LinkChargebee>
        </Descriptions.Item>
        <Descriptions.Item label="Chargebee Customer">
          <LinkChargebee
            url={getCustomerLink(subscriptionPX?.customerId ?? "")}
          >
            {subscriptionPX?.customerId}
          </LinkChargebee>
        </Descriptions.Item>
        {/* @ts-ignore */}
        {subscriptionCBInfo?.subscription?.cf_deal_id ? (
          <Descriptions.Item label="Deal ID">
            <Link
              target="_blank"
              passHref={true}
              // @ts-ignore
              href={getDealLink(subscriptionCBInfo?.subscription?.cf_deal_id)}
            >
              {/* @ts-ignore */}
              {subscriptionCBInfo?.subscription?.cf_deal_id}
            </Link>
          </Descriptions.Item>
        ) : null}
        {subscriptionPX.customFields?.cf_os_offer_id ? (
          <Descriptions.Item label="Linked Offer">
            <Link
              passHref={true}
              target="_blank"
              href={`/dashboard/offers/${subscriptionPX.customFields?.cf_os_offer_id}`}
            >
              {subscriptionPX.metadata?.offerName ||
                subscriptionPX.customFields?.cf_os_offer_id}
            </Link>
          </Descriptions.Item>
        ) : null}
      </Descriptions>
      <Descriptions
        title=""
        bordered
        size="small"
        column={3}
        style={{ marginTop: 10 }}
      >
        <Descriptions.Item
          label={
            <>
              <CalendarOutlined /> Created on
            </>
          }
        >
          {formatDateFromUnix(subscriptionPX?.createdAt)}
        </Descriptions.Item>
        {subscriptionPX?.startedAt && (
          <Descriptions.Item
            label={
              <>
                <CalendarOutlined /> Started on
              </>
            }
          >
            {formatDateFromUnix(subscriptionPX?.startedAt)}
          </Descriptions.Item>
        )}
        {subscriptionPX?.cancelScheduleCreatedAt && (
          <Descriptions.Item
            label={
              <>
                <CalendarOutlined /> Cancelled on
              </>
            }
          >
            {formatDateFromUnix(subscriptionPX?.cancelScheduleCreatedAt)}
          </Descriptions.Item>
        )}
      </Descriptions>
    </PageHeader>
  );
}
