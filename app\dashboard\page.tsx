import { auth } from "@clerk/nextjs/server";
import { Metadata } from "next";

import Welcome from "@/components/core/Welcome";

export const metadata: Metadata = {
  title: "ParadoxOS",
};

const DashboardPage: React.FC = async () => {
  const { userId, sessionClaims } = await auth();
  if (!userId) {
    return <h1>Loading...</h1>;
  }

  const firstName = sessionClaims?.firstName as string;

  return <Welcome firstName={firstName} />;
};

export default DashboardPage;
