import { FindPlanPriceParams, SearchItemPricesParams } from "@/types";
import { requestChargebee } from ".";
import { Item, ItemPrice } from "chargebee";

/**
 * Retrieves the item from the server.
 * @param id - The ID of the item.
 * @returns A promise that resolves to the item price.
 */
export async function getItem(
  id: string
): Promise<Item.RetrieveResponse["item"]> {
  return requestChargebee(`/api/items/${id}`, "GET");
}

/**
 * Searches for item prices based on the provided parameters.
 *
 * @param payload - The parameters for searching item prices.
 * @returns A Promise that resolves to the response JSON object.
 */
export async function searchItemPrices(
  payload: SearchItemPricesParams
): Promise<ItemPrice[]> {
  return requestChargebee(
    `/api/items/${payload.itemIds[0]}/search-item-prices`,
    "POST",
    payload
  );
}

/**
 * Retrieves the price of a chargebee plan.
 * @params payload - the plan ID and the currency of the price to find
 * @returns A promise that resolves to the plan price.
 */
export async function getPlanPrice(
  payload: FindPlanPriceParams
): Promise<ItemPrice> {
  return requestChargebee(
    `/api/items/${payload.planId}/find-plan-price`,
    "POST",
    payload
  );
}
