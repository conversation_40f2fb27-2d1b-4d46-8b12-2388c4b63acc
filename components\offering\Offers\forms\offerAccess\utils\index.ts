"use client";

import {
  CircleSpaceGroup,
  CommunityInfo,
  CommunityInfoDTO,
  CommunityType,
  CourseInfo,
  Offer,
  Product,
  SpaceInfo,
} from "@/types";

/**
 * Extracts and organizes community and space information from an array of community access data
 * @param communityAccessInfo - Array of community information DTOs containing community and space details
 * @returns Object containing arrays of communities and their associated spaces
 */
export const getCurrentSpaceAndCommunityInfo = (
  communityAccessInfo: CommunityInfoDTO[]
): {
  communities: CommunityType[];
  spaces: SpaceInfo[];
} => {
  let spaces: SpaceInfo[] = [];
  const communities: CommunityType[] = [];
  for (const communityInfo of communityAccessInfo) {
    spaces.push(...communityInfo.spaces);
    communities.push(communityInfo.community);
  }

  return {
    communities,
    spaces,
  };
};

/**
 * Extracts and organizes course information from an array of offer products
 * @param offerProducts - Array of offer products containing course information
 * @returns Array of course information objects
 */
export const getDefaultCourses = (products: Product[]) => {
  return products
    .map((product: Product) => {
      return product.lmsIds;
    })
    .flat()
    .filter((course) => course !== undefined) as CourseInfo[];
};

/**
 * Extracts and organizes community information from an array of offer products
 * @param offerProducts - Array of offer products containing community information
 * @returns Array of community information objects
 */
export const getDefaultCommunities = (products: Product[]) => {
  return products
    .map((product: Product) => {
      return product.communityIds;
    })
    .flat()
    .filter((comm) => comm !== undefined) as CommunityInfo[];
};

/**
 * Returns the community type based on the community ID
 * @param communityId - Community ID
 * @returns Community type
 */
export const getCommunityById = (communityId: number) => {
  if (String(communityId) === process.env.NEXT_PUBLIC_PXL_COMMUNITY_ID) {
    return CommunityType.PXL;
  } else if (String(communityId) === process.env.NEXT_PUBLIC_PXS_COMMUNITY_ID) {
    return CommunityType.PXS;
  }
  return null;
};

export const getCommunityBySpaceId = (
  spaceId: number,
  spacesFromCircle: any[]
) => {
  const space = spacesFromCircle.find((space) => space.id === spaceId);
  return getCommunityById(space?.community_id);
};

/**
 * Builds an array of community IDs based on the community types present
 * @param communities - Array of community types
 * @returns Array of community IDs
 */
export const buildCommunityIds = (communities: CommunityType[]) => {
  const communityIds: (string | undefined)[] = [];
  if (communities.includes(CommunityType.PXL)) {
    communityIds.push(process.env.NEXT_PUBLIC_PXL_COMMUNITY_ID);
  }
  if (communities.includes(CommunityType.PXS)) {
    communityIds.push(process.env.NEXT_PUBLIC_PXS_COMMUNITY_ID);
  }
  return communityIds;
};

type PrepareSpaceLeafsProps = {
  spaceGroupId: number;
  spaces: any[];
  selectedSpaces: SpaceInfo[];
};

function prepareSpaceLeafs({
  spaceGroupId,
  spaces,
  selectedSpaces,
}: PrepareSpaceLeafsProps) {
  const spacesBySpaceGroupId = spaces.filter(
    (space) => space.space_group_id === spaceGroupId
  );
  return spacesBySpaceGroupId
    .map((space: any) => ({
      title: space.name,
      key: space.id,
      value: space.id,
      checkable: true,
      checked: selectedSpaces.some((s) => s.id === space.id),
      meta: {
        spaceGroupId: space.space_group_id,
      },
    }))
    .sort((a: any, b: any) => a.title.localeCompare(b.title));
}

type PrepareSpaceGroupFolderProps = {
  community: number;
  selectedSpaces: SpaceInfo[];
  spaces: any[];
  spaceGroups: CircleSpaceGroup[];
  searchValue: string;
};

/**
 * Prepares space group folder for the TreeSelect component (SelectCommunitiesAndSpacesModal)
 * @param spaceGroups - Space groups
 * @returns Array of space group folder objects
 */
function prepareSpaceGroupFolder({
  community,
  selectedSpaces,
  spaces,
  spaceGroups,
  searchValue,
}: PrepareSpaceGroupFolderProps) {
  const spaceGroupsByCommunityId = spaceGroups.filter(
    (group) => group.community_id === +community
  );
  let spaceGroupFoldersAndLeafs = [];
  for (const group of spaceGroupsByCommunityId) {
    spaceGroupFoldersAndLeafs.push({
      title: group.name,
      key: group.id,
      value: group.id,
      checkable: true,
      open:
        group.space_order_array.filter((id) =>
          selectedSpaces.some((s) => s.id === id)
        ).length > 0,
      children: prepareSpaceLeafs({
        spaceGroupId: group.id,
        spaces: spaces,
        selectedSpaces: selectedSpaces,
      }),
    });
  }
  return spaceGroupFoldersAndLeafs;
}

/**
 * Prepares tree data for the SelectCommunitiesAndSpacesModal component
 * @param communities - Array of community types
 * @param spaces - Array of space information objects
 * @returns Array of tree data objects
 */
type PrepareTreeDataProps = {
  communities: CommunityType[];
  spaces: any[];
  spaceGroups: CircleSpaceGroup[];
  communityOptions: any[];
  selectedSpaces: SpaceInfo[];
  searchValue: string;
};
export const prepareTreeData = ({
  communities,
  spaces,
  spaceGroups,
  communityOptions,
  selectedSpaces,
  searchValue,
}: PrepareTreeDataProps) => {
  const treeData = [
    ...communities.map((community) => ({
      title: community,
      key: community,
      value: community,
      checkable: false,
      open: true,
      children: prepareSpaceGroupFolder({
        community: communityOptions.find((option) => option.value === community)
          ?.id,
        selectedSpaces,
        spaces,
        spaceGroups,
        searchValue,
      }),
    })),
  ];

  return treeData;
};

/**
 * Checks if an offer has both community and LMS product access configurations
 * @param offer - The offer to check
 * @returns boolean indicating if the offer has both community and LMS product accesses configured
 */
export const isOfferAccessConfigured = (offer: Offer) => {
  const communityProductAccesses = [
    ...(offer.communityIds?.products || []),
    ...(offer.communityIds?.recommended || []),
  ];
  const lmsProductAccesses = [
    ...(offer.lmsIds?.products || []),
    ...(offer.lmsIds?.recommended || []),
  ];

  const hasCommunityAccess =
    Array.isArray(communityProductAccesses) &&
    communityProductAccesses.length > 0;
  const hasLmsAccess =
    Array.isArray(lmsProductAccesses) && lmsProductAccesses.length > 0;

  return { hasCommunityAccess, hasLmsAccess };
};
