"use client";

import { Space } from "antd";

import { Transaction } from "@/types";
import { getMonthOfYear } from "@/lib/date";

export const getTransactionDetailsPaymentMethod = ({
  transaction,
}: {
  transaction: Transaction | undefined;
}) => [
  {
    key: "1",
    label: "Payment method",
    children: (
      <Space>
        {transaction?.paymentMethod &&
          (transaction.paymentMethod === "card" ? (
            <p style={{ margin: 0 }}>
              Card{" "}
              {transaction.paymentMethodDetails &&
                JSON.parse(
                  transaction.paymentMethodDetails
                ).card.brand.toUpperCase()}{" "}
              {transaction.maskedCardNumber}
            </p>
          ) : (
            <p>Bank transfer</p>
          ))}
      </Space>
    ),
  },
  {
    key: "2",
    label: "Expires",
    children: (
      <Space>
        {transaction?.paymentMethod &&
          (transaction.paymentMethod === "card" ? (
            <p style={{ margin: 0 }}>
              {transaction.paymentMethodDetails &&
                getMonthOfYear(
                  JSON.parse(transaction.paymentMethodDetails).card.expiry_month
                )}{" "}
              {transaction.paymentMethodDetails &&
                JSON.parse(transaction.paymentMethodDetails).card.expiry_year}
            </p>
          ) : (
            <p>Bank transfer</p>
          ))}
      </Space>
    ),
  },
  {
    key: "3",
    label: "Gateway Account",
    children: <Space>{transaction?.gateway}</Space>,
  },
  {
    key: "4",
    label: "Gateway transaction id",
    children: <Space>{transaction?.idAtGateway}</Space>,
  },
];
