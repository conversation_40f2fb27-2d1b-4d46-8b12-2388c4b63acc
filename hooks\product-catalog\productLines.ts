"use client";

import useSWR, { SWRConfiguration } from "swr";

import limits from "@/lib/limits";
import { getAllProductLines } from "@/lib/services";
import { ProductLineFilters, ProductLineListResponse } from "@/types";

type UseFetchProductLinesParams = {
  options?: SWRConfiguration; // Adjust type as needed for SWR options
  filters?: ProductLineFilters;
};

/**
 * Fetches product lines
 * @param params - Filters and options
 * @returns Product lines
 */
export const useFetchProductLines = (params?: UseFetchProductLinesParams) => {
  const status = params?.filters?.status || "active";
  const search = params?.filters?.search || "";

  return useSWR<ProductLineListResponse>(
    `useFetchProductLines`,
    () => getAllProductLines(1, limits.productLines, status, search),
    params?.options
  );
};
