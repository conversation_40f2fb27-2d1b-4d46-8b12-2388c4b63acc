import { Metadata } from "next";
import InvoiceDetails from "@/components/accounting/Invoices/InvoiceDetails";

export const metadata: Metadata = {
  title: "Invoice",
};

type InvoiceIdProps = {
  invoiceId: string;
};

const InvoicesPage = async (props: { params: Promise<InvoiceIdProps> }) => {
  const params = await props.params;
  if (!params.invoiceId) {
    throw new Error("Invalid Invoice ID");
  }

  return <InvoiceDetails invoiceId={params.invoiceId} />;
};

export default InvoicesPage;
