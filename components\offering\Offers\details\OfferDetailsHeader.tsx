"use client";

import {
  Button,
  Descriptions,
  Dropdown,
  Flex,
  MenuProps,
  Space,
  Tag,
  Typography,
} from "antd";
import dayjs from "dayjs";

import PageHeader from "@/components/core/PageHeader";
import Link<PERSON>hargebee from "@/components/core/LinkChargebee";
import { getPlanLink } from "@/lib/chargebee";
import { formatDate } from "@/lib/date";
import { Offer } from "@/types";
import {
  CopyOutlined,
  EditOutlined,
  EllipsisOutlined,
  EyeOutlined,
  RocketOutlined,
} from "@ant-design/icons";

type OfferDetailsHeaderProps = {
  offer: Offer;
  onActivate: () => void;
  onDisable: () => void;
  onDuplicateWithProductDelivery: () => void;
  onDuplicateWithoutProductDelivery: () => void;
  onEdit: () => void;
  onPreview: () => void;
  onPublish: () => void;
};

const { Text } = Typography;

export default function OfferDetailsHeader({
  offer,
  onActivate,
  onDisable,
  onDuplicateWithProductDelivery,
  onDuplicateWithoutProductDelivery,
  onEdit,
  onPreview,
  onPublish,
}: OfferDetailsHeaderProps) {
  const items: MenuProps["items"] = [
    {
      label: (
        <Space onClick={onDuplicateWithProductDelivery}>
          <CopyOutlined />
          <Text>Duplicate with Product delivery</Text>
        </Space>
      ),
      key: "1",
    },
    {
      label: (
        <Space onClick={onDuplicateWithoutProductDelivery}>
          <CopyOutlined />
          <Text>Duplicate without Product delivery</Text>
        </Space>
      ),
      key: "2",
    },
    {
      label:
        offer?.status === "active" ? (
          <Space onClick={onDisable}>
            <Text>Switch to disable</Text>
          </Space>
        ) : (
          <Space onClick={onActivate}>
            <Text>Activate</Text>
          </Space>
        ),
      key: "3",
    },
  ];
  return (
    <PageHeader
      title="Offer Details"
      actions={
        <Flex vertical gap="small">
          <Flex gap="small" wrap>
            <Button icon={<EditOutlined />} type="primary" onClick={onEdit}>
              Edit Offer
            </Button>
            <Button
              disabled={offer?.config ? false : true}
              icon={<EyeOutlined />}
              onClick={onPreview}
            >
              <Text strong>Preview</Text>
            </Button>
            <Button
              disabled={
                offer?.config &&
                offer.version === "preview" &&
                offer.status === "active"
                  ? false
                  : true
              }
              icon={<RocketOutlined />}
              onClick={onPublish}
            >
              <Text strong>Publish</Text>
            </Button>
            <Dropdown menu={{ items }}>
              <Button icon={<EllipsisOutlined />}>
                <Text strong>Actions</Text>
              </Button>
            </Dropdown>
          </Flex>
        </Flex>
      }
    >
      <Descriptions title="" bordered size="small" column={3}>
        <Descriptions.Item label="Offer ID">
          <Text copyable>{offer.id}</Text>
        </Descriptions.Item>
        <Descriptions.Item label="Offer Name">
          <Text copyable>{offer.name}</Text>
        </Descriptions.Item>
        <Descriptions.Item label="Offer Status">
          <Tag color={offer?.status === "active" ? "green" : "orange"}>
            {offer?.status === "active" ? "Active" : "Disabled"}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Offer Code">
          <LinkChargebee url={getPlanLink(offer.chargebeeId as string)}>
            {offer.chargebeeId}
          </LinkChargebee>
        </Descriptions.Item>
        <Descriptions.Item label="Last Modified">
          {formatDate(dayjs(offer?.updatedAt).unix() * 1000)}
        </Descriptions.Item>
      </Descriptions>
    </PageHeader>
  );
}
