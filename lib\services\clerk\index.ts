/**
 * Makes an HTTP request to the Clerk API
 * @param {string} url - The URL endpoint to send the request to
 * @param {string} method - The HTTP method to use (GET, POST, PUT, DELETE, etc.)
 * @param {any} [payload] - Optional data to send with the request (for non-GET requests)
 * @returns {Promise<any>} The parsed JSON response from the API
 */
export const requestClerk = async (
  url: string = "",
  method: string,
  payload?: any
) => {
  const baseUrl = new URL(window.location.href).origin;
  let requestInit: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
    },
  };
  if (method !== "GET" && payload) {
    requestInit.body = JSON.stringify(payload);
  }
  try {
    const computedUrl = baseUrl + url;
    const response = await fetch(computedUrl, requestInit);
    const data = await response.json();
    return data;
  } catch (err) {
    console.error("requestClerk error(s)", err);
    throw err;
  }
};

export * from "./users";
