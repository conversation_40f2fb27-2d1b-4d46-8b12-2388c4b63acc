"use client";

import { useEffect, useRef, useState } from "react";

export const useDebounce = (value: any, delay = 1000) => {
  const [debouncedValue, setDebouncedValue] = useState("");
  const timerRef = useRef<any>(undefined);

  useEffect(() => {
    timerRef.current = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timerRef.current);
    };
  }, [value, delay]);

  return debouncedValue;
};
