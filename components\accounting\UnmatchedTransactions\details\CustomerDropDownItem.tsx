"use client";

import { getBusinessEntityName } from "@/lib/businessEntities";
import { Typography } from "antd";

const { Text } = Typography;

const CustomerDropDownItem = ({ customer }: any) => {
  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text style={{ fontSize: 16 }}>
          {customer?.first_name} {customer?.last_name}
        </Text>
      </div>
      <div style={{ marginTop: "5px" }}>
        <Text style={{ color: "gray", fontSize: 13 }}>Email: </Text>
        <Text style={{ fontSize: 12 }}>{customer?.email}</Text>
        <br />
        <Text style={{ color: "gray", fontSize: 13 }}>Billing Entity: </Text>
        <Text style={{ fontSize: 12 }}>
          {getBusinessEntityName(customer?.business_entity_id)}
        </Text>
        <br />
        <Text style={{ color: "gray", fontSize: 13 }}>Phone: </Text>
        <Text style={{ fontSize: 12 }}>{customer?.phone}</Text>
        <br />
      </div>
    </div>
  );
};

export default CustomerDropDownItem;
