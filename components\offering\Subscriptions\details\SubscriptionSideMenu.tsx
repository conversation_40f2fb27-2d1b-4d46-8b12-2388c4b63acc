"use client";

import {
  CalendarOutlined,
  CodeOutlined,
  CreditCardOutlined,
  EditOutlined,
  StopOutlined,
  SwapOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { Affix, Divider, Space } from "antd";
import { Subscription } from "chargebee";

import SideMenuActionItem from "@/components/core/SideMenuActionItem";
import SubscriptionTimeline from "./SubscriptionTimeline";
import { PX_Subscription, SubscriptionHistoricalData } from "@/types";

type SubscriptionSideMenuProps = {
  subscriptionCBInfo: Subscription | undefined;
  subscriptionPX: PX_Subscription;
  historicalDataPX: SubscriptionHistoricalData;
  onChangePaymentMethod: () => void;
  onEditNextBillingCycle: () => void;
  onEditSubscription: () => void;
  onDownsellSubscription: () => void;
  onCancelSubscription: () => void;
  onRefreshSubscription: () => Promise<void>;
  onExportSubscriptionData: () => void;
};

export default function SubscriptionSideMenu({
  subscriptionCBInfo,
  subscriptionPX,
  historicalDataPX,
  onRefreshSubscription,
  onChangePaymentMethod,
  onEditNextBillingCycle,
  onEditSubscription,
  onDownsellSubscription,
  onCancelSubscription,
  onExportSubscriptionData,
}: SubscriptionSideMenuProps) {
  return (
    <Affix offsetTop={20} style={{ overflowY: "auto" }}>
      <Space direction="vertical" style={{ padding: "0px 10px" }}>
        <SideMenuActionItem
          title={
            <>
              <SyncOutlined />
              &nbsp;
              <span>Refresh subscription</span>
            </>
          }
          description="Refresh the subscription to get the latest data from the PX API."
          onClick={onRefreshSubscription}
          permission={false}
        />

        <SideMenuActionItem
          title={
            <>
              <CreditCardOutlined />
              &nbsp;
              <span>Change payment method</span>
            </>
          }
          description="Change the payment method attached to this order."
          onClick={onChangePaymentMethod}
          permission="subscription-change-payment-method"
        />

        <SideMenuActionItem
          title={
            <>
              <CalendarOutlined />
              &nbsp;
              <span>Edit billing date</span>
            </>
          }
          description="Change the date or time of renewal and payment collection."
          onClick={onEditNextBillingCycle}
          permission="subscription-update-next-billing-date"
          disabled={
            // @ts-ignore
            subscriptionCBInfo?.subscription?.status === "cancelled" ||
            // @ts-ignore
            !subscriptionCBInfo?.subscription?.next_billing_at
          }
          tooltipTitle={
            // @ts-ignore
            subscriptionCBInfo?.subscription?.status === "cancelled"
              ? "Cannot edit subscription for cancelled subscriptions"
              : // @ts-ignore
                !subscriptionCBInfo?.subscription?.next_billing_at
                ? "Cannot edit subscription as there is no next billing date"
                : undefined
          }
        />

        <SideMenuActionItem
          title={
            <>
              <EditOutlined />
              &nbsp;
              <span>Edit subscription</span>
            </>
          }
          description="Change the payment plan for this subscription and all the product attached."
          onClick={onEditSubscription}
          permission="subscription-edit"
          disabled={
            // @ts-ignore
            subscriptionCBInfo?.subscription?.status === "cancelled" ||
            // @ts-ignore
            !subscriptionCBInfo?.subscription?.next_billing_at
          }
          tooltipTitle={
            // @ts-ignore
            subscriptionCBInfo?.subscription?.status === "cancelled"
              ? "Cannot edit subscription for cancelled subscriptions"
              : // @ts-ignore
                !subscriptionCBInfo?.subscription?.next_billing_at
                ? "Cannot edit subscription as there is no next billing date"
                : undefined
          }
        />

        <SideMenuActionItem
          title={
            <>
              <SwapOutlined />
              &nbsp;
              <span>Change product</span>
            </>
          }
          description="Change the associated offer and products for this subscription."
          onClick={onDownsellSubscription}
          permission="subscription-downsell"
          disabled={
            // @ts-ignore
            subscriptionCBInfo?.subscription?.status === "cancelled" ||
            // @ts-ignore
            !subscriptionCBInfo?.subscription?.next_billing_at
          }
          tooltipTitle={
            // @ts-ignore
            subscriptionCBInfo?.subscription?.status === "cancelled"
              ? "Cannot change product for cancelled subscriptions"
              : // @ts-ignore
                !subscriptionCBInfo?.subscription?.next_billing_at
                ? "Cannot change product as there is no next billing date"
                : undefined
          }
        />

        <SideMenuActionItem
          title={
            <>
              <CodeOutlined />
              &nbsp;
              <span>Export subscription data</span>
            </>
          }
          description="Export the subscription data to a JSON file."
          onClick={onExportSubscriptionData}
          permission="subscription-debug-export"
          disabled={subscriptionPX.chargebeeId.indexOf("PX_DBG") > -1}
          tooltipTitle={
            subscriptionPX.chargebeeId.indexOf("PX_DBG") > -1
              ? "This feature is not available in debug subscriptions"
              : undefined
          }
        />

        <Divider style={{ margin: "0px" }} />

        <SideMenuActionItem
          title={
            <>
              <StopOutlined />
              &nbsp;
              <span>Cancel subscription</span>
            </>
          }
          description="Cancel the subscription and stop all future payments."
          onClick={onCancelSubscription}
          permission="subscription-cancel"
          disabled={
            // @ts-ignore
            subscriptionPX?.orderStatus === "paid" ||
            // @ts-ignore
            subscriptionCBInfo?.subscription?.status === "cancelled"
          }
          color="danger"
          tooltipTitle={
            // @ts-ignore
            subscriptionPX?.orderStatus === "paid"
              ? "Cannot cancel subscription as it is already paid"
              : // @ts-ignore
                subscriptionCBInfo?.subscription?.status === "cancelled"
                ? "Cannot cancel subscription as it is already cancelled"
                : undefined
          }
        />
        <Divider style={{ margin: "0px" }} />
        {subscriptionCBInfo && (
          <SubscriptionTimeline
            subscriptionPX={subscriptionPX}
            historicalDataPX={historicalDataPX}
          />
        )}
      </Space>
    </Affix>
  );
}
