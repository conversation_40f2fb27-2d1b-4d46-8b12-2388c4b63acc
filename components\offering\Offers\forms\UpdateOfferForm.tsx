"use client";

import { InfoCircleTwoTone } from "@ant-design/icons";
import {
  Col,
  ColorPicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Typography,
  Checkbox,
} from "antd";
import { useState } from "react";

import UploadImage from "@/components/core/UploadImage";
import { getPaymentGateways } from "@/lib/paymentGateways";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";

type AddProps = {
  isModalOpen: boolean;
  handleAdd: Function;
  handleCancel: Function;
  initValues: any;
};

const rules = [{ required: true, message: "This value is required!" }];
const { Title, Text } = Typography;

const UpdateOfferForm = ({
  isModalOpen,
  handleAdd,
  handleCancel,
  initValues,
}: AddProps) => {
  const [offerImage, setOfferImage] = useState(initValues?.image);
  const [bannerImage, setBannerImage] = useState(initValues?.bannerImage);
  const [cardImage, setCardImage] = useState(initValues?.cardImage);
  const [offerBannerImage, setOfferBannerImage] = useState(
    initValues?.offerBannerImage
  );
  const [form] = Form.useForm();

  const formatValues = (values: any) => {
    let testimonial;

    if (values.template === "B" && values.showTestimonial) {
      testimonial = {
        name: values.testimonyName,
        title: values.testimonyTitle,
        comment: values.testimonyComment,
        url: values.testimonyUrl,
        rating: values.testimonyRating,
      };
    } else {
      testimonial = {
        name: undefined,
        title: undefined,
        comment: undefined,
        url: undefined,
        rating: undefined,
      };
    }
    let offer = {
      offerInfo: {
        name: values.name,
        externalName: values.externalName,
        currency: values.currency,
        fiscalEntity: values.fiscalEntity,
        image: offerImage,
        redirectUrl: values.redirectUrl,
        paymentGateway: values.paymentGateway,
        description: values.description,
        cardImage: cardImage,
        bannerImage: offerBannerImage,
        usp: values.usp,
      },
      checkoutInfo: {
        bannerImage: bannerImage,
        mainColor:
          typeof values.mainColor === "string"
            ? values.mainColor
            : values.mainColor.toHexString(),
        template: values.template,
        testimony: testimonial,
        cancellationPolicy: values.cancellationPolicy,
      },
    };
    handleAdd(offer);
  };

  return (
    <DrawerFormContainer
      title="Update Offer"
      isOpen={isModalOpen}
      onClose={() => {
        handleCancel();
      }}
      submitText="Update"
      onSubmit={() => {
        form.submit();
      }}
      onCancel={() => {
        handleCancel();
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={(values) => {
          formatValues(values);
        }}
        initialValues={{
          ...initValues,
          showTestimonial: !(
            initValues.testimonyName === undefined &&
            initValues.testimonyTitle === undefined &&
            initValues.testimonyComment === undefined &&
            initValues.testimonyRating === undefined &&
            initValues.testimonyUrl === undefined
          ),
        }}
      >
        <Title level={5}>Offer settings</Title>
        <div
          className="paradox-card-white"
          style={{
            borderRadius: "10px",
          }}
        >
          <Row>
            <Col span={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <InfoCircleTwoTone style={{ fontSize: 25 }} />
              </div>
            </Col>
            <Col span={22} offset={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                You can associate products and plans from the details page after
                creating the offer.
              </div>
            </Col>
          </Row>
        </div>
        <br />
        <Form.Item
          label="Offer name "
          name="name"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          A descriptive name for this offer. It can be used in the checkout page
          based on your offer configuration.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Offer external name "
          name="externalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          ⚠️ This offer name is visible to the customer, on the portal and
          checkout page
        </Text>
        <br />
        <br />
        <Form.Item
          label="Offer description "
          name="description"
          style={{ marginBottom: "0px" }}
          rules={[{ min: 5, message: "Please enter at least 5 characters" }]}
        >
          <Input.TextArea maxLength={500} />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          A comprehensive description for this offer. It will be used in the
          customer portal when customers view their purchases.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Offer USP "
          name="usp"
          style={{ marginBottom: "0px" }}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The unique selling point of this offer.
        </Text>
        <br />
        <br />
        <UploadImage
          name="offerImage"
          label="Offer Image"
          setImage={setOfferImage}
          required={false}
          url={offerImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This image will be displayed as a product image on the checkout page
          based on your offer configuration.
        </Text>
        <br />
        <br />
        <UploadImage
          name="cardImage"
          label="Card Image"
          setImage={setCardImage}
          required={false}
          url={cardImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This image will be displayed as a product image on the customer portal
          when customers view their purchases.
        </Text>
        <br />
        <br />
        <UploadImage
          name="offerBannerImage"
          label="Banner Image"
          setImage={setOfferBannerImage}
          url={offerBannerImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This image will be displayed as the banner image on the customer
          portal when customers view the details of their purchases.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Currency"
          name="currency"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          For which currency is this offer created?
        </Text>
        <br />
        <br />
        <Form.Item
          label="Redirect URL"
          name="redirectUrl"
          style={{ marginBottom: "0px" }}
          rules={[
            ...rules,
            { type: "url", message: "Please enter a valid URL" },
          ]}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The customer will be redirected to this URL after the purchase.
        </Text>
        <Form.Item
          label="Checkout page slug"
          name="slug"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Slug refers to the end part of the checkout URL
        </Text>
        <Divider />
        <Title level={5}>Fiscality</Title>
        <Form.Item
          label="Billing Entity"
          name="fiscalEntity"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "PI", label: "Paradox Institute" },
              { value: "PG", label: "Paradox Group" },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          By selecting this billing entity, all invoices will be generated by
          it.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Payment Gateway"
          name="paymentGateway"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={getPaymentGateways().map((gateway) => ({
              value: gateway.value,
              label: gateway.label,
            }))}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          By selecting a payment gateway, all payments will go through it.
        </Text>
        <Divider />
        <Title level={5}>Checkout Design</Title>
        <div
          className="paradox-card-white"
          style={{
            borderRadius: "10px",
          }}
        >
          <Row>
            <Col span={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <InfoCircleTwoTone style={{ fontSize: 25 }} />
              </div>
            </Col>
            <Col span={22} offset={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {`After the offer's creation, you can edit all elements
                        from this checkout template.`}
              </div>
            </Col>
          </Row>
        </div>
        <br />
        <Form.Item
          label="Checkout page template"
          name="template"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "A", label: "Template A" },
              { value: "B", label: "Template B" },
            ]}
            onChange={() => {}}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          What template do you want to use for this offer?
        </Text>
        <br />
        <br />
        <UploadImage
          name="bannerImage"
          label="Banner Image"
          setImage={setBannerImage}
          url={bannerImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Based on the offer configuration, this image will be display in the
          Checkout page.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Main color"
          name="mainColor"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <ColorPicker size="large" showText />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Will affect colors of buttons / clickable elements.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Cancellation policy"
          name="cancellationPolicy"
          style={{ marginBottom: "0px" }}
        >
          <Input.TextArea rows={4} maxLength={800} />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, next) =>
            prev.template !== next.template ||
            prev.showTestimonial !== next.showTestimonial
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("template") === "B" && (
              <>
                <br />
                <br />
                <Form.Item
                  name="showTestimonial"
                  label="Display the testimonial section"
                  valuePropName="checked"
                  style={{ marginBottom: "0px" }}
                >
                  <Checkbox>Show testimonial on the checkout page</Checkbox>
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  Toggle whether the testimonial block should be shown on the
                  checkout.
                </Text>
              </>
            )
          }
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The cancellation policy for this offer.
        </Text>
        <Form.Item
          noStyle
          shouldUpdate={(prev, next) =>
            prev.showTestimonial !== next.showTestimonial ||
            prev.template !== next.template
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("template") === "B" &&
            getFieldValue("showTestimonial") && (
              <>
                <Divider />
                <Title level={5}>Testimony</Title>
                <Form.Item
                  label="Testifier name"
                  name="testimonyName"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <Input placeholder="Testifier name" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The name of the person who is testifying.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testifier job"
                  name="testimonyTitle"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <Input placeholder="Testifier job" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The job of the person who is testifying.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testifier quote"
                  name="testimonyComment"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <Input.TextArea placeholder="Testifier quote" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The quote to display on the checkout page.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testifier rating"
                  name="testimonyRating"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <InputNumber placeholder="Testifier rating" min={5} max={5} />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The rating given by the person who is testifying.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testimony URL"
                  name="testimonyUrl"
                  style={{ marginBottom: "0px" }}
                  rules={[
                    ...rules,
                    { type: "url", message: "Please enter a valid URL" },
                  ]}
                >
                  <Input placeholder="Testimony URL" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The url for the video testimony.
                </Text>
              </>
            )
          }
        </Form.Item>
      </Form>
    </DrawerFormContainer>
  );
};

export default UpdateOfferForm;
