import { ApplyDiscountCodeParams } from "@/types";
import { requestChargebee } from ".";

/**
 * Applies a coupon by making a POST request to the "/api/coupon" endpoint.
 * @param payload - The payload containing the coupon string.
 * @returns A Promise that resolves to the response data from the server.
 */
export function applyCoupon(payload: ApplyDiscountCodeParams) {
  return requestChargebee("/api/coupon", "POST", payload);
}
