"use server";

import {
  ProductFamily,
  ProductFamilyListResponse,
  ProductFamilyParams,
} from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/product-families`;

/**
 * Creates a new product family.
 * @param params - The parameters for creating the product family.
 * @returns A Promise that resolves to the response JSON.
 */
export const createProductFamily = async (
  params: ProductFamilyParams
): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}`, "POST", params);

    return res;
  } catch (err) {
    console.error("charge result: ", err);
  }
};

/**
 * Returns a list of product families
 * @param page Page number
 * @param limit Number of families to fetch
 * @returns List of product families with total count and current page number
 */
export const getAllProductFamilies = async (
  page: number,
  limit: number,
  status?: string,
  search?: string
): Promise<ProductFamilyListResponse> => {
  let requestQuery = `${baseUrl}?limit=${limit}&page=${page}`;
  if (status != undefined) {
    requestQuery += `&status=${status}`;
  }
  if (search != undefined) {
    requestQuery += `&name=${search}`;
  }
  const res = await osRequest(requestQuery, "GET");

  return res;
};

/**
 * Retrieves a product family by its ID.
 * @param id - The ID of the product family.
 * @returns A Promise that resolves to the product family object, or undefined if the ID is not provided.
 */
export const getProductFamily = async (
  id?: number
): Promise<ProductFamily | undefined> => {
  if (!id) {
    return undefined;
  }
  let requestQuery = `${baseUrl}/${id}`;
  const res = await osRequest(requestQuery, "GET");

  return res;
};

/**
 * Retrieves product families by line.
 * @param line - The line parameter to filter the product families.
 * @returns A Promise that resolves to the product families.
 */
export const getProductFamiliesByLine = async (line?: string): Promise<any> => {
  if (!line) {
    return undefined;
  }
  let requestQuery = `${baseUrl}?line=${line}&status=active`;
  const res = await osRequest(requestQuery, "GET");

  return res;
};

/**
 * Updates a product family by sending a PATCH request to the server.
 * @param params - The parameters for updating the product family.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export const patchProductFamily = async (
  params: ProductFamilyParams
): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}/${params.id}`, "PATCH", params);

    return res;
  } catch (err) {
    console.error("charge result: ", err);
  }
};
