"use client";

import useSWR, { SWRConfiguration } from "swr";

import { fetchCustomers } from "@/lib/services/chargebee/customers";
import { fetchCustomerAccesses } from "@/lib/services/customers";
import { FetchCustomerAccessesParams, GetCustomersParams } from "@/types";

type UseFetchCustomersParams = {
  options?: SWRConfiguration;
  filters?: GetCustomersParams;
};

export const useFetchCustomerAccesses = (
  params?: FetchCustomerAccessesParams
) => {
  return useSWR<any>(`useFetchCustomerAccesses-${params?.email}`, () =>
    fetchCustomerAccesses(params?.email as string)
  );
};

export const useFetchCustomers = (params?: UseFetchCustomersParams) => {
  const key = ["useFetchCustomers", params?.filters?.searchQuery];
  return useSWR<any>(
    key,
    () => fetchCustomers(params?.filters as GetCustomersParams),
    params?.options
  );
};
