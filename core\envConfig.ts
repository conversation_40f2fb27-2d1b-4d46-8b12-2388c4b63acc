type EnvName = "development" | "staging" | "production";

type EnvConfig = {
  color: string;
  text: string;
};

const envConfigs: Record<EnvName, EnvConfig> = {
  development: {
    color: "yellow",
    text: "LOCAL SITE",
  },
  staging: {
    color: "orange",
    text: "TEST SITE",
  },
  production: {
    color: "green",
    text: "PRODUCTION SITE",
  },
};

/**
 * Computes the environment name based on the provided environment key.
 * If the environment key is not provided, it falls back to the value of `process.env.NODE_ENV`.
 * If the environment key is not recognized, it defaults to "development".
 *
 * @param envKey - The environment key.
 * @returns The computed environment name.
 */
function computeEnvName(envKey: string | undefined): EnvName {
  let selectedKey = envKey;
  if (!selectedKey) {
    selectedKey = process.env.NODE_ENV;
  }
  switch (envKey) {
    case "development":
    case "staging":
    case "production":
      return envKey;
    default:
      return "development";
  }
}

export const currentConfig =
  envConfigs[computeEnvName(process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT)];
