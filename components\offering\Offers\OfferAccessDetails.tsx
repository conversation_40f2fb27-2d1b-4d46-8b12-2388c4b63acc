"use client";

import { <PERSON>, Divider, Flex, Row, Tag, Typography } from "antd";

import { CommunityInfo, CourseInfo, Offer, SpaceInfo } from "@/types";

type OfferAccessDetailsProps = {
  offer: Offer;
  courses?: CourseInfo[];
  communities?: CommunityInfo[];
};

const { Text, Title } = Typography;

const OfferAccessDetails = (params: OfferAccessDetailsProps) => {
  return (
    <div style={{ width: "100%" }}>
      <div style={{ width: "100%" }}>
        <Title level={5}>Learnworld (courses)</Title>
        {params.courses && params.courses.length > 0 ? (
          <Flex gap="4px 0" wrap>
            {params.courses &&
              params.courses.map((lmsId, index) => {
                return (
                  <Tag style={{ fontSize: 11 }} key={index}>
                    {lmsId.name}
                  </Tag>
                );
              })}
          </Flex>
        ) : (
          <small style={{ fontSize: 13, color: "darkgray" }}>
            No courses attached to this offer
          </small>
        )}
      </div>
      <Divider />
      <div style={{ width: "100%" }}>
        <Title level={5}>Circle (communities & spaces)</Title>
        <Row>
          <Col span={8}>
            {params.communities && params.communities.length > 0 ? (
              <Flex gap="4px 0" wrap>
                {params.communities &&
                  params.communities.flat().map((community, index) => {
                    return (
                      <Tag style={{ fontSize: 11 }} key={index}>
                        {community.community}
                      </Tag>
                    );
                  })}
              </Flex>
            ) : (
              <small style={{ fontSize: 13, color: "darkgray" }}>
                No communities attached to this offer
              </small>
            )}
          </Col>
          <Col span={16}>
            <Flex gap="4px 0" wrap>
              {params.communities &&
                params.communities.flat().map((community: CommunityInfo) => {
                  return community.spaces.map((space: SpaceInfo, index) => {
                    return (
                      <Tag style={{ fontSize: 11 }} key={index}>
                        {space.name}
                      </Tag>
                    );
                  });
                })}
            </Flex>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default OfferAccessDetails;
