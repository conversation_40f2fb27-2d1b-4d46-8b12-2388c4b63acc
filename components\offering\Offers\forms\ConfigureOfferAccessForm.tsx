"use client";

import {
  ArrowLeftOutlined,
  CloseOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { App, <PERSON><PERSON>, Divider, Drawer, Row, Space, Typography } from "antd";

import SuspensionRules from "./offerAccess/SuspensionRules";
import ConfigureOfferAccess from "./offerAccess/ConfigureOfferAccess";
import { useConfigureOfferAccessForm } from "@/hooks/offering/offers-settings";
import { ConfigureOfferAccessFormProps } from "@/types";
import { updateOfferAccesses } from "@/lib/services";

const { Text } = Typography;

const ConfigureOfferAccessForm = ({
  isModalOpen,
  handleCancel,
  offerId: offerId,
  refreshOffer,
  suspendable,
  delayPeriod,
  offerProducts,
  offerRecommendedProducts,
  currentAccesses,
}: ConfigureOfferAccessFormProps) => {
  const { message } = App.useApp();
  const {
    offerAccesses,
    removeSpace,
    selectCourse,
    selectSpace,
    selectCommunity,
    setOfferAccesses,
    updateCoursesList,
    updateCommunityList,
  } = useConfigureOfferAccessForm({
    currentAccesses,
    suspendable,
    delayPeriod,
    offerProducts,
    offerRecommendedProducts,
  });

  const submitValues = async (values: any) => {
    let accessUpdates = {
      suspendable: offerAccesses.suspensionRules,
      delayPeriod: offerAccesses.suspensionDelay,
      lmsIds: {
        products: offerAccesses.selectedCourses,
        recommended: offerAccesses.recommendedCourses,
      },
      communityIds: {
        products: offerAccesses.mainCommunityAccesses,
        recommended: offerAccesses.recommendedCommunityAccesses,
      },
    };
    try {
      let res = await updateOfferAccesses({
        id: offerId,
        ...accessUpdates,
      });
      if (res?.success) {
        message.open({
          type: "success",
          content: "Offer's accesses updated successfully",
        });
        handleCancel();
        refreshOffer();
      } else {
        message.open({
          type: "error",
          content: "Failed to update offer's accesses",
        });
      }
    } catch (error) {
      console.log("error", error);
      message.open({
        type: "error",
        content: "Failed to update offer's accesses",
      });
    }
  };

  return (
    <Drawer
      width={"100vw"}
      open={isModalOpen}
      onClose={() => {
        handleCancel();
      }}
      closeIcon={false}
      destroyOnClose={true}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          paddingBottom: "40px",
        }}
      >
        <div>
          <Row
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              paddingRight: "30px",
              paddingLeft: "30px",
            }}
          >
            <div
              style={{
                display: "flex",
                width: "60%",
                justifyContent: "start",
                alignItems: "center",
              }}
            >
              <Space>
                <Button
                  icon={<ArrowLeftOutlined style={{ color: "#5B5B5D" }} />}
                  onClick={() => {
                    handleCancel();
                  }}
                />
                <Text style={{ fontSize: 24 }}>
                  {`Configure offer's access`}
                </Text>
              </Space>
            </div>
            <Space>
              <Button
                icon={<CloseOutlined />}
                onClick={() => {
                  handleCancel();
                }}
              >
                <Text strong>Dismiss</Text>
              </Button>
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={submitValues}
              >
                <Text style={{ color: "white" }} strong>
                  Save
                </Text>
              </Button>
            </Space>
          </Row>
          <Divider />
          <div
            style={{
              width: "100vw",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <br />
            <br />
            <ConfigureOfferAccess
              type="product"
              courses={offerAccesses.coursesFromLW}
              spaceGroups={offerAccesses.spaceGroups}
              spaces={offerAccesses.spacesFromCircle}
              selectedCommunities={offerAccesses.selectedCommunities}
              selectedCourses={offerAccesses.selectedCourses}
              selectedSpaces={offerAccesses.selectedSpaces}
              offerProducts={offerProducts}
              onRemoveSpace={(value) => {
                removeSpace(value, false);
              }}
              onSelectCourse={(value) => {
                selectCourse(value, false);
              }}
              onSelectCommunity={(value) => {
                selectCommunity(value, false);
              }}
              onSelectSpace={(value) => {
                selectSpace(value, false);
              }}
              onUpdateCourseList={updateCoursesList}
              onUpdateCommunityList={updateCommunityList}
            />
            <br />
            <br />
            <ConfigureOfferAccess
              type="recommended"
              courses={offerAccesses.coursesFromLW}
              spaceGroups={offerAccesses.spaceGroups}
              spaces={offerAccesses.spacesFromCircle}
              selectedCommunities={offerAccesses.recommendedCommunities}
              selectedCourses={offerAccesses.recommendedCourses}
              selectedSpaces={offerAccesses.recommendedSpaces}
              offerProducts={offerRecommendedProducts}
              onRemoveSpace={(value) => {
                removeSpace(value, true);
              }}
              onSelectCourse={(value) => {
                selectCourse(value, true);
              }}
              onSelectCommunity={(value) => {
                selectCommunity(value, true);
              }}
              onSelectSpace={(value) => {
                selectSpace(value, true);
              }}
              onUpdateCourseList={updateCoursesList}
              onUpdateCommunityList={updateCommunityList}
            />
            <br />
            <br />
            <SuspensionRules
              onSetSuspensionRules={(value: boolean) => {
                setOfferAccesses((prev) => ({
                  ...prev,
                  suspensionRules: value,
                }));
              }}
              onSetSuspensionDelay={(value: number) => {
                setOfferAccesses((prev) => ({
                  ...prev,
                  suspensionDelay: value,
                }));
              }}
              data={{
                suspensionRules: offerAccesses.suspensionRules,
                suspensionDelay: offerAccesses.suspensionDelay,
              }}
            />
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default ConfigureOfferAccessForm;
