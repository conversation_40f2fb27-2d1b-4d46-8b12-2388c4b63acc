name: Deploy to Production

on:
  push:
    branches:
      - main

jobs:
  production-deploy:
    name: Deploy to EC2 Production
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Make .env file
        uses: SpicyPizza/create-envfile@v2.0
        with:
          envkey_API_URL: ${{ vars.API_URL_PRODUCTION }}
          envkey_NEXT_PUBLIC_CHARGEBEE_DOMAIN: ${{ vars.NEXT_PUBLIC_CHARGEBEE_DOMAIN_PRODUCTION }}
          envkey_NEXT_PUBLIC_CHECKOUT_BASEURL: ${{ vars.NEXT_PUBLIC_CHECKOUT_BASEURL_PRODUCTION}}
          envkey_CHARGEBEE_FA_API_KEY: ${{ secrets.CHARGEBEE_FA_API_KEY_PRODUCTION }}
          envkey_NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${{ secrets.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY_PRODUCTION }}
          envkey_CLERK_SECRET_KEY: ${{ secrets.CLERK_SECRET_KEY_PRODUCTION }}
          envkey_CLERK_ENCRYPTION_KEY: ${{ secrets.CLERK_ENCRYPTION_KEY_PRODUCTION}}
          envkey_NEXT_PUBLIC_CLERK_SIGN_IN_URL: /sign-in
          envkey_NEXT_PUBLIC_CLERK_SIGN_UP_URL: /sign-up
          envkey_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL: /dashboard
          envkey_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL: /dashboard
          envkey_AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          envkey_AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          envkey_AWS_BUCKET_NAME: paradox-os
          envkey_AWS_REGION: eu-west-3
          envkey_AWS_FOLDER_NAME: production
          envKey_NEXT_PUBLIC_AWS_BUCKET_PUBLIC_URL: https://paradox-os.s3.eu-west-3.amazonaws.com/production/
          envkey_NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT: production
          envkey_NEXT_PUBLIC_FEATURE_ENABLE_ROLES: true
          envkey_SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          envkey_LW_CLIENT_ID: ${{ secrets.LW_CLIENT_ID }}
          envkey_LW_CLIENT_SECRET: ${{ secrets.LW_CLIENT_SECRET }}
          envkey_NEXT_PUBLIC_LW_API_URL: https://paradox.getlearnworlds.com/admin/api/v2/
          envkey_NEXT_PUBLIC_LW_AUTH_URL: https://paradox.getlearnworlds.com/admin/api/oauth2/access_token
          envkey_CIRCLE_ACCESS_TOKEN_PXS: ${{ secrets.CIRCLE_ACCESS_TOKEN_PXS}}
          envkey_CIRCLE_ACCESS_TOKEN_PXL: ${{ secrets.CIRCLE_ACCESS_TOKEN_PXL}}
          envkey_NEXT_PUBLIC_CIRCLE_API_URL: https://app.circle.so/api/v1/
          envkey_NEXT_PUBLIC_PXL_COMMUNITY_ID: 113377
          envkey_NEXT_PUBLIC_PXS_COMMUNITY_ID: 130420
          envkey_NEXT_PUBLIC_SEGMENT_WRITE_KEY: ${{ secrets.NEXT_PUBLIC_SEGMENT_WRITE_KEY }}
          envkey_NODE_ENV: production
          directory: ./
          file_name: .env
          fail_on_empty: false
          sort_keys: false

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: 18.x

      - name: Build Docker image
        run: npm run docker:build

      - name: Save Docker image as TAR archive
        run: docker save -o ${{ vars.IMAGE_NAME }}.tar ${{ vars.IMAGE_NAME }}

      - name: Copy Docker image to EC2 instance
        uses: appleboy/scp-action@v0.1.3
        with:
          host: ${{ secrets.EC2_PRODUCTION_INSTANCE_IP }}
          username: ${{ secrets.EC2_PRODUCTION_USERNAME }}
          key: ${{ secrets.EC2_PRODUCTION_SSH_KEY }}
          passphrase: ${{ secrets.EC2_PRODUCTION_PASSPHRASE }}
          source: ${{ vars.IMAGE_NAME }}.tar
          target: /home/<USER>/docker-repository/

      - name: SSH into EC2 instance and load Docker image
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_PRODUCTION_INSTANCE_IP }}
          username: ${{ secrets.EC2_PRODUCTION_USERNAME }}
          key: ${{ secrets.EC2_PRODUCTION_SSH_KEY }}
          passphrase: ${{ secrets.EC2_PRODUCTION_PASSPHRASE }}
          script: |
            docker stop ${{ vars.CONTAINER_NAME }} || true
            docker rm ${{ vars.CONTAINER_NAME }} || true
            docker rmi -f ${{ vars.IMAGE_NAME }} || true
            docker load -i /home/<USER>/docker-repository/${{ vars.IMAGE_NAME }}.tar
            docker run -d --name ${{ vars.CONTAINER_NAME }} --network ${{ secrets.PRODUCTION_DOCKER_NETWORK }} --ip ${{ secrets.PRODUCTION_CONTAINER_IP }} -p 3001:3001 ${{ vars.IMAGE_NAME }}
            docker update --restart=always ${{ vars.CONTAINER_NAME }}
  slack-notification:
    name: Slack Notification
    runs-on: ubuntu-latest
    if: ${{ always() && contains(join(needs.*.result, ','), 'success') }}
    needs: [production-deploy]
    steps:
      - name: Send notification
        uses: rtCamp/action-slack-notify@v2.3.0
        env:
          SLACK_CHANNEL: notifications-techteam
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://avatars.githubusercontent.com/u/66640518?s=200&v=4
          SLACK_TITLE: 🚀 Production Deployment Report
          SLACK_USERNAME: Paradox OS
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_FOOTER: ${{ github.repository }}
          SLACK_ON_FAILURE: Production deployment failed for PR ${{ github.ref }}
