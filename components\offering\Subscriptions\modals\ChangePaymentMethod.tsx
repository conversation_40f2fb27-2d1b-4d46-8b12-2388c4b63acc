"use client";

import { InfoCircleTwoTone, SyncOutlined } from "@ant-design/icons";
import {
  App,
  Card,
  CheckboxProps,
  Col,
  Divider,
  Modal,
  Radio,
  Row,
  Space,
  Typography,
} from "antd";
import { useEffect, useState } from "react";

import { useUser } from "@/hooks/utils/useUser";
import { changeDefaultPaymentMethod } from "@/lib/services/chargebee";

const { Text } = Typography;

type ChangePaymentMethodModalProps = {
  isOpen: boolean;
  defaultValue: string;
  subscriptionId: string;
  handleClose: () => void;
  customerAutoCollect: string;
};

type Choice = "online" | "offline";

export default function ChangePaymentMethodModal({
  isOpen,
  defaultValue,
  subscriptionId,
  handleClose,
  customerAutoCollect,
}: ChangePaymentMethodModalProps) {
  const { isSignedIn, emailAddress } = useUser();
  const { message } = App.useApp();
  const [choice, setChoice] = useState<Choice | null>(null);

  const onChange: CheckboxProps["onChange"] = (e) => {
    setChoice(e.target.value);
  };

  useEffect(() => {
    const isSet = defaultValue === "on" || defaultValue === "off";
    if (isSet) {
      setChoice(defaultValue === "off" ? "offline" : "online");
    }
  }, [defaultValue]);

  return (
    <Modal
      open={isOpen}
      closeIcon={null}
      width="40%"
      cancelText="Dismiss"
      cancelButtonProps={{ type: "link" }}
      okText="Update payment method"
      okButtonProps={{
        danger: true,
        icon: <SyncOutlined />,
        disabled: !choice,
      }}
      onCancel={() => {
        handleClose();
      }}
      onOk={async () => {
        let response;
        if (!isSignedIn) {
          window.location.href = "/";
          return;
        }
        try {
          response = await changeDefaultPaymentMethod({
            id: subscriptionId,
            payment_method: choice as Choice,
            user_email: emailAddress as string,
          });
        } catch (error) {
          message.open({
            type: "error",
            content: "Error changing payment method",
          });
        } finally {
          handleClose();
        }
      }}
    >
      <Text strong>Change payment method for this subscription</Text>
      <Divider />
      <Card
        bodyStyle={{
          padding: "12px",
          boxShadow: "5px 8px 24px 5px rgba(255, 255, 255, 0.6)",
        }}
      >
        <Row>
          <Col span={1}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <InfoCircleTwoTone style={{ fontSize: 24 }} />
            </div>
          </Col>
          <Col span={22}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              Only the payment method for this specific subscription will be
              changed
            </div>
          </Col>
        </Row>
      </Card>
      <Radio.Group onChange={onChange} value={choice} style={{ marginTop: 20 }}>
        <Space direction="vertical">
          <Radio value="online" disabled={customerAutoCollect === "off"}>
            Online payment
          </Radio>
          <Text>
            {`Whenever an invoice is generated, the customer's payment method will
            be charged automatically`}
          </Text>
          <Radio value="offline">Offline payment (bank transfers)</Radio>
          <Text>
            {`When an invoice is generated, we will collect & record payment via
            bank transfer`}
          </Text>
        </Space>
      </Radio.Group>
    </Modal>
  );
}
