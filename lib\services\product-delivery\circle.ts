"use server";

const baseURL = process.env.NEXT_PUBLIC_CIRCLE_API_URL;
const apiKeyPXL = process.env.CIRCLE_ACCESS_TOKEN_PXL;
const apiKeyPXS = process.env.CIRCLE_ACCESS_TOKEN_PXS;

/**
 * Makes an HTTP request to the Circle API with appropriate authentication.
 *
 * @param url - The endpoint path to be appended to the base URL
 * @param method - The HTTP method to use (GET, POST, PUT, DELETE, etc.)
 * @param community - The community identifier ("PXS" or "PXL") to determine which API key to use
 * @param body - Optional request body data to be sent with non-GET requests
 * @returns {Promise<any|undefined>} The parsed JSON response if successful, undefined if the request fails
 *
 * @description
 * This function handles API requests to Circle, automatically:
 * - Adding the correct authorization header based on the community
 * - Converting the body to JSON for non-GET requests
 * - Parsing the JSON response
 */
const requestCircle = async (
  url: string,
  method: string,
  community: string,
  body?: any
) => {
  let requestInit: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${community === "PXS" ? apiKeyPXS : apiKeyPXL}`,
    },
  };
  if (method !== "GET" && body) {
    requestInit.body = JSON.stringify(body);
  }
  const res = await fetch(`${baseURL}${url}`, requestInit);
  if (res.ok) {
    return res.json();
  }
  return undefined;
};

/**
 * Retrieves information about communities, space groups, and spaces from Circle API
 * for both PXL and PXS communities.
 *
 * @param forceUpdate - If true, bypasses cache and fetches fresh data from the API
 * @returns {Promise<{
 *   communities: any[],
 *   spaceGroups: any[],
 *   spaces: any[]
 * }>} Object containing arrays of communities, space groups, and spaces
 *
 * @description
 * This function first checks the global cache for existing data unless forceUpdate is true.
 * If data is not cached or forceUpdate is true, it fetches:
 * - Communities from both PXL and PXS
 * - Space groups for each community
 * - Spaces for each community
 * The results are deduplicated using their IDs and stored in the global cache.
 */
export const getCommunityInfo = async (forceUpdate?: boolean): Promise<any> => {
  if (!forceUpdate) {
    if (
      global.communityCache.has("communities") &&
      global.communityCache.has("spaceGroups") &&
      global.communityCache.has("spaces")
    ) {
      const communities = global.communityCache.get("communities");
      const spaceGroups = global.communityCache.get("spaceGroups");
      const spaces = global.communityCache.get("spaces");
      return { communities, spaceGroups, spaces };
    }
  }
  const PXLCommunities = await requestCircle("/communities", "GET", "PXL");
  const PXLSpaceGroups = await requestCircle(
    `/space_groups?community_id=${PXLCommunities[0].id}&per_page=100`,
    "GET",
    "PXL"
  );
  const PXLSpaces = await requestCircle(
    `/spaces?community_id=${PXLCommunities[0].id}&per_page=100`,
    "GET",
    "PXL"
  );
  const PXSCommunities = await requestCircle("/communities", "GET", "PXS");
  const PXSSpaceGroups = await requestCircle(
    `/space_groups?community_id=${PXSCommunities[0].id}&per_page=100`,
    "GET",
    "PXS"
  );
  const PXSSpaces = await requestCircle(
    `/spaces?community_id=${PXSCommunities[0].id}&per_page=100`,
    "GET",
    "PXS"
  );
  let communities = [...PXLCommunities, ...PXSCommunities];
  communities = [...new Map(communities.map((c) => [c.id, c])).values()];
  let spaceGroups = [...PXLSpaceGroups, ...PXSSpaceGroups];
  spaceGroups = [...new Map(spaceGroups.map((c) => [c.id, c])).values()];
  let spaces = [...PXLSpaces, ...PXSSpaces];
  spaces = [...new Map(spaces.map((c) => [c.id, c])).values()];

  if (communities && communities.length > 0) {
    global.communityCache.set("communities", communities);
  }
  if (spaceGroups && spaceGroups.length > 0) {
    global.communityCache.set("spaceGroups", spaceGroups);
  }
  if (spaces && spaces.length > 0) {
    global.communityCache.set("spaces", spaces);
  }
  return { communities, spaceGroups, spaces };
};

/**
 * Retrieves access information for a user from Circle API
 * for both PXL and PXS communities.
 *
 * @param email - The email address of the user to check access for
 * @param community - The community identifier ("PXL" or "PXS")
 * @param page - The page number to fetch (default is 1)
 * @returns {Promise<{
 *   ...data,
 *   records: Record<string, any[]>
 * }>} Object containing the API response data with records grouped by space_group_id
 */
export const getComunityMemberSpacesByEmail = async (
  email: string,
  community: string,
  page: number = 1
) => {
  if (!email || !community) {
    return {};
  }
  const url = `/community_member_spaces?user_email=${email}&page=${page}`;
  const data = await requestCircle(url, "GET", community.toUpperCase());
  if (!data?.records) {
    return {};
  }
  // group by space_group_id
  const groupedData = data.records.reduce((acc: any, item: any) => {
    acc[item.space_group_id] = acc[item.space_group_id] || [];
    acc[item.space_group_id].push(item);
    return acc;
  }, {});

  return { ...data, records: groupedData };
};

/**
 * Retrieves access information for a user from Circle API
 * for both PXL and PXS communities.
 *
 * @param email - The email address of the user to check access for
 * @returns {Promise<{
 *   pxl: any[],
 *   pxs: any[]
 * }>} Object containing arrays of access data for PXL and PXS
 */
export const getComunityMemberSpacesAccesses = async (email: string) => {
  if (!email) {
    return { pxl: [], pxs: [] };
  }
  const [pxl, pxs] = await Promise.all([
    getComunityMemberSpacesByEmail(email, "PXL", 1),
    getComunityMemberSpacesByEmail(email, "PXS", 1),
  ]);
  return { pxl, pxs };
};
