"use client";

import { Form, Input, Select, Typography } from "antd";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { ProductLine } from "@/types";

type UpdateProps = {
  isModalOpen: boolean;
  formValues: ProductLine | undefined;
  handleUpdate: Function;
  handleCancel: Function;
};

const { Text, Title } = Typography;

const UpdateProductLineForm = ({
  isModalOpen,
  formValues,
  handleUpdate,
  handleCancel,
}: UpdateProps) => {
  const [form] = Form.useForm();
  form.setFieldsValue(formValues);
  const rules = [{ required: true, message: "This value is required!" }];

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      onCancel={() => handleCancel()}
      onClose={() => handleCancel()}
      onSubmit={() => {
        form.submit();
      }}
      submitText="Update"
      title="Update product line"
    >
      <Title level={5}>Product line settings</Title>
      <Form
        form={form}
        onFinish={(values) => {
          handleUpdate(values);
        }}
        layout="vertical"
        style={{ maxWidth: 800 }}
      >
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          label="Product line name"
          name="name"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="Product line name" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this product line internally.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product line description"
          name="description"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="EDEC" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product line description attached to this product.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Status"
          name="status"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            style={{ width: "200px" }}
            options={[
              { value: "active", label: "Active" },
              { value: "draft", label: "Draft" },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Changing a product line to ‘Draft’ status will not affect products &
          offers. The draft product line will not be usable in product creation.
        </Text>
        <br />
        <br />
        <br />
      </Form>
    </DrawerFormContainer>
  );
};

export default UpdateProductLineForm;
