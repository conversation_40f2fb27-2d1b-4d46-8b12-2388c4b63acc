"use client";

import { Col, Row } from "antd";
import { useEffect } from "react";

import StepperFormsContainer from "./StepperFormsContainer";
import EstimateSubscription from "./EstimateSubscription";
import FormActions from "./FormActions";
import useManualSubscriptionWorkflow from "./ManualSubscriptionWorkflow.hook";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { ManualSubscriptionWorkflowProps, Offer } from "@/types";

const totalSteps = 3;

const ManualSubscriptionWorkflow = ({
  isModalOpen,
  handleAdd,
  handleCancel,
}: ManualSubscriptionWorkflowProps) => {
  const {
    bc,
    billingAddress,
    coupons,
    current,
    customerData,
    dealID,
    discounts,
    offer,
    products,
    shippingAddress,
    steps,
    startDate,
    handleProductQtyChange,
    next,
    prev,
    resetCurrent,
    setError,
  } = useManualSubscriptionWorkflow({
    handleSubmit: async () => {
      // Reset error
      setError("");
      // Call the handleAdd function (calling /api/subscriptions/create)
      const result: any = await handleAdd({
        billingCycle: bc,
        billingInfo: billingAddress,
        customerData,
        coupons,
        dealId: dealID as string,
        discounts,
        offer: offer as Offer,
        products,
        shippingInfo: shippingAddress,
        startDate: startDate as string,
      });
      console.log("ℹ️ handleSubmit", { result });
      if (result?.details) {
        setError(result?.details.message || "");
      }
    },
    handleCancel,
  });

  const canAccessNext = (): boolean => {
    if (current === 0) {
      return !!offer && !!bc && !!products.length;
    } else if (current === 1) {
      return (
        dealID &&
        customerData.email &&
        customerData.first_name &&
        customerData.last_name &&
        customerData.country
      );
    } else if (current === 2) {
      return !!(
        billingAddress &&
        billingAddress.first_name &&
        billingAddress.last_name &&
        billingAddress.email &&
        billingAddress.line1 &&
        billingAddress.city &&
        billingAddress.zip &&
        billingAddress.country
      );
    }
    return true;
  };

  useEffect(() => {
    return () => {
      if (isModalOpen) {
        resetCurrent();
      }
    };
  }, [isModalOpen]);

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      submitText="Create subscription"
      title="Create manual subscription"
      width="100%"
      onCancel={handleCancel}
      onClose={handleCancel}
    >
      <Row
        style={{
          width: "95vw",
          margin: "auto",
          display: "flex",
          flexDirection: "row",
          alignItems: "flex-start",
        }}
        gutter={[16, 16]}
      >
        <Col span={15}>
          <StepperFormsContainer currentIdx={current} steps={steps} />
        </Col>
        <Col offset={1} span={8}>
          <EstimateSubscription
            billingCycle={bc}
            customerData={customerData}
            coupons={coupons}
            discounts={discounts}
            offer={offer}
            products={products}
            handleProductQtyChange={handleProductQtyChange}
          />
        </Col>
      </Row>

      <Row
        style={{
          width: "95vw",
          margin: "auto",
          display: "flex",
          flexDirection: "row",
          alignItems: "flex-start",
        }}
        gutter={[0, 8]}
      >
        <FormActions
          next={next}
          prev={prev}
          current={current}
          total={totalSteps}
          canAccessNext={canAccessNext}
        />
      </Row>
    </DrawerFormContainer>
  );
};

export default ManualSubscriptionWorkflow;
