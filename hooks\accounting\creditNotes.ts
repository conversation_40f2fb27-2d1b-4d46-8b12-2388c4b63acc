"use client";

import useSWR, { SWRConfiguration } from "swr";

import { getCreditNote, getCreditNotes } from "@/lib/services";
import {
  CreditNote,
  CreditNoteListResponse,
  CreditNotesFilter,
  GetCreditNotesParams,
} from "@/types";

type UseFetchCreditNotesParams = {
  options?: SWRConfiguration;
  filters?: CreditNotesFilter;
};

function prepareFetchCreditNotes(filters?: CreditNotesFilter) {
  let options: GetCreditNotesParams = {};
  if (filters) {
    if (filters.customerId) options.customerId = filters.customerId as string;
    if (filters.fiscalEntityFilter)
      options.entity = filters.fiscalEntityFilter as string;
    if (filters.searchTerm) options.searchQuery = filters.searchTerm as string;
    if (filters.subscriptionId)
      options.subscriptionId = filters.subscriptionId as string;

    if (filters.statusFilter) options.status = filters.statusFilter as string;
  }
  return options;
}

export const useFetchCreditNotes = (params?: UseFetchCreditNotesParams) => {
  const options: GetCreditNotesParams = prepareFetchCreditNotes(
    params?.filters
  );

  return useSWR<CreditNoteListResponse>(
    `useFetchCreditNotes-${JSON.stringify(options)}`,
    () => getCreditNotes(options),
    params?.options
  );
};

export const useFetchCreditNote = (id: string) => {
  return useSWR<CreditNote>(`useFetchCreditNote-${id}`, () =>
    getCreditNote(id)
  );
};
