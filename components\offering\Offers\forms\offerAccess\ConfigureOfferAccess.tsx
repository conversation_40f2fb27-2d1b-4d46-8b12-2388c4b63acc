"use client";

import { Col, Row, Typography } from "antd";

import SelectCoursesModal from "./modals/SelectCoursesModal";
import SelectCoursesCard from "./cards/SelectCoursesCard";
import UpdateExternalEntitiesList from "./UpdateExternalEntitiesList";
import SelectedCommunitesAndSpaces from "./cards/SelectedCommunitesAndSpaces";
import SelectCommunitiesAndSpacesModal from "./modals/SelectCommunitiesAndSpacesModal";
import { useConfigureOfferAccess } from "@/hooks/offering/offers-settings";
import { ConfigureOfferAccessProps } from "@/types";

const { Text } = Typography;

export default function ConfigureOfferAccess({
  type,
  courses,
  spaceGroups,
  spaces,
  selectedCommunities,
  selectedCourses,
  selectedSpaces,
  offerProducts,
  onRemoveSpace,
  onSelectCourse,
  onSelectCommunity,
  onSelectSpace,
  onUpdateCourseList,
  onUpdateCommunityList,
}: ConfigureOfferAccessProps) {
  const { modals, setModals } = useConfigureOfferAccess({
    spaceGroups,
    spaces,
    offerProducts,
  });

  return (
    <>
      <div className="paradox-card" style={{ width: "80%", padding: "10px" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: "10px",
          }}
        >
          <Text strong>
            Configure the access for{" "}
            {type === "product" ? " main " : " recommended "}
            product(s) in the offer
          </Text>
          <UpdateExternalEntitiesList
            onUpdateCourseList={onUpdateCourseList}
            onUpdateCommunityList={onUpdateCommunityList}
          />
        </div>

        <Row style={{ width: "100%" }}>
          <Col span={12}>
            <SelectCoursesCard
              selectedCourses={selectedCourses}
              onOpenModal={() => setModals({ ...modals, addingCourse: true })}
            />
          </Col>
          <Col span={11} offset={1}>
            <SelectedCommunitesAndSpaces
              selectedCommunities={selectedCommunities}
              selectedSpaces={selectedSpaces}
              onOpenModal={() =>
                setModals({ ...modals, setCommunitiesAndCourses: true })
              }
            />
          </Col>
        </Row>
      </div>
      {/* Modals */}
      {modals.addingCourse ? (
        <SelectCoursesModal
          isOpen={modals.addingCourse}
          onClose={() => setModals({ ...modals, addingCourse: false })}
          courses={courses}
          selectedCourses={selectedCourses}
          onSelectCourse={onSelectCourse}
        />
      ) : null}
      {modals.setCommunitiesAndCourses ? (
        <SelectCommunitiesAndSpacesModal
          communities={selectedCommunities}
          isOpen={modals.setCommunitiesAndCourses}
          spaces={spaces}
          spaceGroups={spaceGroups}
          onClose={() =>
            setModals({ ...modals, setCommunitiesAndCourses: false })
          }
          selectedSpaces={selectedSpaces}
          onSelectCommunity={onSelectCommunity}
          onSelectSpace={onSelectSpace}
          onRemoveSpace={onRemoveSpace}
        />
      ) : null}
    </>
  );
}
