"use server";

import { Transaction, TransactionListResponse } from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/transactions`;

type GetAllTransactionsParams = {
  searchQuery?: string;
  status?: string;
  paymentMethod?: string;
  subscriptionId?: string;
  customerId?: string;
};

/**
 * Retrieves all transactions based on the provided parameters.
 * @param params - Optional parameters for filtering the transactions.
 * @returns A Promise that resolves to the JSON response containing the transactions.
 */
export const getAllTransactions = async (
  params?: GetAllTransactionsParams
): Promise<TransactionListResponse> => {
  let requestQuery = new URL(`${baseUrl}?`);
  const query = new URLSearchParams();
  if (params?.searchQuery) query.append("query", params.searchQuery);
  if (params?.status) query.append("status", params.status);
  if (params?.paymentMethod)
    query.append("paymentMethod", params.paymentMethod);
  if (params?.customerId) query.append("customerId", params.customerId);
  if (params?.subscriptionId)
    query.append("subscriptionId", params.subscriptionId);
  query.append("limit", "1000");
  query.append("page", "1");
  query.append("orderBy", "DESC");
  requestQuery.search = query.toString();
  const res = await osRequest(requestQuery.href, "GET");

  return res;
};

/**
 * Get a specific transaction
 * @param id ID of the transaction
 * @returns Transaction object
 */
export const getTransaction = async (id: string): Promise<Transaction> => {
  const res = await osRequest(`${baseUrl}/${id}`, "GET");

  return res;
};
