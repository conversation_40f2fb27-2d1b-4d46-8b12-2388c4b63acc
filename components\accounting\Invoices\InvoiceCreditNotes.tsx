"use client";

import { Descriptions, Tag, Typography } from "antd";
import { useRouter } from "next/navigation";
import { Invoice as ChargebeeInvoice } from "chargebee";

import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { Invoice } from "@/types";

type InvoiceIssuedCreditNote = ChargebeeInvoice.IssuedCreditNote;
type InvoiceAdjustmentCreditNote = ChargebeeInvoice.AdjustmentCreditNote;

const { Text } = Typography;

type InvoiceCreditNotesProps = {
  invoice: Invoice;
};

const InvoiceCreditNotes = ({ invoice }: InvoiceCreditNotesProps) => {
  const router = useRouter();
  return (
    <Descriptions title="Credit notes" column={1} bordered size="small">
      {invoice.issuedCreditNotes &&
        invoice.adjustmentCreditNotes &&
        [...invoice.issuedCreditNotes, ...invoice.adjustmentCreditNotes].map(
          (record: InvoiceIssuedCreditNote | InvoiceAdjustmentCreditNote) => {
            return (
              <Descriptions.Item
                key={record?.cn_id}
                label={
                  <>
                    <a
                      onClick={() => {
                        router.push(`/dashboard/credit-notes/${record?.cn_id}`);
                      }}
                    >
                      {record?.cn_id}
                    </a>
                    <Tag
                      color={
                        record?.cn_status === "refunded"
                          ? "red"
                          : record?.cn_status === "adjusted"
                            ? "orange"
                            : "gray"
                      }
                    >
                      {capitalizeWord(record?.cn_status)}
                    </Tag>
                  </>
                }
              >
                <Text>
                  <Text strong>
                    {record?.cn_total && formatCents(record?.cn_total)}
                  </Text>
                  &nbsp;
                  <Text>
                    {record?.cn_status === "refunded"
                      ? "issued"
                      : record?.cn_status}
                  </Text>
                  &nbsp;due to reason&nbsp;
                  <Text strong>{record?.cn_create_reason_code}</Text>
                  &nbsp;on {formatDateFromUnix(record?.cn_date)}
                </Text>
              </Descriptions.Item>
            );
          }
        )}
      {invoice?.issuedCreditNotes?.length === 0 &&
        invoice?.adjustmentCreditNotes?.length === 0 && (
          <Descriptions.Item>
            <Text style={{ color: "gray" }}>
              No credit note attached to this invoice
            </Text>
          </Descriptions.Item>
        )}
    </Descriptions>
  );
};

export default InvoiceCreditNotes;
