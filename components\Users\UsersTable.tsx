"use client";

import {
  Avatar,
  Col,
  Divider,
  Form,
  Input,
  Row,
  Select,
  Skeleton,
  Table,
  Tag,
  Typography,
} from "antd";
import {
  CalendarOutlined,
  ClockCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Suspense, useState, useMemo, useCallback } from "react";

import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchUsers } from "@/hooks/users";
import { useProtectPage } from "@/hooks/roles";
import { useUser } from "@/hooks/utils/useUser";
import { Role, User, UsersFilters } from "@/types";
import { getRoleColor, getRoleLabel, roles } from "@/lib/roles";
import { updateUserRole } from "@/lib/services/clerk";

const initialFilters = {
  searchQuery: "",
  role: "",
};

const { Text, Title } = Typography;

const UsersTable = () => {
  const { user: currentUser } = useUser();
  const { canAccess } = useProtectPage("employees-list");

  const [filters, setFilters] = useState<UsersFilters>(initialFilters);
  const debouncedText = useDebounce(filters.searchQuery, 500);

  const queryFilters = useMemo(
    () => ({
      ...filters,
      searchQuery: debouncedText,
    }),
    [filters, debouncedText]
  );

  const {
    data: users,
    isLoading,
    mutate: refetchUsers,
  } = useFetchUsers({ filters: queryFilters });

  const [filterForm] = Form.useForm();

  const handleRoleChanged = useCallback(
    async (role: string, user: User) => {
      if (!canAccess("employee-set-role")) return;

      try {
        await updateUserRole({ role, user_id: user.id });
        await refetchUsers();
      } catch (error) {
        console.error("Failed to update user role:", error);
      }
    },
    [canAccess, updateUserRole]
  );

  const columns = useMemo(
    () => [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        render: (_: any, record: User) => <Text copyable>{record.id}</Text>,
      },
      {
        title: "Email",
        key: "email",
        render: (_: any, record: User) => (
          <Row align="middle">
            <Avatar
              src={record.imageUrl}
              size={24}
              style={{ marginRight: 10 }}
            />
            <Text copyable>{record.email}</Text>
          </Row>
        ),
        sorter: (a: User, b: User) => a.email.localeCompare(b.email),
      },
      {
        title: "Full name",
        key: "firstName",
        render: (_: any, record: User) => (
          <Row align="middle">
            <Text copyable>
              {record.firstName} {record.lastName}
            </Text>
          </Row>
        ),
        sorter: (a: User, b: User) =>
          String(a.firstName).localeCompare(String(b?.firstName)),
      },
      {
        title: "Role",
        key: "role",
        render: (_: any, record: User) =>
          currentUser?.publicMetadata?.role === "admin" ? (
            <Select
              disabled={isLoading}
              options={roles}
              placeholder="Role"
              value={record.role as Role["value"]}
              onChange={(v) => handleRoleChanged(v, record)}
              style={{ width: "100%" }}
            ></Select>
          ) : (
            <Text>
              <Tag color={getRoleColor(record.role as Role["value"])}>
                {getRoleLabel(record.role as Role["value"])}
              </Tag>
            </Text>
          ),
        sorter: (a: User, b: User) =>
          String(a.role).localeCompare(String(b?.role)),
      },
      {
        title: (
          <>
            <ClockCircleOutlined />
            &nbsp; Last login
          </>
        ),
        key: "lastLogin",
        render: (_: any, record: User) => (
          <Text>
            {record.lastSignInAt
              ? new Date(Number(record.lastSignInAt)).toLocaleString()
              : ""}
          </Text>
        ),
        sorter: (a: User, b: User) =>
          Number(a.lastSignInAt) - Number(b.lastSignInAt),
      },
      {
        title: (
          <>
            <CalendarOutlined />
            &nbsp; Created at
          </>
        ),
        key: "createdAt",
        render: (_: any, record: User) => (
          <Text>{new Date(Number(record.createdAt)).toLocaleString()}</Text>
        ),
      },
    ],
    [currentUser?.publicMetadata?.role, handleRoleChanged, isLoading]
  );

  return (
    <Suspense fallback={<Skeleton />}>
      <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
        <Title level={3}>Users</Title>
        <Row style={{ lineHeight: "10px" }}>
          <Col span={24}>
            <Form
              form={filterForm}
              onValuesChange={() => setFilters(filterForm.getFieldsValue())}
            >
              <Form.Item name="searchQuery">
                <Input
                  prefix={<SearchOutlined />}
                  placeholder="Search by firstname, lastname, email or user_id"
                />
              </Form.Item>
            </Form>
          </Col>
        </Row>
        <Divider />
        <Table
          dataSource={users?.data}
          loading={isLoading}
          columns={columns}
          size="small"
          pagination={{
            showSizeChanger: false,
            total: users?.total,
            showTotal: (total: number, range: number[]) => {
              return (
                <span>
                  Showing {range[0]} - {range[1]} / {total}
                </span>
              );
            },
          }}
          rowClassName={() => {
            return "paradox-table-row";
          }}
          rowKey="id"
        />
      </div>
    </Suspense>
  );
};

export default UsersTable;
