import { PXActionResult } from '@px-shared-account/hermes';

// Temporary interfaces until Her<PERSON> is built with new types
export interface IGetLearnworldsCoursesParams {
  forceRefresh?: boolean;
  page?: number;
  limit?: number;
}

export interface ILearnworldsCoursesResponse extends PXActionResult {
  data?: any[];
}

export interface ILearnworldsUserResponse extends PXActionResult {
  data?: any;
}

export interface ILearnworldsEnrollmentParams {
  email: string;
  customerFirstName: string;
  courseId: string;
  comment?: string;
  notifyUser?: boolean;
}

export interface ILearnworldsUnenrollmentParams {
  email: string;
  courseId: string;
}

export interface ILearnworldsUserTagsParams {
  email: string;
  tags: string[];
}

export interface ILearnworldsUserSuspensionParams {
  email: string;
}

export interface ICreateLearnworldsUserParams {
  email: string;
  customerFirstName: string;
}

export interface IGetLearnworldsUserParams {
  email: string;
}

/**
 * Core API endpoints for Learnworlds operations
 * These are the base paths and methods used by both client and server implementations
 */
export const LEARNWORLDS_API_ENDPOINTS = {
  /**
   * Get courses endpoint
   * @param params Query parameters for pagination and filtering
   * @returns Endpoint configuration
   */
  getCourses: (params: IGetLearnworldsCoursesParams = {}) => {
    const { forceRefresh, page, limit } = params;
    const queryParams = new URLSearchParams();

    if (forceRefresh !== undefined) {
      queryParams.append('forceRefresh', forceRefresh.toString());
    }
    if (page !== undefined) {
      queryParams.append('page', page.toString());
    }
    if (limit !== undefined) {
      queryParams.append('limit', limit.toString());
    }

    const queryString = queryParams.toString();
    return {
      url: `/learnworlds/courses${queryString ? `?${queryString}` : ''}`,
      method: 'GET',
    };
  },

  /**
   * Enroll user endpoint
   * @returns Endpoint configuration
   */
  enrollUser: () => ({
    url: '/learnworlds/enroll',
    method: 'POST',
  }),

  /**
   * Unenroll user endpoint
   * @returns Endpoint configuration
   */
  unenrollUser: () => ({
    url: '/learnworlds/unenroll',
    method: 'DELETE',
  }),

  /**
   * Add tags to user endpoint
   * @returns Endpoint configuration
   */
  addTagsToUser: () => ({
    url: '/learnworlds/user/tags/add',
    method: 'POST',
  }),

  /**
   * Remove tags from user endpoint
   * @returns Endpoint configuration
   */
  removeTagsFromUser: () => ({
    url: '/learnworlds/user/tags/remove',
    method: 'POST',
  }),

  /**
   * Suspend user endpoint
   * @returns Endpoint configuration
   */
  suspendUser: () => ({
    url: '/learnworlds/user/suspend',
    method: 'PUT',
  }),

  /**
   * Unsuspend user endpoint
   * @returns Endpoint configuration
   */
  unsuspendUser: () => ({
    url: '/learnworlds/user/unsuspend',
    method: 'PUT',
  }),

  /**
   * Get user endpoint
   * @returns Endpoint configuration
   */
  getUser: () => ({
    url: '/learnworlds/user',
    method: 'GET',
  }),

  /**
   * Create user endpoint
   * @returns Endpoint configuration
   */
  createUser: () => ({
    url: '/learnworlds/user',
    method: 'POST',
  }),
};

/**
 * Type definitions for API responses
 */
export type LearnworldsApiTypes = {
  getCourses: ILearnworldsCoursesResponse;
  enrollUser: PXActionResult;
  unenrollUser: PXActionResult;
  addTagsToUser: PXActionResult;
  removeTagsFromUser: PXActionResult;
  suspendUser: PXActionResult;
  unsuspendUser: PXActionResult;
  getUser: ILearnworldsUserResponse;
  createUser: ILearnworldsUserResponse;
};

/**
 * Type definitions for API request payloads
 */
export type LearnworldsApiPayloads = {
  getCourses: IGetLearnworldsCoursesParams;
  enrollUser: ILearnworldsEnrollmentParams;
  unenrollUser: ILearnworldsUnenrollmentParams;
  addTagsToUser: ILearnworldsUserTagsParams;
  removeTagsFromUser: ILearnworldsUserTagsParams;
  suspendUser: ILearnworldsUserSuspensionParams;
  unsuspendUser: ILearnworldsUserSuspensionParams;
  getUser: IGetLearnworldsUserParams;
  createUser: ICreateLearnworldsUserParams;
};
