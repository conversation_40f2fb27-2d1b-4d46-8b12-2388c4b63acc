"use client";

import useSWR, { SWRConfiguration } from "swr";

import { getAllTransactions, getTransaction } from "@/lib/services";
import {
  GetAllTransactionsParams,
  Transaction,
  TransactionListResponse,
  TransactionsFilters,
} from "@/types";

type UseFetchTxnsParams = {
  options?: SWRConfiguration;
  filters?: TransactionsFilters;
};

function prepareFetchTxns(filters?: TransactionsFilters) {
  let options: GetAllTransactionsParams = {};
  if (filters) {
    if (filters.searchQuery)
      options.searchQuery = filters.searchQuery as string;
    if (filters.statusFilter) options.status = filters.statusFilter as string;
    if (filters.subscriptionFilter)
      options.subscriptionId = filters.subscriptionFilter as string;
    if (filters.paymentMethodFilter)
      options.paymentMethod = filters.paymentMethodFilter as string;
  }
  return options;
}

export const useFetchTransactions = (params?: UseFetchTxnsParams) => {
  const options: GetAllTransactionsParams = prepareFetchTxns(params?.filters);

  return useSWR<TransactionListResponse>(
    `useFetchTransactions-${JSON.stringify(options)}`,
    () => getAllTransactions(options),
    params?.options
  );
};

export const useFetchTransaction = (id: string) => {
  return useSWR<Transaction>(`useFetchTransaction-${id}`, () =>
    getTransaction(id)
  );
};
