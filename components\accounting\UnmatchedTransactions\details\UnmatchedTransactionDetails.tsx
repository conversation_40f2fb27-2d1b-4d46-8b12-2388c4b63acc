"use client";

import { ArrowLeftOutlined, CloseOutlined } from "@ant-design/icons";
import {
  App,
  Button,
  Col,
  Descriptions,
  Divider,
  Modal,
  Row,
  Skeleton,
  Space,
  Tabs,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import dayjs from "dayjs";

import SearchCustomer from "./SearchCustomer";
import SearchInvoice from "./SearchInvoice";
import { useFetchInvoices } from "@/hooks/accounting/invoices";
import { formatter } from "@/lib/currency";
import { formatDate } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { getBusinessEntityId } from "@/lib/businessEntities";
import { Invoice, UnMatchedTransactions } from "@/types";
import {
  recordExcessPaymentForCustomer,
  recordOfflinePaymentForInvoice,
} from "@/lib/services/chargebee";

type UnmatchedTransactionDetailsProps = {
  id: string;
  transaction: UnMatchedTransactions;
  handleClose: Function;
  skipTransaction: Function;
  updateTransactionStatus: Function;
};

const { Text } = Typography;

export default function UnmatchedTransactionDetails({
  transaction,
  handleClose,
  skipTransaction,
  updateTransactionStatus,
}: UnmatchedTransactionDetailsProps) {
  const { message, modal } = App.useApp();
  const [allInvoices, setAllInvoices] = useState<Invoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [searchQuery, setSearchQuery] = useState<string | undefined>(undefined);
  // Queries
  const { data: invoicesPaymentDue } = useFetchInvoices({
    filters: {
      statusFilter: "payment_due",
      searchQuery,
    },
  });
  const { data: invoicesNotPaid } = useFetchInvoices({
    filters: {
      statusFilter: "not_paid",
      searchQuery,
    },
  });

  useEffect(() => {
    getInvoices();
  }, [invoicesPaymentDue, invoicesNotPaid]);

  const getInvoices = async () => {
    if (invoicesPaymentDue && invoicesNotPaid) {
      setAllInvoices([...invoicesPaymentDue?.items, ...invoicesNotPaid?.items]);
      setFilteredInvoices([
        ...invoicesPaymentDue?.items,
        ...invoicesNotPaid?.items,
      ]);
    }
  };

  const getFilteredInvoices = (query: string) => {
    if (query === "") setSearchQuery(undefined);
    setSearchQuery(query);
  };

  const handleTransactionSkip = () => {
    skipTransaction(transaction.id);
  };

  const attachPaymentToInvoice = async (invoiceId: string) => {
    let customPaymentMethodId = getCustomPaymentId(transaction.bankName);
    if (!customPaymentMethodId || customPaymentMethodId === "") {
      message.open({
        type: "error",
        content: "Invalid bank name!",
      });
      return;
    }
    const res = await recordOfflinePaymentForInvoice({
      invoiceId,
      amount: Math.round(transaction.paymentAmount * 100),
      date: dayjs(transaction.paymentDate).unix(),
      paymentMethod: "custom",
      customPaymentMethodId: customPaymentMethodId,
      reference: transaction.paymentId,
    });
    if (res?.transaction) {
      message.open({
        type: "success",
        content: "Offline payment added to invoice!",
      });
      updateTransactionStatus(transaction.id, "matched");
    }
  };

  // This is necessary for now because there are some values in production
  // that do not have the proper case, so they will be stuck in unmatched transactions
  const getCustomPaymentId = (paymentMethod: string): string => {
    let bankName = paymentMethod.toLowerCase();
    switch (bankName) {
      case "airwallex":
        return "AirWallex";
      case "ibanfirst":
        return "IbanFirst";
      case "revolut":
        return "Revolut";
    }
    return "";
  };

  const attachPaymentToCustomer = async (customerId: string) => {
    let customPaymentMethodId = getCustomPaymentId(transaction.bankName);
    if (!customPaymentMethodId || customPaymentMethodId === "") {
      message.open({
        type: "error",
        content: "Invalid bank name!",
      });
      return;
    }
    const res = await recordExcessPaymentForCustomer({
      customerId,
      amount: Math.round(transaction.paymentAmount * 100),
      date: dayjs(transaction.paymentDate).unix(),
      paymentMethod: "custom",
      customPaymentMethodId: customPaymentMethodId,
      reference: transaction.paymentId,
    });
    if (res?.transaction) {
      message.open({
        type: "success",
        content: "Offline payment added to customer!",
      });
      // TODO: Before skipping transaction, make sure to mark it as matched in paradox API
      updateTransactionStatus(transaction.id, "matched");
      skipTransaction();
    } else {
      message.open({
        type: "error",
        content: "Failed to add payment to customer!",
      });
    }
  };

  const ignorePayment = async () => {
    modal.warning({
      title: `Are you sure you want to mark this bank transfer as "ignored"?`,
      content:
        "You won't be able to attach this bank transfer anymore. Mainly when this bank transfer doesn't involve customer payments",
      okText: "Mark as Ignored",
      okType: "danger",
      cancelText: "Close",
      okCancel: true,
      onCancel: () => {},
      onOk: async () => {
        updateTransactionStatus(transaction.id, "ignored");
      },
    });
  };

  const transactionSummaryItems = [
    {
      key: "1",
      label: "Bank name",
      children: <Text style={{ fontSize: 13 }}>{transaction?.bankName}</Text>,
    },
    {
      key: "2",
      label: "Amount",
      children: (
        <Text style={{ fontSize: 13 }}>
          {transaction?.paymentAmount &&
            formatter.format(transaction?.paymentAmount)}
        </Text>
      ),
    },
    {
      key: "3",
      label: "Payment date",
      children: (
        <Text style={{ fontSize: 13 }}>
          {transaction?.paymentDate && formatDate(transaction?.paymentDate)}
        </Text>
      ),
    },
    {
      key: "4",
      label: "Payment Id",
      children: <Text style={{ fontSize: 13 }}>{transaction?.paymentId}</Text>,
    },
    {
      key: "5",
      label: "Payment description",
      children: (
        <Text style={{ fontSize: 13 }}>{transaction?.paymentDescription}</Text>
      ),
    },
    {
      key: "6",
      label: "Sender name",
      children: (
        <Text style={{ fontSize: 13 }}>
          {transaction?.senderLastName + " " + transaction?.senderLastName}
        </Text>
      ),
    },
    {
      key: "7",
      label: "Sender address",
      children: (
        <Text style={{ fontSize: 13 }}>{transaction?.senderAddress}</Text>
      ),
    },
    {
      key: "8",
      label: "Sender email",
      children: (
        <Text style={{ fontSize: 13 }}>{transaction?.senderEmail}</Text>
      ),
    },
    {
      key: "9",
      label: "Currency",
      children: <Text style={{ fontSize: 13 }}>{transaction?.currency}</Text>,
    },
    {
      key: "10",
      label: "Status",
      children: (
        <Text style={{ fontSize: 13 }}>
          {capitalizeWord(transaction?.status)}
        </Text>
      ),
    },
    {
      key: "11",
      label: "Business Entity",
      children: <Text style={{ fontSize: 13 }}>{transaction?.entityId}</Text>,
    },
  ];

  const tabItems = [
    {
      key: "1",
      label: "Attach to an invoice",
      children: (
        <SearchInvoice
          filteredInvoices={filteredInvoices}
          handleSearch={getFilteredInvoices}
          attachPaymentToInvoice={attachPaymentToInvoice}
          markPaymentAsIgnored={ignorePayment}
          resetFilteredList={() => {
            setFilteredInvoices(allInvoices);
          }}
          handleSkip={() => {
            handleTransactionSkip();
          }}
        />
      ),
    },
    {
      key: "2",
      label: "Attach to a customer",
      children: (
        <SearchCustomer
          handleSkip={() => {
            handleTransactionSkip();
          }}
          markPaymentAsIgnored={ignorePayment}
          attachPaymentToCustomer={attachPaymentToCustomer}
          entityId={getBusinessEntityId(transaction?.entityId)}
        />
      ),
    },
  ];

  return !transaction ? (
    <Skeleton />
  ) : (
    <div>
      <Row
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            width: "35%",
            justifyContent: "space-evenly",
            alignItems: "center",
          }}
        >
          <Button
            icon={<ArrowLeftOutlined style={{ color: "#5B5B5D" }} />}
            onClick={() => {
              handleClose();
            }}
          />
          <Text style={{ fontSize: 24 }}>Attach unmatched bank transfers</Text>
        </div>
        <Space>
          <Button
            icon={<CloseOutlined />}
            onClick={() => {
              handleClose();
            }}
          >
            <Text strong>Close</Text>
          </Button>
        </Space>
      </Row>
      <Divider />
      <Row>
        <Col span={8}>
          <Descriptions
            title="Transaction Summary"
            items={transactionSummaryItems}
            column={1}
            bordered
            size="small"
          />
        </Col>
        <Col span={15} offset={1}>
          <Tabs defaultActiveKey="1" items={tabItems} />
        </Col>
      </Row>
    </div>
  );
}
