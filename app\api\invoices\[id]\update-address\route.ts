import {
  initChargebee,
  refundInvoice,
  updateInvoiceAddress,
} from "@/server/chargebee";
import { UpdateInvoiceAddressParams } from "@/types";
import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the POST request for issuing a refund.
 *
 * @param req - The request object.
 * @returns A JSON response containing the returned response or an error message.
 */
export async function POST(req: Request) {
  try {
    const body = (await req.json()) as UpdateInvoiceAddressParams;
    if (body.invoiceId) {
      const response = await updateInvoiceAddress(body);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_UPDATE_INVOICE_ADDRESS", details: error },
      { status: 400 }
    );
  }
}
