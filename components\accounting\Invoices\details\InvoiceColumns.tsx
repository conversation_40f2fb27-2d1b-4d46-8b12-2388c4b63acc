"use client";

import { Row, Col, Typography } from "antd";
import { Invoice as ChargebeeInvoice } from "chargebee";

import { formatCents } from "@/lib/currency";

const { Text } = Typography;

type InvoiceLineItem = ChargebeeInvoice.LineItem;

export const getInvoiceColumns = (invoice: any) => [
  {
    title: "Product",
    key: "id",
    width: 250,
    render: (record: any) => (
      <Row>
        <Col span={8}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <img
              src={record.image}
              style={{ width: "64px", borderRadius: "5px" }}
            />
          </div>
        </Col>
        <Col span={14} offset={2}>
          <div
            style={{
              display: "flex",
              alignItems: "start",
              justifyContent: "start",
              flexDirection: "column",
            }}
          >
            <Text style={{ color: "gray" }}>{record?.name}</Text>
            <Text
              style={{
                color: "gray",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                width: "200px",
              }}
              ellipsis={{ tooltip: record?.description }}
            >
              {record?.description}
            </Text>
          </div>
        </Col>
      </Row>
    ),
  },
  {
    title: "Unit price (Excl. VAT)",
    key: "id",
    render: (record: InvoiceLineItem) => {
      let unitAmountTaxExcluded = 0;
      if (invoice?.priceType === "tax_inclusive") {
        unitAmountTaxExcluded = Number(record.amount);
        if (record.tax_rate) {
          unitAmountTaxExcluded =
            unitAmountTaxExcluded / (1 + record.tax_rate / 100);
        }
      } else {
        unitAmountTaxExcluded = Number(record.amount);
      }
      return (
        <div>
          <Text>{`
            ${formatCents(unitAmountTaxExcluded)} x ${record.quantity}`}</Text>
        </div>
      );
    },
  },
  {
    title: "Discount (Excl. VAT)",
    key: "id",
    render: (record: InvoiceLineItem) => {
      let discountAmountTaxExcluded = 0;
      if (invoice?.priceType === "tax_inclusive") {
        discountAmountTaxExcluded = Number(record.discount_amount);
        if (record.tax_rate) {
          discountAmountTaxExcluded =
            discountAmountTaxExcluded / (1 + record.tax_rate / 100);
        }
      } else {
        discountAmountTaxExcluded = Number(record.discount_amount);
      }
      return (
        <div>
          <Text>{`-
            ${formatCents(discountAmountTaxExcluded)}`}</Text>
        </div>
      );
    },
  },
  {
    title: "Total Excl. VAT",
    key: "id",
    render: (record: InvoiceLineItem) => {
      let discountAmountTaxExcluded = 0;
      if (invoice?.priceType === "tax_inclusive") {
        discountAmountTaxExcluded = Number(record.discount_amount);
        if (record.tax_rate) {
          discountAmountTaxExcluded =
            discountAmountTaxExcluded / (1 + record.tax_rate / 100);
        }
      } else {
        discountAmountTaxExcluded = Number(record.discount_amount);
      }
      let unitAmountTaxExcluded = 0;
      if (invoice?.priceType === "tax_inclusive") {
        unitAmountTaxExcluded = Number(record.amount);
        if (record.tax_rate) {
          unitAmountTaxExcluded =
            unitAmountTaxExcluded / (1 + record.tax_rate / 100);
        }
      } else {
        unitAmountTaxExcluded = Number(record.amount);
      }
      return (
        <div>
          <Text>{`
            ${formatCents(
              unitAmountTaxExcluded - discountAmountTaxExcluded
            )}`}</Text>
        </div>
      );
    },
  },
  {
    title: "VAT",
    dataIndex: "tax_amount",
    render: (tax_amount: InvoiceLineItem["tax_amount"]) => (
      <span>
        {`
          ${tax_amount && formatCents(tax_amount)}`}
      </span>
    ),
    key: "id",
  },
  {
    title: "Amount (EUR) (Incl. VAT)",
    key: "id",
    render: (record: InvoiceLineItem) => {
      let discountAmountTaxExcluded = 0;
      if (invoice?.priceType === "tax_inclusive") {
        discountAmountTaxExcluded = Number(record.discount_amount);
        if (record.tax_rate) {
          discountAmountTaxExcluded =
            discountAmountTaxExcluded / (1 + record.tax_rate / 100);
        }
      } else {
        discountAmountTaxExcluded = Number(record.discount_amount);
      }
      let unitAmountTaxExcluded = 0;
      if (invoice?.priceType === "tax_inclusive") {
        unitAmountTaxExcluded = Number(record.amount);
        if (record.tax_rate) {
          unitAmountTaxExcluded =
            unitAmountTaxExcluded / (1 + record.tax_rate / 100);
        }
      } else {
        unitAmountTaxExcluded = Number(record.amount);
      }
      const totalExclVat = unitAmountTaxExcluded - discountAmountTaxExcluded;
      return (
        <div>
          <Text>{`
            ${formatCents(totalExclVat + Number(record?.tax_amount || 0))}`}</Text>
        </div>
      );
    },
  },
];
