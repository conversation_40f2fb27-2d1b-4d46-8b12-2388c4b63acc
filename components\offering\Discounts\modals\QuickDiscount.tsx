"use client";

import { useState, useEffect } from "react";
import { App, Modal, Typography } from "antd";

import { get5DaysFromTodayDateString } from "@/lib/date";
import { createDiscount } from "@/lib/services";
import { CreateDiscount } from "@/types";

const { Title, Text } = Typography;

type AddProps = {
  isModalOpen: boolean;
  handleCancel: Function;
};

const CreateQuickDiscountModal = ({ isModalOpen, handleCancel }: AddProps) => {
  const { message } = App.useApp();
  const [discountCode, setDiscountCode] = useState<string>("");
  const [generating, setGenerating] = useState<boolean>(false);

  const getDiscountCode = async (): Promise<string> => {
    if (generating) return "";
    setGenerating(true);
    const random = Math.floor(Math.random() * 900) + 100;
    const time = Date.now() % 1000;
    const code = ((random + time) % 1000).toString().padStart(3, "0");
    const discountInfo: CreateDiscount = {
      name: "",
      code: `PX24HEROES${code}`,
      type: "percentage",
      amount: 10,
      applyOn: "each_specified_item",
      durationType: "forever",
      durationPeriodUnit: "day",
      durationPeriodAmount: 5,
      maxRedemptions: 1,
      validUntil: get5DaysFromTodayDateString(),
      status: "active",
    };
    try {
      let res = await createDiscount(discountInfo, true);
      if (res && res.id) {
        message.open({
          type: "success",
          content: "Discount Generated!",
        });
        return discountInfo.code;
      }
    } finally {
      setGenerating(false);
    }
    return "";
  };

  useEffect(() => {
    if (!isModalOpen) {
      setDiscountCode("");
    } else {
      getDiscountCode().then((code) => setDiscountCode(code));
    }
  }, [isModalOpen]);

  return (
    <Modal
      title={
        <Title level={3} style={{ textAlign: "center", paddingBottom: "1rem" }}>
          {`Here's a quick 10% discount code`}
        </Title>
      }
      centered
      open={isModalOpen}
      footer={null}
      onCancel={() => handleCancel()}
      destroyOnClose={true}
    >
      <div style={{ textAlign: "center" }}>
        <Text
          copyable
          code
          type={"success"}
          style={{ fontSize: 20, color: "black" }}
        >
          {discountCode || "Generating..."}
        </Text>
      </div>
      <Text
        mark
        style={{ display: "block", textAlign: "center", paddingTop: "1rem" }}
      >
        This discount code is valid only for the next 5 days & can only be
        redeemed once.
      </Text>
    </Modal>
  );
};

export default CreateQuickDiscountModal;
