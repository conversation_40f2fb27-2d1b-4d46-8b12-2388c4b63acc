"use client";

import {
  App,
  Checkbox,
  Col,
  Divider,
  Form,
  Input,
  Row,
  Select,
  Typography,
} from "antd";
import countryList from "react-select-country-list";
import { useEffect, useMemo, useState } from "react";
import { EditOutlined, PhoneOutlined } from "@ant-design/icons";
import { CheckboxChangeEvent } from "antd/es/checkbox";

import { Invoice } from "@/types";
import { updateInvoiceAddress } from "@/lib/services/chargebee";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";

const { Text, Title } = Typography;

type CustomerAddressProps = {
  isOpen: boolean;
  setIsOpen: Function;
  invoice: Invoice;
};

export const CustomerAddress = ({
  isOpen,
  invoice,
  setIsOpen,
}: CustomerAddressProps) => {
  const { message } = App.useApp();
  const countryDropdownOptions = useMemo(() => countryList().getData(), []);
  const [copyBillingAddress, setCopyBillingAddress] = useState(false);
  const [customerBillingForm] = Form.useForm();
  const [customerShippingForm] = Form.useForm();

  const copyBillingAddressToShipping = (e: boolean) => {
    setCopyBillingAddress(e);
    if (e === true) {
      // Set field values of the shipping address form to the billing address form
      let billingAddress = customerBillingForm.getFieldsValue();
      customerShippingForm.setFieldsValue(billingAddress);
    } else {
      customerShippingForm.resetFields();
    }
  };

  const updateCustomerAddress = async () => {
    const billing_address = customerBillingForm.getFieldsValue();
    const shipping_address = customerShippingForm.getFieldsValue();
    const params = {
      invoiceId: invoice?.chargebeeId,
      shippingAddress: shipping_address,
      billingAddress: billing_address,
    };
    const res = await updateInvoiceAddress(params);
    if (res.invoice) {
      message.open({
        type: "success",
        content: "Address updated successfully!",
      });
      setIsOpen(false);
    } else if (res.error) {
      message.open({
        type: "error",
        content: "Cannot update address!",
      });
    }
  };

  useEffect(() => {
    console.log("ℹ️ invoice: ", invoice);
    customerBillingForm.setFieldsValue(invoice.billingAddress);
    customerShippingForm.setFieldsValue(invoice.billingAddress);
  }, [invoice]);

  return (
    <DrawerFormContainer
      title="Edit customer address details"
      onClose={() => {
        setIsOpen(false);
      }}
      isOpen={isOpen}
      onSubmit={updateCustomerAddress}
      submitText="Update"
      onCancel={() => {
        setIsOpen(false);
      }}
    >
      <Form form={customerBillingForm} layout="vertical">
        <Title level={3}>Billing info</Title>
        <Row>
          <Col span={11}>
            <Form.Item name={"first_name"} label="First name">
              <Input placeholder="First name" suffix={<EditOutlined />} />
            </Form.Item>
          </Col>
          <Col span={11} offset={2}>
            <Form.Item name={"last_name"} label="Last name">
              <Input placeholder="Last name" suffix={<EditOutlined />} />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name={"email"}
          label="Email"
          rules={[
            {
              type: "email",
              message: "Please enter a valid email",
            },
          ]}
        >
          <Input placeholder="Email" suffix={<EditOutlined />} />
        </Form.Item>
        <Form.Item name={"phone"} label="Phone">
          <Input placeholder="Phone number" suffix={<PhoneOutlined />} />
        </Form.Item>
        <Form.Item name={"company"} label="Company">
          <Input placeholder="Company" suffix={<EditOutlined />} />
        </Form.Item>
        <Text style={{ fontSize: 15 }} strong>
          Address
        </Text>
        <br />
        <br />
        <Form.Item name={"country"} label="Country">
          <Select
            placeholder="Country"
            options={countryDropdownOptions}
            allowClear
            showSearch
            optionFilterProp="label"
          />
        </Form.Item>
        <Form.Item name={"line1"} label="Address line 1">
          <Input placeholder="Address line 1" suffix={<EditOutlined />} />
        </Form.Item>
        <Form.Item name={"line2"} label="Address line 2">
          <Input placeholder="Address line 2" suffix={<EditOutlined />} />
        </Form.Item>
        <Form.Item name={"city"} label="City">
          <Input placeholder="City" suffix={<EditOutlined />} />
        </Form.Item>
        <Form.Item name={"zip"} label="Zip/Postal code">
          <Input placeholder="Zip code" suffix={<EditOutlined />} />
        </Form.Item>
        <Form.Item name={"state"} label="State">
          <Input placeholder="State" suffix={<EditOutlined />} />
        </Form.Item>
      </Form>
      <Divider />
      <Form form={customerShippingForm} layout="vertical">
        <Title level={3}>Shipping info</Title>

        <Checkbox
          checked={copyBillingAddress}
          onChange={(e: CheckboxChangeEvent) => {
            copyBillingAddressToShipping(e.target.checked);
          }}
        >
          Same as billing info
        </Checkbox>
        <br />
        <br />
        <Row>
          <Col span={11}>
            <Form.Item name={"first_name"} label="First name">
              <Input
                placeholder="First name"
                suffix={<EditOutlined />}
                disabled={copyBillingAddress}
              />
            </Form.Item>
          </Col>
          <Col span={11} offset={2}>
            <Form.Item name={"last_name"} label="Last name">
              <Input
                placeholder="Last name"
                suffix={<EditOutlined />}
                disabled={copyBillingAddress}
              />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name={"email"}
          label="Email"
          rules={[
            {
              type: "email",
              message: "Please enter a valid email",
            },
          ]}
        >
          <Input
            placeholder="Email"
            suffix={<EditOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Form.Item name={"phone"} label="Phone">
          <Input
            placeholder="Phone number"
            suffix={<PhoneOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Form.Item name={"company"} label="Company">
          <Input
            placeholder="Company"
            suffix={<EditOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Text style={{ fontSize: 15 }} strong>
          Address
        </Text>
        <br />
        <br />
        <Form.Item name={"country"} label="Country">
          <Select
            placeholder="Country"
            options={countryDropdownOptions}
            allowClear
            showSearch
            optionFilterProp="label"
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Form.Item name={"line1"} label="Address line 1">
          <Input
            placeholder="Address line 1"
            suffix={<EditOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Form.Item name={"line2"} label="Address line 2">
          <Input
            placeholder="Address line 2"
            suffix={<EditOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Form.Item name={"city"} label="City">
          <Input
            placeholder="City"
            suffix={<EditOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Form.Item name={"zip"} label="Zip/Postal code">
          <Input
            placeholder="Zip code"
            suffix={<EditOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
        <Form.Item name={"state"} label="State">
          <Input
            placeholder="State"
            suffix={<EditOutlined />}
            disabled={copyBillingAddress}
          />
        </Form.Item>
      </Form>
    </DrawerFormContainer>
  );
};
