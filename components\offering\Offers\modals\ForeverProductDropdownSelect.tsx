"use client";

import { Product } from "@/types";
import { DownOutlined, WarningOutlined } from "@ant-design/icons";
import { Col, Image, Row, Space, Tag, Tooltip, Typography } from "antd";
import { useState } from "react";

const { Text } = Typography;

const europeImg =
  "https://paradox-os.s3.eu-west-3.amazonaws.com/projectimages/europe-flag-icon.png";

const ForeverProductDropDownSelect = (params: {
  product: Product | undefined;
  showDisabledTooltip?: boolean;
  alreadySelected?: boolean;
}) => {
  const [detailsVisible, setDetailsVisible] = useState<boolean>(false);

  return (
    params.product && (
      <div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              width: "20%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Image src={params.product.image} preview={false} height={50} />
          </div>
          <div
            style={{
              width: "60%",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {params.product.externalName} <br />
            <Text style={{ fontSize: 13, color: "darkgray" }}>
              {params.product.plans.length} Plans
            </Text>
          </div>
          <div
            style={{
              width: "10%",
              padding: "10px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Space>
              {params.showDisabledTooltip && (
                <Tooltip
                  title={
                    params.product?.status !== "active"
                      ? "This product can't be selected as the status is archived"
                      : "This product can't be selected"
                  }
                >
                  <WarningOutlined />
                </Tooltip>
              )}
              <DownOutlined
                onClick={(e) => {
                  e.stopPropagation();
                  setDetailsVisible(!detailsVisible);
                }}
              />
            </Space>
          </div>
        </div>
        <div
          style={{
            display: detailsVisible ? "block" : "none",
            marginTop: "10px",
            marginLeft: "20%",
          }}
        >
          {params.product.plans.map((item) => {
            return (
              <Row key={item.chargebeeId} style={{ padding: "7px" }}>
                <Col span={20} offset={0}>
                  {item.internalName}
                  <br />
                  <Space>
                    <Image
                      src={europeImg}
                      preview={false}
                      height={18}
                      width={25}
                      style={{ borderRadius: "3px" }}
                    />
                    <Text style={{ color: "darkgray" }}>
                      {item.prices[0].currencyCode.toUpperCase()} -{" "}
                      {item.prices[0].amountPerBillingCycle}€ x{" "}
                      {item.prices[0].periodUnit === "monthly-forever"
                        ? "Monthly"
                        : "Yearly"}
                    </Text>
                  </Space>
                </Col>
                <Col span={4}>
                  <Tag color="purple">
                    {item.prices[0].periodUnit === "monthly-forever"
                      ? "Monthly"
                      : "Yearly"}
                  </Tag>
                </Col>
              </Row>
            );
          })}
        </div>
      </div>
    )
  );
};

export default ForeverProductDropDownSelect;
