# Paradox OS

This is a Next.js project written primarily in TypeScript.

## 💻 Getting Started

Install dependencies:

```bash
npm install
```

Run the development server:

```bash
npm run dev
```

## 🪺 Major dependencies

The main libraries used by this project are:

- `Antd`: Antd is a UI components library created by Alibaba group that provides good functionality along with clear modern UI for a dashboard application. Particularly important for this application is the great Form control provided by Antd which is used extensively throughout the application.
- `ChargeBee`: Library provided by ChargeBee, that is used to communicate directly with their systems. This is used to perform small tasks (writing off invoice, manually creating subscriptions etc) in the front-end without a backend request.
  _This list is not exhaustive - have a look at `package.json`_

## 🗂️ Project Structure

- `.github/` - Deployment via Github Actions (both for staging & production)
- `app/` - This directory contains all the root pages of the project.
- `components/` - This directory contains all the components used in the pages. Each offering in our product (eg Invoices) will have their own folder with all the corresponding components.
- `hooks/` - This directory contains all the hooks related functionalities. You will find a separate file for each module of our application. These are custom hooks that abstract the network calls for the components making the code cleaner and easier to manipulate.
- `lib/` - This folder contains the helper functions used throughout the application (e.g. Date processing, data formatting)
  - `services` This folder contains all the API calls to the backend. The hooks act as an abstraction layer between the services and the components themselves. Each module will have it's own service handling the API calls.
- `types/` - All the TypeScript types used within the project are centralized here, sepated into folders by module (apart from the types for declaring component properties).

## 🚀 Deployment

We use AWS EC2 for this application's traffic. Deployments are done via Github Actions. All you need to do is merge any branch with `staging` or merge `staging` -> `main` to trigger deployments:

- To deploy in `staging`: merge our work on the `staging` branch.
- To deploy in `production`: merge our work on the `main` branch.

---

## 🧪 TODO

- Add new endpoint for px-api getOfferBySlug
- Add a link into sales details page
- Change useFetchOffer

### TO optimize:

./components/Offers/forms/AddOfferForm.tsx
./components/Offers/forms/offerAccess/ConfigProductAccess.tsx
./components/Offers/forms/offerAccess/ConfigRecommendedAccess.tsx
./components/Offers/forms/ConfigureOfferProductsForm.tsx
./components/Offers/forms/UpdateOfferForm.tsx
./components/Products/forms/UpdateProductForm.tsx
./components/Products/forms/CreateProductForm.tsx
./components/UnmatchedTransactions/details/InvoiceDisplay.tsx
./components/Subscriptions/forms/EditBillingCycles.tsx
./components/Subscriptions/forms/Downsell.tsx

## 👁️‍🗨️ Use-cases / Tests

| Status | Description                     |
| :----: | ------------------------------- |
|   ✅   | Main dashboard page             |
|   ✅   | Customer management             |
|   ✅   | Product lines management        |
|   ✅   | Product families management     |
|   ✅   | Products management             |
|   ✅   | Products plans management       |
|   ✅   | Discounts management            |
|   ✅   | Offers management               |
|   ❓   | Sales tracking                  |
|   ✅   | Invoice management              |
|   ✅   | Credit notes management         |
|   ✅   | Transaction tracking            |
|   ✅   | Unmatched transactions handling |
|   ✅   | Cron jobs monitoring            |

### Feature Status by Page

| Page                       | Feature                       | Status | Notes                       |
| -------------------------- | ----------------------------- | :----: | --------------------------- |
| **Dashboard**              | View statistics               |   ✅   |                             |
| ---                        | ---                           |  ---   | ---                         |
| **Customers**              | List view                     |   ✅   |                             |
|                            | Drawer view                   |   ✅   |                             |
|                            | Data updates                  |   ✅   |                             |
|                            | Search functionality          |   ✅   |                             |
| ---                        | ---                           |  ---   | ---                         |
| **Product Lines**          | List view                     |   ✅   |                             |
|                            | Search                        |   ✅   |                             |
|                            | Create                        |   ✅   |                             |
|                            | Update                        |   ✅   |                             |
| **Product Families**       | List view                     |   ✅   |                             |
|                            | Search                        |   ✅   |                             |
|                            | Create                        |   ✅   |                             |
|                            | Update                        |   ✅   |                             |
| **Products**               | List view                     |   ✅   |                             |
|                            | Search                        |   ✅   |                             |
|                            | Create                        |   ✅   |                             |
|                            | Update                        |   ✅   |                             |
| **Product Plans**          | List                          |   ✅   |                             |
|                            | Create                        |   ✅   |                             |
|                            | Update                        |   ✅   |                             |
| ---                        | ---                           |  ---   | ---                         |
| **Discounts**              | List view                     |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Details view                  |   ✅   |                             |
|                            | Create                        |   ✅   |                             |
|                            | Create quick                  |   ✅   |                             |
|                            | Edit                          |   ✅   |                             |
|                            | Disable / Enable              |   ✅   |                             |
|                            | Set plans                     |   ✅   |                             |
| **Offers**                 | List view                     |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Details view                  |   ✅   |                             |
|                            | Create                        |   ✅   |                             |
|                            | Update                        |   ✅   |                             |
|                            | Attach Product                |   ✅   |                             |
|                            | Attach Access                 |   ✅   |                             |
|                            | Disable / Enable              |   ✅   |                             |
| **Sales**                  | List view                     |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Refresh subscription          |   ✅   |                             |
|                            | Change payment method         |   ✅   |                             |
|                            | Edit billing date             |   ✅   |                             |
|                            | Edit subscription             |   ✅   |                             |
|                            | Change product                |   ✅   | Need refactoring            |
|                            | Cancel subscription           |   ✅   |                             |
|                            | Details view                  |   ✅   |                             |
|                            | Export subscriptions          |   ✅   |                             |
|                            | Create Manual Subscription    |   ❓   |                             |
| ---                        | ---                           |  ---   | ---                         |
| **Invoices**               | List view                     |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Details view                  |   ✅   |                             |
|                            | Download PDF                  |   ✅   |                             |
|                            | Update Address                |   ✅   | Fixed (not working in prod) |
|                            | Collect now                   |   ✅   |                             |
|                            | Record an offline refund      |   ✅   |                             |
|                            | Issue a refund                |   ✅   |                             |
|                            | Void Invoice with Credit Note |   ✅   |                             |
| **Credit Notes**           | List view                     |   ✅   |                             |
|                            | Details view                  |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Download PDF                  |   ✅   |                             |
| **Transactions**           | List view                     |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Details view                  |   ✅   |                             |
| **Unmatched Transactions** | List view                     |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Manual attachment             |   ✅   |                             |
| **Cron Logs**              | List view                     |   ✅   |                             |
|                            | Filter list                   |   ✅   |                             |
|                            | Reprocess log                 |   ✅   |                             |
| ---                        | ---                           |  ---   | ---                         |

---

Legend:

- ✅ Working
- 🚜 In progress
- ❌ Not working
- ❓ Needs verification
