"use client";

import { Tag, Typography } from "antd";

const { Text } = Typography;

export const getDiscountsTableColumns = () => {
  return [
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      render: (val: string) => {
        return (
          <Text type="secondary" copyable>
            {val}
          </Text>
        );
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (val: string) => {
        if (val === "active") {
          return <Tag color="green">Active</Tag>;
        }
        if (val === "disabled") {
          return <Tag color="orange">Disabled</Tag>;
        }
        if (val === "expired") {
          return <Tag color="red">Expired</Tag>;
        }
      },
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (val: string) => {
        if (val === "percentage") {
          return <Tag>Percentage</Tag>;
        }
        if (val === "fixed_amount") {
          return <Tag>Fixed amount</Tag>;
        }
      },
    },
    {
      title: "Duration type",
      dataIndex: "durationType",
      key: "durationType",
      render: (val: string) => {
        if (val === "one_time") {
          return <Tag>One time</Tag>;
        }
        if (val === "limited_period") {
          return <Tag>Limited period</Tag>;
        }
        if (val === "forever") {
          return <Tag>Forever</Tag>;
        }
      },
    },
  ];
};
