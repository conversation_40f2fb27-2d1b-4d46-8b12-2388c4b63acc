"use client";

import { Form, Modal, Input, Typography, App } from "antd";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { ProductLine } from "@/types";

type CreateProductLineFormProps = {
  isOpen: boolean;
  onAddNewPL: Function;
  onDismiss: Function;
};

const { Title, Text } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

export default function CreateProductLineForm({
  isOpen,
  onAddNewPL,
  onDismiss,
}: CreateProductLineFormProps) {
  const [form] = Form.useForm();
  const { modal } = App.useApp();

  const handleCancel = async () => {
    modal.warning({
      title: "Are you sure you want to leave this page?",
      content: "You will lose all unsaved changes",
      onOk: () => {
        onDismiss();
        form.resetFields();
      },
      okText: "Confirm",
      okButtonProps: { danger: true },
      cancelButtonProps: { ghost: true },
      cancelText: "Cancel",
    });
  };

  return (
    <DrawerFormContainer
      isOpen={isOpen}
      onCancel={handleCancel}
      onClose={handleCancel}
      onSubmit={() => {
        form.submit();
      }}
      submitText="Create"
      submitButtonProps={{
        "data-testid": "create-product-line-form-btn",
      }}
      title="Create a product line"
    >
      <Title level={5}>Product line settings</Title>
      <Form
        form={form}
        onFinish={() => onAddNewPL(form.getFieldsValue() as ProductLine)}
        layout="vertical"
        style={{ maxWidth: 800 }}
      >
        <Form.Item
          label="Product line name"
          name="name"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input
            placeholder="Product line name"
            data-testid="create-product-line-name-input"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this product line internally.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product line description"
          name="description"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input
            placeholder="EDEC"
            data-testid="create-product-line-description-input"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product line description attached to this product.
        </Text>
        <br />
        <br />
        <br />
      </Form>
    </DrawerFormContainer>
  );
}
