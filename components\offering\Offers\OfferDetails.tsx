"use client";

import { App, Divider, Ta<PERSON>, TabsProps, Skeleton } from "antd";
import { useRouter } from "next/navigation";
import React, { useMemo, useState } from "react";

import { CheckoutSettings } from "@/components/offering/Offers/tabs/CheckoutSettings";
import { OfferSettings } from "@/components/offering/Offers/tabs/OfferSettings";
import UpdateOfferForm from "@/components/offering/Offers/forms/UpdateOfferForm";
import DisableOfferForm from "./forms/DisableOfferForm";
import OfferSettingsCheckoutURL from "./details/OfferSettingsCheckoutURL";
import { useFetchOffer } from "@/hooks/offering/offers";
import { useProtectPage } from "@/hooks/roles";
import { useFetchProducts } from "@/hooks/product-catalog/products";
import {
  activateOffer,
  duplicateOffer,
  patchOffer,
  publishOffer,
} from "@/lib/services";
import { Offer, Product } from "@/types";
import OfferDetailsHeader from "./details/OfferDetailsHeader";

type OfferIdProps = {
  offerId: string;
};

const OfferDetails = (params: OfferIdProps) => {
  if (!params.offerId) {
    throw new Error("Invalid offer ID");
  }
  const router = useRouter();
  const { canAccess } = useProtectPage("offer-details");
  const { message, modal } = App.useApp();

  const [isEditModalOpen, setEditModalOpen] = useState(false);
  const [isDisableModalOpen, setDisableModalOpen] = useState(false);

  // Queries
  const { data: products } = useFetchProducts();
  const {
    isLoading,
    data: offer,
    mutate: refetchOffer,
  } = useFetchOffer(params.offerId);

  const updateOffer = async (values: any) => {
    values.offerInfo.id = offer?.id;
    values.checkoutInfo.id = offer?.checkoutPage?.id;
    let result = await patchOffer(values);
    if (result) {
      message.open({
        type: "success",
        content: "Offer updated!",
      });
      setEditModalOpen(false);
      await refetchOffer();
    }
  };

  const activOffer = async () => {
    if (canAccess("offer-activate")) {
      modal.warning({
        title: "Are you sure you want to activate the offer? ",
        content:
          "By activating the offer, our customer will be able to purchase this offer.",
        okText: "Activate",
        cancelText: "Close",
        okCancel: true,
        onCancel: () => {},
        onOk: async () => {
          if (offer && offer.id && offer.checkoutPage?.id) {
            await activateOffer(offer.id, offer.checkoutPage.id);
            await refetchOffer();
          }
        },
      });
    }
  };

  const duplicate = async (includeProductDelivery: boolean) => {
    if (canAccess("offer-duplicate")) {
      if (offer) {
        const res = await duplicateOffer(offer, includeProductDelivery);
        if (res && res.id) {
          message.open({
            type: "success",
            content: "Offer duplicated!",
          });
          router.push(`/dashboard/offers/${res.chargebeeId}`);
        } else if (res.error && res.statusCode === 400) {
          modal.error({
            title: "Error",
            content: res?.message.map((item: any, index: number) => (
              <p key={index}>{item}</p>
            )),
            okText: "Ok",
            okCancel: true,
          });
        }
      }
    }
  };

  const handlePreview = () => {
    if (offer?.config) {
      window.open(
        `${process.env.NEXT_PUBLIC_CHECKOUT_BASEURL}${offer?.slug}?preview=true`,
        "_blank"
      );
    }
  };

  const handlePublish = async () => {
    if (canAccess("offer-publish")) {
      if (
        offer?.config &&
        offer?.id &&
        offer?.status === "active" &&
        offer?.version === "preview"
      ) {
        let res = await publishOffer(offer.id.toString());
        if (res === 201) {
          message.open({
            type: "success",
            content: "Your changes are published and live on the checkout page",
          });
          await refetchOffer();
        }
      } else {
        return;
      }
    }
  };

  const tabItems: TabsProps["items"] = useMemo(
    () => [
      {
        key: "1",
        label: "Settings",
        children: offer && (
          <OfferSettings
            offer={offer}
            refreshOffer={refetchOffer}
            products={products?.items as Product[]}
          />
        ),
      },
      {
        key: "2",
        label: "Checkout design",
        children: offer && (
          <CheckoutSettings offer={offer} updateCheckout={refetchOffer} />
        ),
      },
    ],
    [offer, products?.items]
  );

  return isLoading ? (
    <Skeleton />
  ) : (
    <div>
      <div>
        <OfferDetailsHeader
          offer={offer as Offer}
          onActivate={activOffer}
          onDisable={() => {
            if (canAccess("offer-disable")) {
              setDisableModalOpen(true);
            }
          }}
          onDuplicateWithProductDelivery={() => duplicate(true)}
          onDuplicateWithoutProductDelivery={() => duplicate(false)}
          onEdit={() => {
            if (canAccess("offer-update")) {
              setEditModalOpen(true);
            }
          }}
          onPreview={handlePreview}
          onPublish={handlePublish}
        />
        <Divider />
        <OfferSettingsCheckoutURL offer={offer as Offer} />
      </div>
      <Divider />
      <div style={{ padding: " 0 30px" }}>
        <Tabs defaultActiveKey="1" items={tabItems} />
      </div>
      {offer && offer.id && isDisableModalOpen ? (
        <DisableOfferForm
          isOpen={isDisableModalOpen}
          offerId={offer.id}
          handleCancel={() => {
            setDisableModalOpen(false);
          }}
          refetchOffer={refetchOffer}
        />
      ) : null}
      {offer && isEditModalOpen ? (
        <UpdateOfferForm
          isModalOpen={isEditModalOpen}
          handleAdd={updateOffer}
          handleCancel={() => setEditModalOpen(false)}
          initValues={{
            id: offer.id,
            name: offer.name,
            status: offer.status,
            externalName: offer.externalName,
            currency: "EUR",
            fiscalEntity: offer.fiscalEntity,
            description: offer.description,
            cardImage: offer.cardImage,
            offerBannerImage: offer.bannerImage,
            usp: offer.usp,
            image: offer.image,
            redirectUrl: offer.redirectUrl,
            bannerImage: offer.checkoutPage?.bannerImage,
            mainColor: offer.checkoutPage?.mainColor,
            slug: offer.slug,
            paymentGateway: offer.paymentGateway,
            template: offer.checkoutPage?.template,
            testimonyName: offer.checkoutPage?.testimony?.name,
            testimonyTitle: offer.checkoutPage?.testimony?.title,
            testimonyComment: offer.checkoutPage?.testimony?.comment,
            testimonyUrl: offer.checkoutPage?.testimony?.url,
            testimonyRating: offer.checkoutPage?.testimony?.rating,
            cancellationPolicy: offer.checkoutPage?.cancellationPolicy,
          }}
        />
      ) : null}
    </div>
  );
};

export default OfferDetails;
