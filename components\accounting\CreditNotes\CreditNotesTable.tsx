"use client";

import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Input,
  Row,
  Select,
  Table,
  Typography,
  Form,
  Divider,
} from "antd";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import paginationConfig from "../../form/paginationConfig";
import { getCreditNotesColumns } from "./CreditNotesColumns";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchCreditNotes } from "@/hooks/accounting/creditNotes";
import { useProtectPage } from "@/hooks/roles";
import { CreditNotesFilter } from "@/types";

const { Title } = Typography;

const initialFilters = {
  searchTerm: "",
  subscriptionId: "",
  statusFilter: "",
  fiscalEntityFilter: "",
};

const CreditNotesTable = () => {
  const router = useRouter();
  const [filters, setFilters] = useState<CreditNotesFilter>(initialFilters);

  // We initially had these two text types as part of the filters state but this
  // was causing an issue on first page load. After the initial load, the debounce
  // search wound not work for the first 2 searches (probably because of setting
  // the initial state of the filters to an empty string). We decided to move these
  // separate states so we can do proper debounce on them without interference from
  // the rest of the filter values. This is how it was done previously on products
  // table as well so this also bring uniformity to the codebase.
  const [searchQuery, setSearchQuery] = useState("");
  const [subscriptionFilter, setSubscriptionFilter] = useState("");

  const debouncedSearchTerm = useDebounce(searchQuery);
  const debouncedSubId = useDebounce(subscriptionFilter);

  useProtectPage("credit-notes-list");

  const [filterForm] = Form.useForm();

  const {
    isLoading,
    data: creditNotes,
    mutate: refetchCreditNotes,
  } = useFetchCreditNotes({
    filters: {
      ...filters,
      searchTerm: debouncedSearchTerm,
      subscriptionId: debouncedSubId,
    },
  });

  const filterCreditNotes = (changedValues: any) => {
    if (changedValues?.searchTerm || changedValues?.searchTerm === "") {
      setSearchQuery(changedValues.searchTerm);
    } else if (
      changedValues?.subscriptionId ||
      changedValues?.subscriptionId === ""
    ) {
      setSubscriptionFilter(changedValues.subscriptionId);
    } else {
      setFilters(filterForm.getFieldsValue());
    }
  };

  const resetFilters = () => {
    setFilters(initialFilters);
    setSearchQuery("");
    setSubscriptionFilter("");
  };

  const columns = React.useMemo(() => getCreditNotesColumns(router), [router]);

  const handleRowClick = (data: any) => {
    router.push(`/dashboard/credit-notes/${data.chargebeeId}`);
  };

  useEffect(() => {
    refetchCreditNotes();
  }, [
    debouncedSearchTerm,
    debouncedSubId,
    filters.statusFilter,
    filters.fiscalEntityFilter,
  ]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Credit Notes</Title>
      <Row style={{ lineHeight: "10px" }}>
        <Col span={24}>
          <Form form={filterForm} onValuesChange={filterCreditNotes}>
            <Form.Item name="searchTerm">
              <Input
                prefix={<SearchOutlined />}
                placeholder="Search by customer email"
              />
            </Form.Item>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <Form.Item name="subscriptionId" style={{ width: "25%" }}>
                <Input allowClear placeholder="Subscription Id" />
              </Form.Item>
              <Form.Item name="statusFilter" style={{ width: "25%" }}>
                <Select
                  allowClear
                  placeholder="Status"
                  options={[
                    { value: "refunded", label: <span>Refunded</span> },
                    { value: "adjusted", label: <span>Adjusted</span> },
                  ]}
                />
              </Form.Item>
              <Form.Item name="fiscalEntityFilter" style={{ width: "25%" }}>
                <Select
                  allowClear
                  placeholder="Billing entity"
                  options={[
                    {
                      value: "PG",
                      label: "Paradox Group",
                    },
                    {
                      value: "PI",
                      label: "Paradox Institute",
                    },
                  ]}
                />
              </Form.Item>
              <Button
                type="link"
                onClick={() => {
                  resetFilters();
                  filterForm.resetFields();
                }}
              >
                Clear Filters
              </Button>
            </div>
          </Form>
        </Col>
      </Row>
      <Divider />
      <Table
        dataSource={creditNotes?.items}
        columns={columns}
        scroll={{ x: 3000 }}
        size="small"
        pagination={paginationConfig}
        loading={isLoading}
        rowClassName={() => {
          return "paradox-table-row";
        }}
        onRow={(data, index) => {
          return {
            onClick: () => {
              handleRowClick(data);
            },
          };
        }}
        rowKey="id"
      />
    </div>
  );
};

export default CreditNotesTable;
