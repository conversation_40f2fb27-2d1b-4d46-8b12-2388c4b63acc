"use client";

import { InfoCircleTwoTone } from "@ant-design/icons";
import {
  App,
  Card,
  Col,
  Divider,
  Form,
  Modal,
  Row,
  Select,
  Typography,
} from "antd";

import { useUser } from "@/hooks/utils/useUser";
import { cancelSubscription } from "@/lib/services/chargebee";

const { Text } = Typography;

type CancelSubscriptionModalProps = {
  subscriptionId: string;
  isOpen: boolean;
  handleCancel: (shouldReload: boolean) => void;
};

const rules = [{ required: true, message: "This value is required!" }];

const cancelAtOptions = [
  { value: "now", label: "Cancel immediatly" },
  { value: "end_of_term", label: "Cancel at end of term" },
];

const cancelReasonOptions = [
  { value: "order_change_upsell", label: "Order change - Upsell" },
  { value: "order_change_downsell", label: "Order change - Downsell" },
  { value: "lost", label: "Lost" },
  { value: "duplicate_subscription", label: "Duplicate Subscription" },
  { value: "no_payment_retractation", label: "No payment - Retractation" },
  {
    value: "Paid In Full via Bank Transfer",
    label: "Paid In Full via Bank Transfer",
  },
];

export default function CancelSubscriptionModal({
  subscriptionId,
  isOpen,
  handleCancel,
}: CancelSubscriptionModalProps) {
  const { message } = App.useApp();
  const { isSignedIn, emailAddress } = useUser();
  const [form] = Form.useForm();

  const onFormSubmit = async (values: any) => {
    const cancelReasonLabel = cancelReasonOptions.find(
      (option) => option.value === values.cancel_reason
    )?.label as string;
    let response;
    if (!isSignedIn) {
      window.location.href = "/";
      return;
    }
    try {
      response = await cancelSubscription({
        id: subscriptionId,
        cancel_at: values.cancel_at,
        cancel_reason: cancelReasonLabel,
        user_email: emailAddress as string,
      });
      if (response.error) {
        message.open({
          type: "error",
          content: response.details.message,
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      handleCancel(!response.error);
    }
  };

  return (
    <Modal
      open={isOpen}
      closeIcon={null}
      width="40%"
      cancelText="Dismiss"
      cancelButtonProps={{ type: "link" }}
      okText="Cancel subscription"
      okButtonProps={{ danger: true }}
      onCancel={() => {
        handleCancel(false);
      }}
      onOk={() => {
        form.submit();
      }}
    >
      <Text strong>Cancel subscription</Text>
      <Text type="secondary">
        &nbsp;This action is irreversible; be sure about what you are doing all
      </Text>
      <Divider />

      <Card
        bodyStyle={{
          padding: "12px",
          boxShadow: "5px 8px 24px 5px rgba(255, 255, 255, 0.6)",
        }}
      >
        <Row>
          <Col span={1}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <InfoCircleTwoTone style={{ fontSize: 24 }} />
            </div>
          </Col>
          <Col span={22} offset={1}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              All the ‘Payment due’ & ‘Not paid’ invoices won’t be affected. If
              you want to write them off or refund them, you need to do it in
              the invoice itself.
            </div>
          </Col>
        </Row>
      </Card>
      <Form
        form={form}
        layout="vertical"
        style={{ marginTop: "20px", marginBottom: "20px" }}
        onFinish={onFormSubmit}
      >
        <Form.Item
          label="When do you want to cancel this subscription?"
          name="cancel_at"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select options={cancelAtOptions} />
        </Form.Item>
        <Form.Item
          label="Cancellation reason"
          name="cancel_reason"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select options={cancelReasonOptions} />
        </Form.Item>
      </Form>
    </Modal>
  );
}
