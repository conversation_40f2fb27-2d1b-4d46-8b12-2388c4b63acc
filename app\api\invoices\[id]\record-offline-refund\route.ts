import { initChargebee, offlineRefundInvoice } from "@/server/chargebee";
import { RecordOfflineRefundParams } from "@/types";
import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the POST request for recording an offline refund.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the returned response or an error message.
 */
export async function POST(req: Request) {
  try {
    let body = (await req.json()) as RecordOfflineRefundParams;
    if (body.id) {
      const response = await offlineRefundInvoice(body);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RECORD_REFUND", details: error },
      { status: 400 }
    );
  }
}
