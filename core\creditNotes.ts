import { CreditNote, InvoiceLineItemPX } from "@/types";

/**
 * Computes the adjustment amount based on the credit notes
 * @param creditNotes - The list of credit notes.
 * @param totalBillingCycles - The total number of billing cycles.
 * @returns The computed adjustment amount.
 */
export function computeAdjustment(creditNotes: CreditNote[]): number {
  // For each credit note, sum the amount allocated
  const result = creditNotes.reduce((acc: number, i) => {
    if (i.amountAllocated) acc += i.amountAllocated;
    return acc;
  }, 0);
  return result;
}

/**
 * Computes the refund amount based on the credit notes and total billing cycles.
 * @param creditNotes - The list of credit notes.
 * @param totalBillingCycles - The total number of billing cycles.
 * @returns The computed refund amount.
 */
export function computeRefund(creditNotes: CreditNote[]): number {
  // For each credit note, sum the amount refunded
  const result =
    creditNotes.reduce((acc: any, i) => {
      const cnLineItems = i?.lineItems;
      const linesSubTotal = cnLineItems?.reduce(
        (acc: number, item: InvoiceLineItemPX) => {
          if (item.amount) acc += item.amount;
          return acc;
        },
        0
      );
      acc += linesSubTotal;
      return acc;
    }, 0) || 0;
  return result;
}
