"use client";
import { ArrowLeftOutlined, CloseOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Row, Space, Typography } from "antd";

import { Warranty } from "@/types";

import React from "react";
import UpdateWarranty from "@/components/offering/Offers/warranty/EditWarranty";
import AddWarranty from "./AddWarranty";

type AddProps = {
  isModalOpen: boolean;
  handleCancel: Function;
  checkoutPageId: number | undefined;
  handleAdd: Function;
  warranties?: Warranty[];
  updateWarranties?: Function;
};

const { Text } = Typography;

const AddWarrantyForm = ({
  isModalOpen,
  handleCancel,
  checkoutPageId,
  handleAdd,
  warranties,
  updateWarranties,
}: AddProps) => {
  const handleAddWarranty = async (values: any) => {
    await handleAdd(values);
  };

  return (
    <Drawer
      width={"100vw"}
      open={isModalOpen}
      onClose={() => {
        handleCancel;
      }}
      closeIcon={false}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          paddingBottom: "40px",
        }}
      >
        <div>
          <Row
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              paddingRight: "30px",
              paddingLeft: "30px",
            }}
          >
            <div
              style={{
                display: "flex",
                width: "60%",
                justifyContent: "start",
                alignItems: "center",
              }}
            >
              <Space>
                <Button
                  icon={<ArrowLeftOutlined style={{ color: "#5B5B5D" }} />}
                  onClick={() => {
                    handleCancel();
                  }}
                />
                <Text style={{ fontSize: 24 }}>
                  Update template customisation
                </Text>
              </Space>
            </div>
            <Space>
              <Button
                icon={<CloseOutlined />}
                onClick={() => {
                  handleCancel();
                }}
              >
                <Text strong>Close</Text>
              </Button>
            </Space>
          </Row>
          <Divider />
          <div
            style={{
              width: "100vw",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <div
              className="paradox-card"
              style={{ width: "70%", padding: "30px" }}
            >
              <span>
                <Text strong style={{ fontSize: 16 }}>
                  Warranty Section
                </Text>
                &nbsp;&nbsp;
                <Text style={{ fontSize: 11, color: "gray" }}>
                  Based on template you have selected
                </Text>
              </span>
              <br />
              <br />
              <br />
              <div style={{ width: "100%" }}>
                {warranties &&
                  warranties.map((item, index) => {
                    return (
                      <div key={item.id}>
                        <UpdateWarranty
                          initialValues={item}
                          updateWarranties={updateWarranties}
                          warrantyCount={warranties.length}
                        />
                        <br />
                      </div>
                    );
                  })}
              </div>
              <Divider />
              <div style={{ width: "100%" }}>
                <AddWarranty
                  handleAdd={handleAddWarranty}
                  checkoutPageId={checkoutPageId}
                />
              </div>
              <br />
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default AddWarrantyForm;
