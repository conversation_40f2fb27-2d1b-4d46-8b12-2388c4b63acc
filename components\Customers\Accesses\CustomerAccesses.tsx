"use client";

import {
  Image,
  Collapse,
  Segmented,
  List,
  Typography,
  CollapseProps,
} from "antd";
import Link from "next/link";
import { useState, useEffect } from "react";

import AccessListItem from "./AccessListItem";

const { Text } = Typography;
export const LMS_ACCESS_TYPE = "Learnworlds Accesses";
export const COMMUNITY_ACCESS_TYPE = "Circle Accesses";

const CustomerAccesses = ({
  accesses,
  onAccessUpdateSuccess,
}: {
  accesses?: Record<string, any>;
  onAccessUpdateSuccess?: () => void;
}) => {
  const [currentTab, setCurrentTab] = useState<Record<string, string>>({});

  const items: CollapseProps["items"] = Object.entries(accesses || {}).map(
    ([subscriptionId, access]) => ({
      id: subscriptionId,
      label: (
        <>
          <Link href={`/dashboard/subscriptions/${subscriptionId}`}>
            {subscriptionId}
          </Link>
          <span>- {access.offerName}</span>
        </>
      ),
      children: (
        <>
          <Segmented
            options={[
              {
                label: (
                  <>
                    <Image
                      src="/images/logo/lw.jpeg"
                      width={16}
                      height={16}
                      preview={false}
                    />
                    &nbsp;
                    <span>{LMS_ACCESS_TYPE}</span>
                  </>
                ),
                value: LMS_ACCESS_TYPE,
              },
              {
                label: (
                  <>
                    <Image
                      src="/images/logo/circle.png"
                      width={16}
                      height={16}
                      preview={false}
                    />
                    &nbsp;
                    <span>{COMMUNITY_ACCESS_TYPE}</span>
                  </>
                ),
                value: COMMUNITY_ACCESS_TYPE,
              },
            ]}
            value={currentTab[subscriptionId] || LMS_ACCESS_TYPE}
            onChange={(value) =>
              setCurrentTab((prev) => ({
                ...prev,
                [subscriptionId]: value as string,
              }))
            }
            style={{ marginBottom: 16 }}
          />

          <List
            bordered
            dataSource={
              (currentTab[subscriptionId] || LMS_ACCESS_TYPE) ===
              LMS_ACCESS_TYPE
                ? access.lmsAccesses
                : access.communityAccesses
            }
            renderItem={(item) => (
              <AccessListItem
                item={item}
                activeSegment={currentTab[subscriptionId]}
                onAccessUpdateSuccess={onAccessUpdateSuccess}
              />
            )}
          />
        </>
      ),
    })
  );

  useEffect(() => {
    if (accesses) {
      setCurrentTab(
        Object.fromEntries(
          Object.keys(accesses).map((id) => [
            id,
            currentTab[id] || LMS_ACCESS_TYPE,
          ])
        )
      );
    } else {
      setCurrentTab({});
    }
  }, [accesses]);

  if (!accesses || Object.keys(accesses).length === 0) {
    return (
      <Text>
        Customer has no accesses for this subscription or we don&apos;t have
        them on record
      </Text>
    );
  }

  return <Collapse items={items} />;
};

export default CustomerAccesses;
