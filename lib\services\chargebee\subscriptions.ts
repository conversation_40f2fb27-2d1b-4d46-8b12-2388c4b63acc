import {
  CancelSubscriptionParams,
  ChangeDefaultPaymentMethodParams,
  CreateSubscriptionParams,
  EditNextBillingDateParams,
  EstimateSubscriptionParams,
} from "@/types";
import { requestChargebee } from ".";
import { Subscription } from "chargebee";

/**
 * Retrieves a subscription by its ID.
 * @param id - The ID of the subscription to retrieve.
 * @returns A Promise that resolves to the subscription data.
 */
export async function getChargebeeSubscription(
  id: string
): Promise<Subscription> {
  return requestChargebee(`/api/subscriptions/${id}`, "GET");
}

/**
 * Retrieves the subscription history for a given ID.
 * @param id - The ID of the subscription.
 * @returns A Promise that resolves to the subscription history data.
 */
export async function getSubscriptionHistory(id: string) {
  return requestChargebee(`/api/subscriptions/${id}/history`, "GET");
}

/**
 * Cancels a subscription.
 *
 * @param id - The ID of the subscription to cancel.
 * @param cancel_at - The date at which the subscription should be canceled.
 * @param cancel_reason - The reason for canceling the subscription.
 * @returns A Promise that resolves to the result of the cancellation request.
 */
export async function cancelSubscription(payload: CancelSubscriptionParams) {
  return requestChargebee(
    `/api/subscriptions/${payload.id}/cancel`,
    "POST",
    payload
  );
}

/**
 * Edits the next billing date of a subscription.
 * @param id - The ID of the subscription.
 * @param date - The new billing date.
 * @param comments - Additional comments for the edit.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export async function editNextBillingDate(payload: EditNextBillingDateParams) {
  return requestChargebee(
    `/api/subscriptions/${payload.id}/edit-next-billing-date`,
    "POST",
    payload
  );
}

/**
 * Retrieves comments for a specific subscription ID.
 * @param id - The ID of the subscription.
 * @returns A Promise that resolves to the comments data.
 */
export async function getComments(id: string) {
  return requestChargebee(`/api/subscriptions/${id}/comments`, "GET");
}

/**
 * Changes the default payment method for a subscription.
 *
 * @param id - The ID of the subscription.
 * @param payment_method - The new payment method.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export async function changeDefaultPaymentMethod(
  payload: ChangeDefaultPaymentMethodParams
) {
  return requestChargebee(
    `/api/subscriptions/${payload.id}/change-default-payment-method`,
    "POST",
    payload
  );
}

/**
 * Creates a subscription by sending a POST request to the specified API endpoint.
 * @param values - The values to be sent in the request body.
 * @returns A Promise that resolves to the response JSON or rejects with an error.
 */
export async function createSubscription(payload: CreateSubscriptionParams) {
  return requestChargebee(`/api/subscriptions/create`, "POST", payload);
}

/**
 * Estimates the subscription based on the provided payload.
 * @param payload - The payload containing the necessary data for estimating the subscription.
 * @returns A Promise that resolves to the estimated subscription.
 */
export async function estimateSubscription(
  payload: EstimateSubscriptionParams
) {
  return requestChargebee(`/api/subscriptions/estimate`, "POST", payload);
}
