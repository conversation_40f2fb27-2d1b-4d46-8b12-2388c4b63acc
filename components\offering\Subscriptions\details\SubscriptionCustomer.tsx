"use client";

import { Descriptions, Space, Tag, Typography } from "antd";
import { Customer, Subscription } from "chargebee";
import { usePathname, useRouter } from "next/navigation";

const { Text } = Typography;

type SubscriptionCustomerProps = {
  customer: Customer;
  subscription: Subscription;
};

const CustomerShippingAddress = ({
  customer,
  subscription,
}: {
  customer: Customer;
  subscription: Subscription;
}) => {
  return subscription.shipping_address ? (
    <Space direction="vertical">
      <Space direction="horizontal">
        <Text strong>
          {subscription.shipping_address.first_name}&nbsp;
          {subscription.shipping_address.last_name}
        </Text>
        <Text>{customer.email}</Text>
        <Text>{customer.phone}</Text>
      </Space>
      <Space direction="horizontal">
        <Text>{subscription.shipping_address.line1}</Text>
        <Text>{subscription.shipping_address.line2}</Text>
        <Text>{subscription.shipping_address.zip}</Text>
        <Text>{subscription.shipping_address.country}</Text>
        <Text>{subscription.shipping_address.state}</Text>
      </Space>
    </Space>
  ) : (
    <div style={{ color: "gray" }}>(No shipping address)</div>
  );
};

const CustomerBillingAddress = ({ customer }: { customer: Customer }) => {
  return (
    <Space direction="vertical">
      {customer.billing_address && (
        <Space direction="vertical">
          <Space direction="horizontal">
            <Text strong>
              {customer.billing_address.first_name}&nbsp;
              {customer.billing_address.last_name}
            </Text>
            <Text>{customer.email}</Text>
            <Text>{customer.phone}</Text>
          </Space>
          <Space direction="horizontal">
            <Text>{customer.billing_address.line1}</Text>
            <Text>{customer.billing_address.line2}</Text>
            <Text>{customer.billing_address.zip}</Text>
            <Text>{customer.billing_address.country}</Text>
            <Text>{customer.billing_address.state}</Text>
          </Space>
        </Space>
      )}
    </Space>
  );
};

export default function SubscriptionCustomer({
  customer,
  subscription,
}: SubscriptionCustomerProps) {
  const router = useRouter();
  const pathname = usePathname();

  const openDrawer = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    e.stopPropagation();
    router.push(pathname + "?customer=" + customer.id);
  };

  if (!customer) {
    return null;
  }
  return (
    <Descriptions
      title={
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <span>Customer</span>
          <a href="#" onClick={openDrawer}>
            See more
          </a>
        </div>
      }
      bordered
      column={1}
      size="small"
    >
      <Descriptions.Item label="First name">
        <Text>{customer.billing_address?.first_name || "(empty)"}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Last name">
        <Text>{customer.billing_address?.last_name || "(empty)"}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Email">
        <Text>{customer.email}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Phone number">
        <Text>{customer.billing_address?.phone || "(empty)"}</Text>
      </Descriptions.Item>
      <Descriptions.Item label="Company">
        <Text>{customer.billing_address?.company || "(empty)"} </Text>
      </Descriptions.Item>
      <Descriptions.Item label="Billing address">
        <CustomerBillingAddress customer={customer} />
      </Descriptions.Item>
      <Descriptions.Item label="Shipping address">
        <CustomerShippingAddress
          customer={customer}
          subscription={subscription}
        />
      </Descriptions.Item>
    </Descriptions>
  );
}
