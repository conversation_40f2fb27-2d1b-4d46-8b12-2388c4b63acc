"use client";

import { useEffect, useMemo, useState } from "react";
import { Col, Row, Skeleton } from "antd";
import { Invoice as ChargebeeInvoice } from "chargebee";

import {
  useFetchInvoice,
  useFetchInvoicePayments,
} from "@/hooks/accounting/invoices";
import { InvoiceInfo } from "../../Invoices/details/InvoiceInfo";
import { PX_Subscription } from "@/types";
import { getSubscriptionParadoxApi } from "@/lib/services/subscriptions";
import InvoiceCreditNotes from "../../Invoices/InvoiceCreditNotes";
import InvoicePayments from "../../Invoices/InvoicePayments";
import { getInvoiceColumns } from "../../Invoices/details/InvoiceColumns";

type InvoiceIssuedCreditNote = ChargebeeInvoice.IssuedCreditNote;

type InvoiceIdProps = {
  invoiceId: string;
};

const InvoiceDisplay = ({ invoiceId }: InvoiceIdProps) => {
  const [invoiceInfo, setInvoiceInfo] = useState<any>();
  const [subscription, setSubscription] = useState<
    PX_Subscription | undefined
  >();

  // Queries
  const {
    data: invoice,
    isLoading: invoiceLoading,
    mutate: refetchInvoice,
  } = useFetchInvoice(invoiceId);
  const {
    data: payments,
    isLoading: paymentsLoading,
    mutate: refetchPayments,
  } = useFetchInvoicePayments(invoice?.chargebeeId as string);

  const mappedPayments = useMemo(() => {
    return payments?.list.map((record) => {
      return record.transaction;
    });
  }, [payments]);

  useEffect(() => {
    if (!invoice) return;
    // Initialize totals for all line items
    let totalExclVatAllLineItems = 0; // Total amount excluding VAT
    let allVat = 0; // Total VAT amount
    let totalInclVat = 0; // Total amount including VAT

    invoice.lineItems &&
      invoice.lineItems.forEach((record) => {
        // Calculate discount amount excluding tax
        let discountAmountTaxExcluded = 0;
        if (
          invoice.priceType === "tax_inclusive" && // If prices include tax
          record.discount_amount &&
          record.tax_rate
        ) {
          // Remove tax component from discount amount
          discountAmountTaxExcluded =
            record.discount_amount / (1 + record.tax_rate / 100);
        } else if (record.discount_amount) {
          // If tax exclusive, use discount amount as is
          discountAmountTaxExcluded = record.discount_amount;
        }

        // Calculate unit amount excluding tax
        let unitAmountTaxExcluded = 0;
        if (
          invoice?.priceType === "tax_inclusive" && // If prices include tax
          record.amount &&
          record.tax_rate
        ) {
          // Remove tax component from unit amount
          unitAmountTaxExcluded = record.amount / (1 + record.tax_rate / 100);
        } else if (record.amount) {
          // If tax exclusive, use unit amount as is
          unitAmountTaxExcluded = record.amount;
        }

        // Add tax amount to total VAT
        if (record.tax_amount) {
          allVat += record.tax_amount;
        }

        // Calculate net amount (unit price - discount) excluding VAT
        totalExclVatAllLineItems +=
          unitAmountTaxExcluded - discountAmountTaxExcluded;
      });

    // Calculate final total including VAT
    totalInclVat = totalExclVatAllLineItems + allVat;

    // Update state with calculated totals
    setInvoiceInfo({
      totalExclVatAllLineItems,
      allVat,
      totalInclVat,
    });
  }, [invoice]);

  const columns = getInvoiceColumns(invoice);

  const computeCreditNoteSum = (notes: InvoiceIssuedCreditNote[]): number => {
    let sum = 0;
    notes.forEach((note) => {
      sum += note.cn_total ? note.cn_total : 0;
    });
    return sum;
  };

  const fetchSubscription = async () => {
    if (!invoice?.subscriptionId) return;
    const response = await getSubscriptionParadoxApi(
      invoice?.subscriptionId as string
    );
    setSubscription(response);
  };

  useEffect(() => {
    if (!invoiceId) return;
    Promise.all([fetchSubscription(), refetchInvoice(), refetchPayments()]);
  }, [invoiceId]);

  return !invoice ? (
    <Skeleton />
  ) : (
    <>
      <Row>
        <Col span={24} style={{ paddingRight: "20px" }}>
          {invoiceLoading ? (
            <Skeleton />
          ) : (
            <InvoiceInfo
              invoice={invoice}
              subscription={subscription as PX_Subscription}
              invoiceInfo={invoiceInfo}
              columns={columns}
            />
          )}
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          {paymentsLoading ? (
            <Skeleton />
          ) : (
            <InvoicePayments invoice={invoice} payments={mappedPayments} />
          )}
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <InvoiceCreditNotes invoice={invoice} />
        </Col>
      </Row>
    </>
  );
};

export default InvoiceDisplay;
