export const dynamic = "force-dynamic";

import { Metadata } from "next";

import DiscountDetails from "@/components/offering/Discounts/DiscountDetails";

export const metadata: Metadata = {
  title: "Discount Details",
};

type DiscountDetailsProps = {
  discountId: string;
};

export default async function DiscountDetailsPage(
  props: {
    params: Promise<DiscountDetailsProps>;
  }
) {
  const params = await props.params;
  return <DiscountDetails discountId={params.discountId} />;
}
