"use client";

import { App, Divider, Form, Table, Typography } from "antd";
import React, { useEffect, useState, useMemo, useCallback } from "react";

import UpdateProductLineForm from "@/components/product-catalog/ProductLines/forms/UpdateProductLineForm";
import CreateProductLineForm from "./forms/CreateProductLineForm";
import ProductLinesFilterTool from "./ProductLinesFilterTool";
import { getProductLinesTableColumns } from "./ProductLinesTableColumns";
import { useFetchProductLines } from "@/hooks/product-catalog/productLines";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useProtectPage } from "@/hooks/roles";
import { createProductLine, updateProductLine } from "@/lib/services";
import { ProductLine, ProductLineFilters } from "@/types";

export type Filter = ProductLineFilters & {
  current?: ProductLine;
};

const { Title } = Typography;

const ProductLinesTable = () => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const { canAccess } = useProtectPage("product-lines-list");

  const [filters, setFilters] = useState<Filter>({ search: "", status: null });
  const searchTerm = useDebounce(filters.search);
  // Modals
  const [creatingPL, setCreatingPL] = useState(false);
  const [updatingPL, setUpdatingPL] = useState(false);

  // Queries
  const {
    isLoading,
    data: productLines,
    mutate: refetchProductLines,
  } = useFetchProductLines({
    filters: {
      status: filters.status,
      search: searchTerm,
    },
  });

  const handleAdd = useCallback(
    async (values: any) => {
      try {
        const hideMessage = message.loading("Adding new product line");
        const res = await createProductLine({
          ...values,
          status: "active",
        });
        form.resetFields();
        setCreatingPL(false);
        if (res) {
          message.open({
            type: "success",
            content: "New product line added!",
          });
        }
        hideMessage();
        await refetchProductLines();
      } catch (error) {
        message.open({
          type: "error",
          content: "Failed to add product line",
        });
      }
    },
    [form, message, refetchProductLines]
  );

  const handleUpdate = useCallback(
    async (values: any) => {
      try {
        const hideMessage = message.loading("Editing product line");
        const res = await updateProductLine(values);
        setUpdatingPL(false);
        if (res) {
          message.open({
            type: "success",
            content: "Product line updated!",
          });
        }
        hideMessage();
        await refetchProductLines();
      } catch (error) {
        message.open({
          type: "error",
          content: "Failed to update product line",
        });
      }
    },
    [message, refetchProductLines]
  );

  const columns = getProductLinesTableColumns({
    onEdit: (productLine) => {
      setFilters((prev) => ({ ...prev, current: productLine }));
      setUpdatingPL(true);
    },
  });

  const filterToolProps = useMemo(
    () => ({
      filters,
      setFilters,
      onCreatePL: () => {
        if (canAccess("product-line-create")) {
          setCreatingPL(true);
        }
      },
    }),
    [filters, canAccess]
  );

  useEffect(() => {
    refetchProductLines();
  }, [refetchProductLines, filters.status, searchTerm]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>Product Lines</Title>
      <ProductLinesFilterTool {...filterToolProps} />
      <Divider />
      <Table
        dataSource={productLines?.items}
        loading={isLoading}
        columns={columns}
        rowKey="id"
        size="small"
        rowClassName={() => {
          return "paradox-table-row";
        }}
      />
      {creatingPL && (
        <CreateProductLineForm
          isOpen={creatingPL}
          onAddNewPL={handleAdd}
          onDismiss={() => setCreatingPL(false)}
        />
      )}
      {updatingPL && (
        <UpdateProductLineForm
          isModalOpen={updatingPL}
          handleUpdate={handleUpdate}
          formValues={filters.current}
          handleCancel={() => setUpdatingPL(false)}
        />
      )}
    </div>
  );
};

export default React.memo(ProductLinesTable);
