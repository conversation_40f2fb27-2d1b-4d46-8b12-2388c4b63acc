"use client";

import {
  App,
  Col,
  Divider,
  InputNumber,
  Radio,
  Row,
  Image,
  Space,
  Tag,
  Typography,
  Form,
  Table,
  Descriptions,
} from "antd";
import { useEffect, useMemo, useState } from "react";

import {
  getSubscriptionOffer,
  getSubscriptionProducts,
  updateSubscriptionBillingCycles,
} from "@/lib/services";
import EditSubscriptionOrderSummary from "../details/EditSubscriptionOrderSummary";
import {
  ChangeSubscriptionPageState,
  Invoice,
  LineItemWithPrice,
  SubscriptionHistoricalData,
  UpdateBillingCyclesParams,
} from "@/types";
import { formatCents } from "@/lib/currency";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";

type EditBillingCyclesProps = {
  isModalOpen: boolean;
  closeModal: Function;
  subscriptionId: string;
  history: SubscriptionHistoricalData;
  nextBillingDate: string;
  remainingBillingCycles?: number;
  onUpdate?: Function;
};

const { Text } = Typography;

const EditBillingCycles = ({
  isModalOpen,
  closeModal,
  subscriptionId,
  history,
  nextBillingDate,
  onUpdate,
}: EditBillingCyclesProps) => {
  const { message } = App.useApp();
  const [billingCycleForm] = Form.useForm();
  const [pageState, setPageState] = useState<ChangeSubscriptionPageState>({
    selectedBillingCycle: 0,
    configuredBillingCycles: [],
    oldProdConfig: [],
    newProdConfig: [],
    oldBillingCycle: 0,
    offerName: "",
    details: {
      amountPaid: 0,
      total: 0,
      remaining: 0,
    },
  });

  const isImportedSubscription = useMemo(() => {
    return subscriptionId.includes("PX_MIG");
  }, [subscriptionId]);

  useEffect(() => {
    Promise.all([getProducts(), getOffer()]);
  }, []);

  const getProducts = async () => {
    const offerProds = await getSubscriptionProducts(subscriptionId);
    if (offerProds?.products && offerProds?.products.length > 0) {
      let remainingBillingCycles =
        offerProds?.products[0]?.billingCyclesRemaining || 0;

      // Calculating the total amounts for the current subscription
      const paid = history.invoices.reduce((acc: number, item: Invoice) => {
        // @ts-ignore
        acc += item?.amountPaid;
        return acc;
      }, 0);
      const linesTotal = offerProds?.products.reduce(
        (acc: number, item: LineItemWithPrice) => {
          // @ts-ignore
          acc += item?.amountPerBillingCycle - item?.discount_amount;
          return acc;
        },
        0
      );
      const remaining = linesTotal * remainingBillingCycles;
      const total = paid + remaining;
      setPageState({
        ...pageState,
        oldBillingCycle: remainingBillingCycles + history.invoices.length,
        selectedBillingCycle:
          offerProds.existingBillingCycles ?? remainingBillingCycles,
        details: { amountPaid: paid, total, remaining },
        oldProdConfig: offerProds?.products.map((prod: LineItemWithPrice) => {
          if (!prod?.amountPerBillingCycle || !prod?.billingCyclesRemaining) {
            return {};
          }
          return {
            id: prod.entity_id,
            image: prod.image,
            name: prod.name,
            billingCycles: prod?.billingCyclesRemaining,
            pricePerBillingCycle: prod?.amountPerBillingCycle,
            quantity: prod.quantity,
            total:
              prod?.billingCyclesRemaining *
              (prod?.amountPerBillingCycle / 100),
          };
        }),
        newProdConfig: offerProds?.products.map((prod: LineItemWithPrice) => {
          if (!prod?.amountPerBillingCycle || !prod?.billingCyclesRemaining) {
            return {};
          }
          return {
            id: prod.entity_id,
            image: prod.image,
            name: prod.name,
            billingCycles: prod?.billingCyclesRemaining,
            pricePerBillingCycle: prod?.amountPerBillingCycle,
            quantity: prod.quantity,
            total:
              prod?.billingCyclesRemaining *
              (prod?.amountPerBillingCycle / 100),
          };
        }),
      });
    }
    if (isImportedSubscription) {
      setPageState({
        ...pageState,
        oldBillingCycle: offerProds[0]?.billing_cycles,
        selectedBillingCycle: offerProds[0]?.billing_cycles,
        newProdConfig: [
          {
            id: offerProds[0]?.item_price_id,
            image: offerProds[0]?.image,
            name: offerProds[0]?.item_price_id,
            billingCycles: offerProds[0]?.billing_cycles,
            pricePerBillingCycle: offerProds[0]?.unit_price,
            quantity: offerProds[0]?.quantity,
            total: (offerProds[0]?.unit_price * offerProds[0]?.quantity) / 100,
          },
        ],
      });
    }
  };

  const getOffer = async () => {
    const offer = await getSubscriptionOffer(subscriptionId);
    setPageState((prev) => {
      return {
        ...prev,
        configuredBillingCycles:
          isImportedSubscription || offer?.error ? [] : offer?.billingCycles,
        offerName:
          isImportedSubscription || offer?.error
            ? "Imported subscription"
            : offer?.offerName,
      };
    });
  };

  const handlePriceChange = (price: number, record: any) => {
    // Create a copy of the product configuration array to maintain immutability
    const prodConfig = [...pageState.newProdConfig];
    const index = prodConfig.findIndex((prod) => prod.id === record.id);

    // Convert the input price to cents (multiply by 100)
    prodConfig[index].pricePerBillingCycle = price * 100;
    const productQuantity = prodConfig[index]?.quantity;

    // Calculate the total price for this product:
    // price per cycle * quantity * number of billing cycles
    if (productQuantity && pageState.selectedBillingCycle) {
      prodConfig[index].total =
        price * productQuantity * pageState.selectedBillingCycle;
    }

    setPageState((prev) => {
      return {
        ...prev,
        newProdConfig: prodConfig,
      };
    });
  };

  const handleBillingCycleChange = (val: number) => {
    billingCycleForm.setFieldValue("billingCycleRadio", val ? val : "0");
    setPageState({
      ...pageState,
      selectedBillingCycle: val ? Number(val) : 1,
    });

    updateNewProdConfig(val ? Number(val) : 1);
  };

  const handleCustomBillingCycleChanged = (val: any) => {
    setPageState({
      ...pageState,
      selectedBillingCycle: val,
    });
    updateNewProdConfig(val);
  };

  const updateNewProdConfig = (billingCycle: number) => {
    setPageState((prev) => {
      let prodConfig = [...prev.newProdConfig];
      let remainingAmount = prev.details.remaining;
      prodConfig.forEach((prod) => {
        let originalProd = prev.oldProdConfig.find((x) => x.id === prod.id);
        // We assing the new calculated amount based on the percentage value of
        // each product in the subscription
        let percentage = 0;
        if (originalProd?.total) {
          percentage = (originalProd.total / prev.details.total) * 100;
        }
        // If there is only one product, set percentage to 100
        if (prodConfig.length === 1) {
          percentage = 1;
        }
        if (prod?.quantity && prod?.pricePerBillingCycle) {
          prod.pricePerBillingCycle =
            (remainingAmount / billingCycle) * percentage;
          prod.total =
            (prod.pricePerBillingCycle / 100) * prod.quantity * billingCycle;
        }
        prod.billingCycles = billingCycle;
      });
      return {
        ...prev,
        newProdConfig: prodConfig,
      };
    });
  };

  const updateSubscription = async () => {
    const params: UpdateBillingCyclesParams = {
      subscriptionId: subscriptionId,
      updatedBillingCycles: pageState.selectedBillingCycle,
      updatedItems: pageState.newProdConfig.map((prod) => {
        return {
          id: prod.id,
          updatedAmount: Number(prod.pricePerBillingCycle?.toFixed(0)),
          updatedBillingCycles: pageState.selectedBillingCycle,
          updatedQuantity: prod.quantity,
        };
      }),
    };
    const res = await updateSubscriptionBillingCycles(params);
    if (res) {
      message.open({
        type: "success",
        content: "Subscription updated successfully",
      });
      closeModal();
      onUpdate?.();
    }
  };

  const columns = [
    {
      title: "Product",
      key: "id",
      render: (record: any) => {
        return (
          <Space>
            {record.image ? (
              <Image
                style={{ borderRadius: "8px" }}
                src={record.image}
                width={50}
                height={50}
              />
            ) : null}
            <Text>{record.name}</Text>
          </Space>
        );
      },
    },
    {
      title: "Billing cycles",
      key: "id",
      render: (record: any) => {
        return pageState.selectedBillingCycle;
      },
    },
    {
      title: "Price per billing cycle",
      dataIndex: "pricePerBillingCycle",
      key: "id",
      render: (text: number, record: any) => {
        return (
          <>
            <InputNumber
              step={0.01}
              style={{ width: "150px" }}
              value={text / 100}
              decimalSeparator=","
              onChange={(e) => handlePriceChange(e ?? 0, record)}
              addonAfter="€"
            />
            {!text ? (
              <>
                <br />
                <Text type="danger" style={{ fontSize: 12 }}>
                  The price per billing cycle is not set
                </Text>
              </>
            ) : null}
          </>
        );
      },
    },
    {
      title: "Quantity",
      dataIndex: "quantity",
      key: "id",
    },
    {
      title: "Total",
      dataIndex: "total",
      key: "id",
      render: (val: number) => {
        return formatCents(val * 100);
      },
    },
  ];

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      width="100vw"
      onClose={() => {
        closeModal();
      }}
      onSubmit={() => {
        updateSubscription();
      }}
      title="Edit a subscription"
      submitText="Update Subscription"
      onCancel={() => {
        closeModal();
      }}
    >
      <Row style={{ padding: 20 }}>
        <Col span={13}>
          <Descriptions title="" size="small" column={1} bordered>
            <Descriptions.Item
              label={
                <>
                  <span>Selected offer</span>
                  <br />
                  <small style={{ color: "darkgray" }}>
                    Selected offer when the subscription was processed
                  </small>
                </>
              }
            >
              <Text>{pageState.offerName}</Text>
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <>
                  <span>Billing cycles</span>
                  <br />
                  <small style={{ color: "darkgray" }}>
                    On which number of billing cycles do you agree with the
                    customer?
                  </small>
                </>
              }
            >
              <Form form={billingCycleForm}>
                <div style={{ marginBottom: "5px" }}>
                  <Form.Item name="billingCycleRadio" noStyle>
                    <Radio.Group
                      onChange={(e) => {
                        handleBillingCycleChange(e.target.value);
                      }}
                    >
                      <Space direction="vertical">
                        {pageState.configuredBillingCycles.map((cycle) => {
                          return (
                            <Radio key={cycle} value={cycle}>
                              {cycle} Billing Cycles
                              {cycle ===
                              pageState.oldBillingCycle?.toString() ? (
                                <Tag
                                  color="#CFCDCA"
                                  style={{
                                    color: "black",
                                    fontSize: 11,
                                    marginLeft: "10px",
                                  }}
                                >
                                  Current
                                </Tag>
                              ) : null}
                            </Radio>
                          );
                        })}
                        <Radio value={"0"}>Custom</Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </div>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.billingCycleRadio !==
                    currentValues.billingCycleRadio
                  }
                >
                  {({ getFieldValue }) =>
                    getFieldValue("billingCycleRadio") === "0" ? (
                      <div style={{ marginLeft: "15px", marginTop: "10px" }}>
                        <InputNumber
                          min={1}
                          defaultValue={1}
                          onChange={(e) => {
                            handleCustomBillingCycleChanged(e);
                          }}
                        />
                      </div>
                    ) : null
                  }
                </Form.Item>
              </Form>
            </Descriptions.Item>
          </Descriptions>
          <Divider />
          <Table
            title={() => {
              return (
                <Text style={{ fontSize: 18, fontWeight: 500 }}>
                  Product configuration
                </Text>
              );
            }}
            dataSource={pageState.newProdConfig}
            columns={columns}
            pagination={false}
            rowKey={(record) => record.id?.toString() || "sad"}
            size="small"
          />
        </Col>
        <Col span={10} offset={1}>
          <EditSubscriptionOrderSummary
            isImportedSubscription={isImportedSubscription}
            nextBillingDate={nextBillingDate}
            history={history}
            pageState={pageState}
          />
        </Col>
      </Row>
    </DrawerFormContainer>
  );
};

export default EditBillingCycles;
