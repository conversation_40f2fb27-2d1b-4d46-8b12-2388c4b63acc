"use client";

import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Col, Input, Row, Select } from "antd";
import { Filter } from "./ProductLinesTable";

type ProductLinesFilterParams = {
  filters: Filter;
  setFilters: Function;
  onCreatePL: Function;
};

export default function ProductLinesFilterTool({
  filters,
  setFilters,
  onCreatePL,
}: ProductLinesFilterParams) {
  return (
    <>
      <Row gutter={[0, 0]} style={{ lineHeight: "10px" }}>
        <Col xs={12} sm={12} md={16} lg={16} xl={17} xxl={19}>
          <Input
            prefix={<SearchOutlined />}
            placeholder="Search for name"
            onChange={(event) =>
              setFilters({ ...filters, search: event.target.value })
            }
            value={filters.search}
          />
        </Col>
        <Col offset={1} xs={11} sm={11} md={7} lg={7} xl={6} xxl={4}>
          <Button
            type="primary"
            style={{ width: "100%" }}
            icon={<PlusOutlined />}
            onClick={() => onCreatePL(true)}
            data-testid="create-product-line-btn"
          >
            Create Product Line
          </Button>
        </Col>
      </Row>
      <div
        style={{
          display: "flex",
          marginBottom: "30px",
          marginTop: "10px",
        }}
      >
        <Select
          value={filters.status}
          allowClear
          style={{ width: "200px" }}
          onChange={(values) => setFilters({ ...filters, status: values })}
          placeholder="Status"
          options={[
            { value: "active", label: <span>Active</span> },
            { value: "draft", label: <span>Draft</span> },
          ]}
        />
        <Button
          type="link"
          onClick={() => {
            setFilters((prev: Filter) => ({
              ...prev,
              search: "",
              status: null,
            }));
          }}
        >
          Clear Filters
        </Button>
      </div>
    </>
  );
}
