"use client";

import { Button } from "antd";

import { Space } from "antd";

type UpdateExternalEntitiesListProps = {
  onUpdateCourseList: () => void;
  onUpdateCommunityList: () => void;
};

export default function UpdateExternalEntitiesList({
  onUpdateCourseList,
  onUpdateCommunityList,
}: UpdateExternalEntitiesListProps) {
  return (
    <Space>
      <Button
        size="small"
        type="primary"
        onClick={() => {
          onUpdateCourseList();
        }}
      >
        Update course list
      </Button>
      <Button
        size="small"
        type="primary"
        onClick={() => {
          onUpdateCommunityList();
        }}
      >
        Update community info
      </Button>
    </Space>
  );
}
