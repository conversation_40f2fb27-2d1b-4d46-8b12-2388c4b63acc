import useSWR from "swr";

import { getWebhooks } from "@/lib/services";
import { WebhooksListResponse, UseFetchWebhooksParams } from "@/types";

/**
 * Custom hook to fetch webhooks.
 *
 * @param params - The parameters for fetching webhooks.
 * @returns The SWR object containing the webhooks data.
 */
export const useFetchWebhooks = (params: UseFetchWebhooksParams) => {
  return useSWR<WebhooksListResponse>(
    ["webhooks", params.filters],
    () => getWebhooks(params.filters),
    params?.options
  );
};
