"use client";

import { Tag, Typography } from "antd";

import { Invoice } from "@/types";
import { capitalizeWord } from "@/lib/text";
import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";

type InvoiceDropDownItemProps = {
  invoice: Invoice | undefined;
};

const { Text } = Typography;

const generateStatusTag = (status: string | undefined) => {
  if (!status) {
    return <Tag>Unknown</Tag>;
  }
  switch (status) {
    case "paid":
      return <Tag color="green">Paid</Tag>;
    case "not_paid":
      return <Tag color="orange">Not paid</Tag>;
    case "payment_due":
      return <Tag color="red">Payment due</Tag>;
    default:
      return <Tag>{capitalizeWord(status)}</Tag>;
  }
};

const InvoiceDropDownItem = ({ invoice }: InvoiceDropDownItemProps) => {
  if (!invoice) {
    return <div>Hamza</div>;
  }
  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text style={{ fontSize: 16 }}>
          Invoice for {formatCents(invoice.total as number)}
        </Text>
        {generateStatusTag(invoice.status)}
      </div>
      <div style={{ marginTop: "5px" }}>
        <Text style={{ color: "gray", fontSize: 13 }}>Dated: </Text>
        <Text style={{ fontSize: 12 }}>{formatDateFromUnix(invoice.date)}</Text>
        <br />
        <Text style={{ color: "gray", fontSize: 13 }}>Customer name: </Text>
        <Text style={{ fontSize: 12 }}>{invoice.customerName}</Text>
        <br />
        <Text style={{ color: "gray", fontSize: 13 }}>Customer email: </Text>
        <Text style={{ fontSize: 12 }}>{invoice.customerEmail}</Text>
        <br />
        <Text style={{ color: "gray", fontSize: 13 }}>Subscription #: </Text>
        <Text style={{ fontSize: 12 }}>{invoice.subscriptionId}</Text>
        <br />
        <Text style={{ color: "gray", fontSize: 13 }}>Invoice #: </Text>
        <Text style={{ fontSize: 12 }}>{invoice.chargebeeId}</Text>
        <br />
      </div>
    </div>
  );
};

export default InvoiceDropDownItem;
