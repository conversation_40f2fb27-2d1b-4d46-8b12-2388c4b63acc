"use server";

import {
  BankTransferListResponse,
  BankTransfersFilters,
  UpdateBankTransferParams,
} from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/bank-transfers`;

/**
 * Returns a list of unmatched bank transfers
 * @param searchQuery Search query
 * @param status Status of the bank transfer
 * @param bankName Name of the bank
 * @param paymentDate Payment date of the bank transfer
 * @param currency Currency of the bank transfer
 * @param senderName Name of the sender
 * @returns List of bank transfers
 */
export const getBankTransfers = async (
  params: BankTransfersFilters
): Promise<BankTransferListResponse> => {
  let requestQuery = new URL(`${baseUrl}?`);
  const query = new URLSearchParams();
  if (params.searchQuery) query.append("query", params.searchQuery);
  if (params.status) query.append("status", params.status);
  if (params.bankName) query.append("bankName", params.bankName);
  if (params.paymentDate) query.append("paymentDate", params.paymentDate);
  if (params.currency) query.append("currency", params.currency);
  if (params.senderName) query.append("senderName", params.senderName);
  query.append("limit", "1000");
  query.append("page", "1");
  query.append("orderBy", "DESC");
  requestQuery.search = query.toString();
  const res = await osRequest(requestQuery.href, "GET");

  return res;
};

/**
 * Returns a specific bank transfer
 * @param id ID of the bank transfer
 * @returns Bank transfer object
 */
export const getBankTransfer = async (id: string) => {
  const res = await osRequest(`${baseUrl}/${id}`, "GET");
  return res;
};

/**
 * Updates the status of a bank transaction in paradox api
 * @param id ID of the bank transfer
 * @param status Status of the bank transfer
 * @returns Bank transfer object
 */
export const updateBankTransferStatus = async (
  params: UpdateBankTransferParams
) => {
  const { status } = params;
  const res = await osRequest(`${baseUrl}/${params.id}/updateStatus`, "POST", {
    status,
  });

  return res;
};
