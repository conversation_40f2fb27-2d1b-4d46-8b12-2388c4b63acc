"use client";

import {
  Button,
  DatePicker,
  Input,
  Col,
  FormInstance,
  Row,
  Form,
  Select,
} from "antd";
import { SearchOutlined, SyncOutlined } from "@ant-design/icons";

import { UnmatchedTransactionsTableState } from "@/hooks/accounting/bankTransfers";

type UnmatchedTransactionsHeaderProps = {
  filterForm: FormInstance;
  handleFilterChange: (changedValues: any, allValues: any) => void;
  resetFilters: () => void;
  openMatchingModal: () => void;
  pageState: UnmatchedTransactionsTableState;
};

export default function UnmatchedTransactionsHeader({
  filterForm,
  handleFilterChange,
  resetFilters,
  openMatchingModal,
  pageState,
}: UnmatchedTransactionsHeaderProps) {
  return (
    <Form form={filterForm} onValuesChange={handleFilterChange}>
      <Row>
        <Col xs={12} sm={12} md={16} lg={16} xl={16} xxl={18}>
          <Form.Item name="searchQuery">
            <Input
              prefix={<SearchOutlined />}
              placeholder="Search for Transactions"
            />
          </Form.Item>
        </Col>
        <Col xs={11} sm={11} md={7} lg={7} xl={7} xxl={5} offset={1}>
          <Button
            icon={<SyncOutlined />}
            type="primary"
            onClick={() => {
              openMatchingModal();
            }}
            disabled={pageState.unmatchedTrx.length === 0}
          >
            Attach unmatched bank transfers
          </Button>
        </Col>
      </Row>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <Form.Item name="bankFilter" style={{ width: "15%" }}>
          <Select
            allowClear
            placeholder="Bank"
            options={[
              {
                value: "AirWallex",
                label: "AirWallex",
              },
              {
                value: "Revolut",
                label: "Revolut",
              },
              {
                value: "IbanFirst",
                label: "IbanFirst",
              },
            ]}
          />
        </Form.Item>
        <Form.Item name="currencyFilter" style={{ width: "15%" }}>
          <Select
            allowClear
            placeholder="Currency"
            options={[{ value: "EUR", label: <span>EUR</span> }]}
          />
        </Form.Item>
        <Form.Item name="senderNameFilter" style={{ width: "15%" }}>
          <Input allowClear placeholder="Sender Name" />
        </Form.Item>
        <Form.Item name="statusFilter" style={{ width: "15%" }}>
          <Select
            allowClear
            placeholder="Status"
            options={[
              {
                value: "matched",
                label: "Matched",
              },
              {
                value: "unmatched",
                label: "Unmatched",
              },
              {
                value: "ignored",
                label: "Ignored",
              },
            ]}
          />
        </Form.Item>
        <Form.Item name="paymentDateFilter" style={{ width: "20%" }}>
          <DatePicker allowClear placeholder="Payment Date" picker="date" />
        </Form.Item>
        <Button
          type="link"
          onClick={() => {
            resetFilters();
            filterForm.resetFields();
          }}
        >
          Clear Filters
        </Button>
      </div>
    </Form>
  );
}
