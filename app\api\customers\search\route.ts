import { NextResponse } from "next/server";

import { getCustomers, initChargebee } from "@/server/chargebee";
import { GetCustomersParams } from "@/types";

initChargebee();

/**
 * Handles the GET request for retrieving customers.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved customers or an error message.
 */
export async function POST(req: Request) {
  try {
    const body = (await req.json()) as GetCustomersParams;

    const response = await getCustomers(body);
    return NextResponse.json(response);
  } catch (error) {
    console.error("error", error);
    return NextResponse.json(
      { error: "CANNOT_SEARCH_CUSTOMERS" },
      { status: 400 }
    );
  }
}
