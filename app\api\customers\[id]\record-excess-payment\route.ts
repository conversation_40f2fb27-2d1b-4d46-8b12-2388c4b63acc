import {
  init<PERSON>hargebee,
  recordExcessPaymentForCustomer,
} from "@/server/chargebee";
import { RecordExcessPaymentInputParams } from "@/types";
import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the POST request for recording an offline refund.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the returned response or an error message.
 */
export async function POST(req: Request) {
  try {
    const body = await req.json();
    const response = await recordExcessPaymentForCustomer(
      body.params as RecordExcessPaymentInputParams
    );
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_RECORD_PAYMENT" },
      { status: 400 }
    );
  }
}
