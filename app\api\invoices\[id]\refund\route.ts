import { NextResponse } from "next/server";
import { initChargebee, refundInvoice } from "@/server/chargebee";

import { RefundInvoiceParams } from "@/types";

initChargebee();

/**
 * Handles the POST request for issuing a refund.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the returned response or an error message.
 */
export async function POST(req: Request) {
  try {
    const body = (await req.json()) as RefundInvoiceParams;
    if (body.id) {
      const response = await refundInvoice(body);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_REFUND_INVOICE" },
      { status: 400 }
    );
  }
}
