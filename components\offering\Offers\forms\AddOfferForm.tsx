"use client";

import { InfoCircleTwoTone } from "@ant-design/icons";
import {
  Checkbox,
  Col,
  ColorPicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Typography,
} from "antd";
import { useState } from "react";
import UploadImage from "@/components/core/UploadImage";
import { getPaymentGateways } from "@/lib/paymentGateways";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";

type AddProps = {
  isModalOpen: boolean;
  handleAdd: Function;
  handleCancel: Function;
};

const rules = [{ required: true, message: "This value is required!" }];
const { Title, Text } = Typography;

const AddOfferForm = ({ isModalOpen, handleAdd, handleCancel }: AddProps) => {
  const [imgs, setImgs] = useState({
    offerImage: "",
    cardImage: "",
    bannerImage: "",
    offerBannerImage: "",
  });
  const [form] = Form.useForm();
  const [showCollectDataForm, setShowCollectDataForm] = useState(true);

  const formatValues = (values: any) => {
    let testimonial;
    if (values.template === "B") {
      testimonial = {
        name: values.testimonyName,
        title: values.testimonyTitle,
        comment: values.testimonyComment,
        url: values.testimonyUrl,
        rating: values.testimonyRating,
      };
    }
    let offer = {
      offerInfo: {
        name: values.name,
        externalName: values.externalName,
        status: values.status,
        currency: values.currency,
        fiscalEntity: values.fiscalEntity,
        image: imgs.offerImage,
        redirectUrl: values.redirectUrl,
        slug: values.slug,
        paymentGateway: values.paymentGateway,
        description: values.description,
        bannerImage: imgs.offerBannerImage,
        cardImage: imgs.cardImage,
        isForever: values.isForever,
        usp: values.usp,
        trialDaysYearly: values?.trialDaysYearly,
        trialDaysMonthly: values?.trialDaysMonthly,
      },
      checkoutInfo: {
        bannerImage: imgs.bannerImage,
        mainColor:
          typeof values.mainColor === "string"
            ? values.mainColor
            : values.mainColor.toHexString(),
        template: values.template,
        testimony: testimonial,
        cancellationPolicy: values.cancellationPolicy,
        abandonedCartFlow: values.abandonedCartFlow,
      },
    };
    handleAdd(offer);
  };

  return (
    <DrawerFormContainer
      title="Create Offer"
      isOpen={isModalOpen}
      onClose={() => {
        handleCancel();
      }}
      submitText="Create"
      onSubmit={() => {
        form.submit();
      }}
      onCancel={() => {
        handleCancel();
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={(values) => {
          formatValues(values);
        }}
        initialValues={{
          currency: "EUR",
          template: "A",
          mainColor: "#D50404",
          showTestimonial: false,
          abandonedCartFlow: false,
        }}
      >
        <Title level={5}>Offer settings</Title>
        <div
          className="paradox-card-white"
          style={{
            borderRadius: "10px",
          }}
        >
          <Row>
            <Col span={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <InfoCircleTwoTone style={{ fontSize: 25 }} />
              </div>
            </Col>
            <Col span={22} offset={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                You can associate products and plans from the details page after
                creating the offer.
              </div>
            </Col>
          </Row>
        </div>
        <br />
        <Form.Item
          label="Offer name "
          name="name"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          A descriptive name for this offer. It can be used in the checkout page
          based on your offer configuration.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Offer's external name "
          name="externalName"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          ⚠️ This offer name is visible to the customer, on the portal and
          checkout page
        </Text>
        <br />
        <br />
        <Form.Item
          label="Offer description "
          name="description"
          style={{ marginBottom: "0px" }}
          rules={[
            ...rules,
            { min: 5, message: "Please enter at least 5 characters" },
          ]}
        >
          <Input.TextArea maxLength={500} />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          A comprehensive description for this offer. It will be used in the
          customer portal when customers view their purchases.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Offer USP "
          name="usp"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The unique selling point of this offer.
        </Text>
        <br />
        <br />
        <UploadImage
          name="offerImage"
          label="Offer Image"
          setImage={(img) => setImgs({ ...imgs, offerImage: img })}
          url={imgs.offerImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This image will be displayed as a product image on the checkout page
          based on your offer configuration.
        </Text>
        <br />
        <br />
        <UploadImage
          name="cardImage"
          label="Card Image"
          setImage={(img) => setImgs({ ...imgs, cardImage: img })}
          url={imgs.cardImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This image will be displayed as a product image on the customer portal
          when customers view their purchases.
        </Text>
        <br />
        <br />
        <UploadImage
          name="offerBannerImage"
          label="Banner Image"
          setImage={(img) => setImgs({ ...imgs, offerBannerImage: img })}
          url={imgs.offerBannerImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          This image will be displayed as the banner image on the customer
          portal when customers view the details of their purchases.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Currency"
          name="currency"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          For which currency is this offer created?
        </Text>
        <br />
        <br />
        <Form.Item
          label="Status"
          name="status"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "active", label: "Active" },
              { value: "disabled", label: "Disabled" },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Setting a offer to Disabled will disable all the related checkout
          pages.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Is Forever offer?"
          name="isForever"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            placeholder="Yes/No"
            options={[
              {
                label: "Yes",
                value: true,
                disabled:
                  process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT ===
                  "production",
              },
              { label: "No", value: false },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Is this a forever offer?
        </Text>
        <br />
        <br />
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.isForever !== currentValues.isForever
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("isForever") === true ? (
              <Row style={{ marginBottom: "10px" }}>
                <Col span={11}>
                  <Form.Item
                    label="Trial period yearly (in days)"
                    name="trialDaysYearly"
                    style={{ marginBottom: "0px" }}
                    rules={rules}
                  >
                    <InputNumber placeholder="30" min={0} max={300} />
                  </Form.Item>
                  <Text style={{ fontSize: 13, color: "darkgray" }}>
                    Trial period for yearly subscription
                  </Text>
                </Col>
                <Col span={11} offset={2}>
                  <Form.Item
                    label="Trial period monthly (in days)"
                    name="trialDaysMonthly"
                    style={{ marginBottom: "0px" }}
                    rules={rules}
                  >
                    <InputNumber placeholder="7" min={0} max={28} />
                  </Form.Item>
                  <Text style={{ fontSize: 13, color: "darkgray" }}>
                    Trial period for monthly subscription
                  </Text>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>
        <Form.Item
          label="Redirect URL"
          name="redirectUrl"
          style={{ marginBottom: "0px" }}
          rules={[
            ...rules,
            { type: "url", message: "Please enter a valid URL" },
          ]}
        >
          <Input />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The customer will be redirected to this URL after the purchase.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Checkout page slug"
          name="slug"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input
            onInput={(e) =>
              (e.currentTarget.value = e.currentTarget.value
                .replace(/[^-a-zA-Z0-9 ]/g, "")
                .toLowerCase()
                .replace(/\s+/g, "-"))
            }
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Slug refers to the end part of the checkout URL
        </Text>
        <Divider />
        <Title level={5}>Fiscality</Title>
        <Form.Item
          label="Billing Entity"
          name="fiscalEntity"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "PI", label: "Paradox Institute" },
              { value: "PG", label: "Paradox Group" },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          By selecting this fiscal entity, all invoices will be generated by it.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Payment Gateway"
          name="paymentGateway"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={getPaymentGateways().map((gateway) => ({
              value: gateway.value,
              label: gateway.label,
            }))}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          By selecting a payment gateway, all payments will go through it.
        </Text>
        {showCollectDataForm && (
          <>
            <Divider />
            <Title level={5}>Data Collection</Title>
            <Form.Item
              label="Abandoned Cart Flow"
              name="abandonedCartFlow"
              valuePropName="checked"
              style={{ marginBottom: "0px" }}
            >
              <Checkbox>Enable hubspot collect-flow before checkout</Checkbox>
            </Form.Item>
            <Text style={{ fontSize: 13, color: "darkgray" }}>
              If enabled, users will be asked for their first name, last name,
              email and phone number before checkout.
            </Text>
          </>
        )}
        <Divider />
        <Title level={5}>Checkout Design</Title>
        <div
          className="paradox-card-white"
          style={{
            borderRadius: "10px",
          }}
        >
          <Row>
            <Col span={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <InfoCircleTwoTone style={{ fontSize: 25 }} />
              </div>
            </Col>
            <Col span={22} offset={1}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {`After the offer's creation, you can edit all elements
                        from this checkout template.`}
              </div>
            </Col>
          </Row>
        </div>
        <br />
        <Form.Item
          label="Checkout page template"
          name="template"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "A", label: "Template A" },
              { value: "B", label: "Template B" },
            ]}
            onChange={(value) => {
              setShowCollectDataForm(value === "A");
              form.setFieldsValue({ showTestimonial: value === "B" });
            }}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          What template do you want to use for this offer?
        </Text>
        <br />
        <br />
        <UploadImage
          name="bannerImage"
          label="Banner Image"
          setImage={(img) => setImgs({ ...imgs, bannerImage: img })}
          url={imgs.bannerImage}
        />
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Based on the offer configuration, this image will be display in the
          Checkout page.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Main color"
          name="mainColor"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <ColorPicker size="large" showText />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Will affect colors of buttons / clickable elements.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Cancellation policy"
          name="cancellationPolicy"
          style={{ marginBottom: "0px" }}
        >
          <Input.TextArea rows={4} maxLength={800} />
          <Form.Item
            noStyle
            shouldUpdate={(prev, next) =>
              prev.template !== next.template ||
              prev.showTestimonial !== next.showTestimonial
            }
          >
            {({ getFieldValue }) =>
              getFieldValue("template") === "B" && (
                <>
                  <br />
                  <br />
                  <Form.Item
                    name="showTestimonial"
                    label="Display the testimonial section"
                    valuePropName="checked"
                    style={{ marginBottom: "0px" }}
                  >
                    <Checkbox defaultChecked>
                      Show testimonial on the checkout page
                    </Checkbox>
                  </Form.Item>
                  <Text style={{ fontSize: 13, color: "darkgray" }}>
                    Toggle whether the testimonial block should be shown on the
                    checkout.
                  </Text>
                </>
              )
            }
          </Form.Item>
          <Text style={{ fontSize: 13, color: "darkgray" }}>
            The cancellation policy for this offer.
          </Text>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, next) =>
            prev.showTestimonial !== next.showTestimonial
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("template") === "B" &&
            getFieldValue("showTestimonial") && (
              <>
                <Divider />
                <Title level={5}>Testimony</Title>
                <Form.Item
                  label="Testifier name"
                  name="testimonyName"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <Input placeholder="Testifier name" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The name of the person who is testifying.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testifier job"
                  name="testimonyTitle"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <Input placeholder="Testifier job" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The job of the person who is testifying.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testifier quote"
                  name="testimonyComment"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <Input.TextArea placeholder="Testifier quote" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The quote to display on the checkout page.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testifier rating"
                  name="testimonyRating"
                  style={{ marginBottom: "0px" }}
                  rules={rules}
                >
                  <InputNumber placeholder="Testifier rating" min={5} max={5} />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The rating given by the person who is testifying.
                </Text>
                <br />
                <br />
                <Form.Item
                  label="Testimony URL"
                  name="testimonyUrl"
                  style={{ marginBottom: "0px" }}
                  rules={[
                    ...rules,
                    { type: "url", message: "Please enter a valid URL" },
                  ]}
                >
                  <Input placeholder="Testimony URL" />
                </Form.Item>
                <Text style={{ fontSize: 13, color: "darkgray" }}>
                  The url for the video testimony.
                </Text>
              </>
            )
          }
        </Form.Item>
      </Form>
    </DrawerFormContainer>
  );
};

export default AddOfferForm;
