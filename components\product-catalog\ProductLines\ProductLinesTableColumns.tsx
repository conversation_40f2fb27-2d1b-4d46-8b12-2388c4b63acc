"use client";

import { ProductLine } from "@/types";

import { Button, Tag, Typography } from "antd";
import { EditOutlined } from "@ant-design/icons";

const { Text } = Typography;

type ProductLinesTableColumnsProps = {
  onEdit: (productLine: ProductLine) => void;
};

export const getProductLinesTableColumns = ({
  onEdit,
}: ProductLinesTableColumnsProps) => [
  {
    title: "ID",
    dataIndex: "chargebeeId",
    key: "chargebeeId",
    render: (val: string) => {
      return (
        <Text type="secondary" copyable>
          {val}
        </Text>
      );
    },
  },
  {
    title: "Name",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (data: string) =>
      data === "active" ? (
        <Tag color="green">Active</Tag>
      ) : (
        <Tag color="orange">Draft</Tag>
      ),
  },
  {
    title: "Description",
    dataIndex: "description",
    key: "description",
  },
  {
    title: "Action",
    key: "action",
    render: (data: ProductLine) => {
      return (
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => onEdit(data)}
        >
          Edit
        </Button>
      );
    },
  },
];
