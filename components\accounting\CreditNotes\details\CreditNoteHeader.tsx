"use client";

import { DownloadOutlined } from "@ant-design/icons";
import { Button, Tag, Space, Typography, Descriptions } from "antd";

import { capitalizeWord } from "@/lib/text";
import { formatDateFromUnix } from "@/lib/date";
import { CreditNote } from "@/types";
import PageHeader from "@/components/core/PageHeader";

const { Text } = Typography;

type CreditNoteHeaderProps = {
  creditNote: CreditNote | undefined;
  downloadCreditNotePDF: () => void;
};

export default function CreditNoteHeader({
  creditNote,
  downloadCreditNotePDF,
}: CreditNoteHeaderProps) {
  return (
    <PageHeader
      title="Credit Note Details"
      actions={
        <Space>
          <Button
            icon={<DownloadOutlined />}
            onClick={downloadCreditNotePDF}
            type="primary"
          >
            Download PDF
          </Button>
        </Space>
      }
    >
      <Descriptions title="" column={3} bordered size="small">
        <Descriptions.Item label="ID">
          <Text copyable={{ text: creditNote?.chargebeeId }}>
            {creditNote?.chargebeeId}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="Date">
          {formatDateFromUnix(creditNote?.date)}
        </Descriptions.Item>
        <Descriptions.Item label="Status">
          <Tag color={creditNote?.status === "refunded" ? "red" : "orange"}>
            {capitalizeWord(creditNote?.status)}
          </Tag>
        </Descriptions.Item>
      </Descriptions>
    </PageHeader>
  );
}
