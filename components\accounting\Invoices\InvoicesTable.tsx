"use client";

import React, {
  Suspense,
  useState,
  useMemo,
  useCallback,
  useEffect,
} from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  Button,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  Row,
  Select,
  Skeleton,
  Table,
  Tag,
  Typography,
} from "antd";
import { SearchOutlined } from "@ant-design/icons";

import { Invoice, InvoicesFilters } from "@/types";
import paginationConfig from "../../form/paginationConfig";
import { useProtectPage } from "@/hooks/roles";
import { useDebounce } from "@/hooks/utils/useDebounce";
import { useFetchInvoices } from "@/hooks/accounting/invoices";
import { formatter } from "@/lib/currency";
import { capitalizeWord } from "@/lib/text";
import { formatDateFromUnix } from "@/lib/date";

const { Text, Title } = Typography;

const generateStatusTag = (status: string) => {
  switch (status) {
    case "paid":
      return <Tag color="green">Paid</Tag>;
    case "not_paid":
      return <Tag color="orange">Not paid</Tag>;
    case "payment_due":
      return <Tag color="red">Payment due</Tag>;
    default:
      return <Tag>{capitalizeWord(status)}</Tag>;
  }
};

const initialFilters = {
  searchQuery: "",
  fiscalEntityFilter: "",
  subscriptionFilter: "",
  statusFilter: "",
};

const InvoicesTable = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [filters, setFilters] = useState<InvoicesFilters>(initialFilters);

  // We initially had these two text types as part of the filters state but this
  // was causing an issue on first page load. After the initial load, the debounce
  // search wound not work for the first 2 searches (probably because of setting
  // the initial state of the filters to an empty string). We decided to move these
  // separate states so we can do proper debounce on them without interference from
  // the rest of the filter values. This is how it was done previously on products
  // table as well so this also bring uniformity to the codebase.
  const [searchQuery, setSearchQuery] = useState("");
  const [subscriptionFilter, setSubscriptionFilter] = useState("");
  const searchTerm = useDebounce(searchQuery);
  const debouncedSubId = useDebounce(subscriptionFilter);

  useProtectPage("invoices-list");

  const [filterForm] = Form.useForm();

  const {
    data: invoices,
    isLoading,
    mutate: refetchInvoices,
  } = useFetchInvoices({
    filters: {
      ...filters,
      searchQuery: searchTerm,
      subscriptionFilter: debouncedSubId,
    },
  });

  const handleRowClick = (data: Invoice) => {
    router.push(`/dashboard/invoices/${data.chargebeeId}`);
  };

  const columns = useMemo(
    () => [
      {
        title: "Invoice Id",
        dataIndex: "chargebeeId",
        key: "id",
        render(val: string) {
          return (
            <>
              <Text copyable>{val}</Text>
            </>
          );
        },
      },
      {
        title: "Subscription id",
        dataIndex: "subscriptionId",
        key: "id",
        render: (id: string) => {
          return (
            <Text type="secondary" copyable={{ text: id }}>
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(`/dashboard/subscriptions/${id}`);
                }}
              >
                {id}
              </a>
            </Text>
          );
        },
      },
      {
        title: "Customer Email",
        dataIndex: "customerEmail",
        key: "id",
        render: (email: string, record: Invoice) => {
          return (
            <Text
              copyable
              style={{ cursor: "pointer" }}
              onClick={(e) => {
                e.stopPropagation();
                router.push(pathname + "?customer=" + record.customerId);
              }}
            >
              {email}
            </Text>
          );
        },
      },
      {
        title: "Customer Name",
        dataIndex: "customerName",
        key: "id",
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "id",
        render: (status: string) => {
          return generateStatusTag(status);
        },
      },
      {
        title: "Date",
        dataIndex: "date",
        key: "id",
        render: (date: number) => {
          return formatDateFromUnix(date);
        },
      },
      {
        title: "Net term days",
        dataIndex: "netTermDays",
        key: "id",
      },
      {
        title: "Due date",
        dataIndex: "dueDate",
        key: "id",
        render: (date: number) => {
          return formatDateFromUnix(date);
        },
      },
      {
        title: "Paid on",
        dataIndex: "paidAt",
        key: "id",
        render: (date: number) => {
          return formatDateFromUnix(date);
        },
      },
      {
        title: "Total",
        dataIndex: "total",
        key: "id",
        render: (total: number) => {
          return formatter.format(total / 100);
        },
      },
      {
        title: "Amount paid",
        dataIndex: "amountPaid",
        key: "id",
        render: (amount: number) => {
          return formatter.format(amount / 100);
        },
      },
      {
        title: "Amount due",
        dataIndex: "amountDue",
        key: "id",
        render: (amount: number) => {
          return formatter.format(amount / 100);
        },
      },
      {
        title: "Currency code",
        dataIndex: "currencyCode",
        key: "id",
      },
      {
        title: "Generated at",
        dataIndex: "generatedAt",
        key: "id",
        render: (date: number) => {
          return formatDateFromUnix(date);
        },
      },
      {
        title: "Business entity",
        dataIndex: "businessEntityId",
        key: "id",
      },
    ],
    []
  );

  const filterInvoices = useCallback(
    async (changedValues: any) => {
      if (changedValues?.searchQuery || changedValues?.searchQuery === "") {
        setSearchQuery(changedValues.searchQuery);
      } else if (
        changedValues?.subscriptionFilter ||
        changedValues?.subscriptionFilter === ""
      ) {
        setSubscriptionFilter(changedValues.subscriptionFilter);
      } else {
        const filterValues = filterForm.getFieldsValue();
        if (filterValues.paidAtFilter) {
          filterValues.paidAtFilter = (
            filterValues.paidAtFilter as Date
          ).toISOString();
        }
        setFilters(filterValues);
      }
    },
    [filterForm]
  );

  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
    setSearchQuery("");
    setSubscriptionFilter("");
  }, []);

  useEffect(() => {
    refetchInvoices();
  }, [
    filters.subscriptionFilter,
    filters.searchQuery,
    filters.fiscalEntityFilter,
    filters.statusFilter,
    filters.paidAtFilter,
  ]);

  return (
    <Suspense fallback={<Skeleton />}>
      <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
        <Title level={3}>Invoices</Title>
        <Row style={{ lineHeight: "10px" }}>
          <Col span={24}>
            <Form form={filterForm} onValuesChange={filterInvoices}>
              <Form.Item name="searchQuery">
                <Input
                  prefix={<SearchOutlined />}
                  placeholder="Search by customer email"
                />
              </Form.Item>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <Form.Item name="subscriptionFilter" style={{ width: "20%" }}>
                  <Input allowClear placeholder="Subscription id" />
                </Form.Item>
                <Form.Item name="statusFilter" style={{ width: "20%" }}>
                  <Select
                    allowClear
                    placeholder="Status"
                    options={[
                      { value: "paid", label: "Paid" },
                      { value: "posted", label: "Posted" },
                      { value: "payment_due", label: "Payment due" },
                      { value: "not_paid", label: "Not paid" },
                      { value: "voided", label: "Voided" },
                      { value: "pending", label: "Refunded" },
                    ]}
                  />
                </Form.Item>
                <Form.Item name="fiscalEntityFilter" style={{ width: "20%" }}>
                  <Select
                    allowClear
                    placeholder="Billing Entity"
                    options={[
                      { value: "PG", label: "Paradox Group" },
                      { value: "PI", label: "Paradox Institute" },
                    ]}
                  />
                </Form.Item>
                <Form.Item name="paidAtFilter" style={{ width: "20%" }}>
                  <DatePicker
                    showTime={false}
                    allowClear
                    placeholder="Paid on"
                  />
                </Form.Item>
                <Button
                  type="link"
                  onClick={() => {
                    resetFilters();
                    filterForm.resetFields();
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            </Form>
          </Col>
        </Row>
        <Divider />
        <Table
          dataSource={invoices?.items}
          loading={isLoading}
          columns={columns}
          scroll={{ x: 3000 }}
          size="large"
          pagination={paginationConfig}
          rowClassName={(data, i) => {
            return "paradox-table-row";
          }}
          onRow={(data, index) => {
            return {
              onClick: () => {
                handleRowClick(data);
              },
            };
          }}
          rowKey="chargebeeId"
        />
      </div>
    </Suspense>
  );
};

export default React.memo(InvoicesTable);
