export const dynamic = "force-dynamic";

import { Metadata } from "next";

import OfferDetails from "@/components/offering/Offers/OfferDetails";

export const metadata: Metadata = {
  title: "Offer details",
};

type OfferDetailsProps = {
  offerId: string;
};

export default async function OfferDetailsPage(
  props: {
    params: Promise<OfferDetailsProps>;
  }
) {
  const params = await props.params;
  return <OfferDetails offerId={params.offerId} />;
}
