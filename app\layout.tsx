import "./app.css";
import { Clerk<PERSON>rovider, RedirectToSignIn, SignedOut } from "@clerk/nextjs";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { App } from "antd";
import "@ant-design/v5-patch-for-react-19";

import Analytics from "@/components/analytics";
import { Suspense } from "react";

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <Suspense>
        <html lang="en" style={{ width: "100%", height: "100%" }}>
          <body
            suppressHydrationWarning={true}
            style={{ margin: 0, height: "100%" }}
          >
            <>
              <AntdRegistry>
                <App>{children}</App>
              </AntdRegistry>
              <SignedOut>
                <RedirectToSignIn />
              </SignedOut>
            </>
          </body>
          <Analytics />
        </html>
      </Suspense>
    </ClerkProvider>
  );
}
