"use server";

import { PlanAddDTO, PlanListResponse } from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/product-plans`;

/**
 * Posts a plan to the backend server.
 * @param params - The parameters for the plan.
 * @returns A Promise that resolves to the response from the server.
 */
export const createProductPlan = async (params: PlanAddDTO): Promise<any> => {
  try {
    let plan = {
      planInfo: {
        internalName: params.internalName,
        externalName: params.externalName,
        description: params.description,
        fiscalEntity: params.fiscalEntity,
        productId: params.productId,
        crmSku: params.crmSku,
      },
      priceInfo: {
        internalName: params.internalName,
        externalName: params.externalName,
        description: params.description,
        amountPerBillingCycle: Number(params.amountPerBillingCycle),
        amount: Number(params.amount),
        currencyCode: "Eur",
        billingCycle: params.billingCycle,
        totalBillingCycles: Number(params.totalBillingCycles),
      },
    };
    const res = await osRequest(`${baseUrl}`, "POST", plan);

    return res;
  } catch (err) {
    console.error("cannot createProductPlan: ", err);
  }
};

/**
 * Posts an existing plan to the backend server to duplicate.
 * @param params - The parameters for the plan.
 * @returns A Promise that resolves to the response from the server.
 */
export const duplicateProductPlan = async (
  params: PlanAddDTO
): Promise<any> => {
  try {
    let plan = {
      planInfo: {
        internalName: params.internalName,
        externalName: params.externalName,
        description: params.description,
        fiscalEntity: params.fiscalEntity,
        productId: params.productId,
        crmSku: params.crmSku,
      },
      priceInfo: {
        internalName: params.internalName,
        externalName: params.externalName,
        description: params.description,
        amountPerBillingCycle: Number(params.amountPerBillingCycle),
        amount: Number(params.amount),
        currencyCode: "Eur",
        billingCycle: params.billingCycle,
        totalBillingCycles: Number(params.totalBillingCycles),
      },
    };
    const res = await osRequest(`${baseUrl}`, "POST", plan);

    return res;
  } catch (err) {
    console.error("cannot createProductPlan: ", err);
  }
};

/**
 * Archives a plan on the backend server.
 * @param params - The parameters for the plan.
 * @returns A Promise that resolves to the response from the server.
 */
export const archivePlan = async (params: {
  planId: number;
  priceId: number;
}): Promise<any> => {
  try {
    let val = {
      planInfo: {
        id: params.planId,
        status: "draft",
      },
      priceInfo: {
        id: params.priceId,
        status: "draft",
      },
    };
    const res = await osRequest(`${baseUrl}`, "PATCH", val);

    return res;
  } catch (err) {
    console.error("cannot archivePlan: ", err);
  }
};

/**
 * Activates a plan on the backend server.
 * @param params - The parameters for the plan.
 * @returns A Promise that resolves to the response from the server.
 */
export const activatePlan = async (params: {
  planId: number;
  priceId: number;
}): Promise<any> => {
  try {
    let val = {
      planInfo: {
        id: params.planId,
        status: "active",
      },
      priceInfo: {
        id: params.priceId,
        status: "active",
      },
    };
    const res = await osRequest(`${baseUrl}`, "PATCH", val);

    return res;
  } catch (err) {
    console.error("cannot activatePlan: ", err);
  }
};

export const getProductPlan = async (params: { id: number }): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}/${params.id}`, "GET");
    return res;
  } catch (err) {
    console.error("cannot getProductPlan: ", err);
  }
};

export const getProductPlans = async (
  productId: number
): Promise<PlanListResponse> => {
  try {
    const url = `${process.env.API_URL}/product-plans/product/${productId}`;
    const res = await osRequest(url, "GET");
    return res;
  } catch (err) {
    console.error("cannot getProductPlans: ", err);
    return { items: [], total: 0, currentPage: 0 };
  }
};

export const updatePlan = async (params: {
  values: any;
  planId: number | undefined;
  priceId: number | undefined;
}): Promise<any> => {
  try {
    const { values, planId, priceId } = params;
    let tempPlan = {
      planInfo: {
        internalName: values.internalName,
        externalName: values.externalName,
        description: values.description,
        fiscalEntity: values.fiscalEntity,
        id: planId,
        status: values.status,
      },
      priceInfo: {
        internalName: values.internalName,
        externalName: values.externalName,
        description: values.description,
        amountPerBillingCycle: values.amountPerBillingCycle,
        periodUnit: values.billingCycle,
        amount: values.amount,
        totalBillingCycles: values.totalBillingCycles,
        id: priceId,
        status: values.status,
      },
    };
    const res = await osRequest(`${baseUrl}`, "PATCH", tempPlan);

    return res;
  } catch (err) {
    console.error("cannot updatePlan: ", err);
  }
};

/**
 * Retrieves the unique billing cycles from the backend server.
 * @returns A Promise that resolves to the response data.
 */
export const getUniqueBillingCycles = async (): Promise<number[]> => {
  try {
    const res = await osRequest(`${baseUrl}/prices/uniqueBillingCycles`, "GET");
    return res;
  } catch (err) {
    console.error("getUniqueBillingCycles error: ", err);
    return [];
  }
};
