"use client";

import useSWR, { SWRConfiguration } from "swr";

import limits from "@/lib/limits";
import {
  getAllProductFamilies,
  getProductFamiliesByLine,
  getProductFamily,
} from "@/lib/services";
import {
  ProductFamily,
  ProductFamilyFilter,
  ProductFamilyListResponse,
} from "@/types";

type UseFetchProductFamiliesParams = {
  options?: SWRConfiguration;
  filters?: ProductFamilyFilter;
};

/**
 * Fetches product families
 * @param params - Filters and options
 * @returns Product families
 */
export const useFetchProductFamilies = (
  params?: UseFetchProductFamiliesParams
) => {
  const status = params?.filters?.status || "active";
  const search = params?.filters?.search || "";

  return useSWR<ProductFamilyListResponse>(
    "useFetchProductFamilies",
    () => getAllProductFamilies(1, limits.productsFamilies, status, search),
    params?.options
  );
};

/**
 * Fetches product families by line
 * @param params - Filters and options
 * @returns Product families
 */
export const useFetchProductFamiliesByLine = (
  params?: UseFetchProductFamiliesParams
) => {
  const productLine = params?.filters?.productLine;

  return useSWR(
    "useFetchProductFamiliesByLine",
    () => getProductFamiliesByLine(productLine),
    params?.options
  );
};

/**
 * Fetches product family
 * @param id - Product family id
 * @param params - Filters and options
 * @returns Product family
 */
export const useFetchProductFamily = (
  id: number,
  params?: UseFetchProductFamiliesParams
) => {
  return useSWR<ProductFamily | undefined>(
    `useFetchProductFamily-${id}`,
    () => getProductFamily(id),
    params?.options
  );
};
