"use client";

import { EditOutlined, PlusOutlined } from "@ant-design/icons";
import {
  App,
  Button,
  Space,
  Typography,
  Divider,
  Tabs,
  TabsProps,
  Skeleton,
  Descriptions,
} from "antd";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { notFound } from "next/navigation";

import UpdateProductForm from "@/components/product-catalog/Products/forms/UpdateProductForm";
import { ProductSettings } from "./ProductSettings";
import { PlanSettings } from "../ProductPlans/PlansSettings";
import AddPlanForm from "../ProductPlans/forms/AddPlanForm";
import { useFetchProduct } from "@/hooks/product-catalog/products";
import { useFetchProductFamily } from "@/hooks/product-catalog/productFamilies";
import { useFetchProductPlans } from "@/hooks/product-catalog/productPlans";
import { useProtectPage } from "@/hooks/roles";
import {
  activateProduct,
  archiveProduct,
  createProductPlan,
  updateProduct,
} from "@/lib/services";
import { ProductUpdateParams } from "@/types";
import PageHeader from "@/components/core/PageHeader";

type ProductIdProps = {
  productId: string;
};

const { Text } = Typography;

const ProductDetails = ({ productId }: ProductIdProps) => {
  if (!productId) {
    notFound();
  }
  const router = useRouter();
  const { canAccess } = useProtectPage("product-details");
  const { message, modal } = App.useApp();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setEditModalOpen] = useState(false);

  const {
    isLoading,
    data: product,
    mutate: refetchProduct,
  } = useFetchProduct(productId);
  const { data: productPlans } = useFetchProductPlans(product?.id as number);
  const { data: productFamily } = useFetchProductFamily(
    product?.productFamily.id as number
  );

  const showModal = () => {
    if (canAccess("product-plans-create")) {
      setIsModalOpen(true);
    }
  };

  const handleCancel = async () => {
    modal.warning({
      title: "Are you sure you want to leave this page?",
      content: "You will lose all unsaved changes",
      afterClose: () => setIsModalOpen(false),
      okText: "Confirm",
      okButtonProps: { danger: true },
      cancelButtonProps: { ghost: true },
      cancelText: "Cancel",
    });
  };

  const handleUpdate = async (values: any) => {
    let updatedProduct: ProductUpdateParams = {
      code: values.code,
      fiscalEntity: values.fiscalEntity,
      id: values.id,
      image: values.image,
      internalName: values.internalName,
      externalName: values.externalName,
      description: values.description,
      status: values.status,
      taxProfile: values.taxProfile,
      isEvent: values.isEvent,
      eventYear: values.eventYear,
      eventLocation: values.eventLocation,
      withProductDelivery: true,
      lmsIds: values.lmsIds,
      communityIds: values.communityIds,
    };
    let result = await updateProduct(updatedProduct);
    if (result) {
      message.open({
        type: "success",
        content: "Product updated!",
      });
    }
    setEditModalOpen(false);
    await refetchProduct();
  };

  const addPlan = async (values: any) => {
    values.productId = product?.id;
    values.externalName = product?.externalName;
    values.description = product?.description;
    const res = await createProductPlan(values);
    if (res?.statusCode && res?.message) {
      message.open({
        type: "error",
        content: res.message,
      });
    }
    if (res?.plan) {
      message.open({
        type: "success",
        content: "New plan added!",
      });
      router.push(`/dashboard/products/plan/${res.plan?.id}`);
    }
  };

  const archiveProd = async () => {
    if (canAccess("product-archive")) {
      if (product) {
        await archiveProduct(product?.id);
        await refetchProduct();
      }
    }
  };

  const activateProd = async () => {
    if (canAccess("product-activate")) {
      if (product) {
        await activateProduct(product?.id);
        await refetchProduct();
      }
    }
  };

  const tabItems: TabsProps["items"] = [
    {
      key: "1",
      label: "Settings",
      children: (
        <ProductSettings product={product} line={productFamily?.productLine} />
      ),
    },
    {
      key: "2",
      label: `Plans (${product?.plans.length})`,
      children: <PlanSettings plans={productPlans?.items} />,
    },
  ];

  return isLoading ? (
    <Skeleton />
  ) : (
    <div>
      <PageHeader
        title="Product Details"
        actions={
          <Space direction="vertical">
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => {
                if (canAccess("product-update")) {
                  setEditModalOpen(true);
                }
              }}
            >
              Edit Product
            </Button>
            <Button icon={<PlusOutlined />} onClick={showModal}>
              <Text strong>Create Plan</Text>
            </Button>
            {product?.status === "active" ? (
              <Button onClick={archiveProd}>
                <Text>Switch to Draft</Text>
              </Button>
            ) : (
              <Button onClick={activateProd}>
                <Text>Activate</Text>
              </Button>
            )}
          </Space>
        }
      >
        <Descriptions title="" size="small" bordered column={2}>
          <Descriptions.Item label="Internal Name">
            <Text copyable>{product?.internalName}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="External Name">
            <Text copyable>{product?.externalName}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Product Code">
            <Text copyable>{product?.code}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            {product?.status === "active" ? "Active" : "Draft"}
          </Descriptions.Item>
          <Descriptions.Item label="Last modified">
            {product?.updatedAt}
          </Descriptions.Item>
        </Descriptions>
      </PageHeader>
      <Divider />
      <div style={{ padding: "0 30px", marginBottom: 10 }}>
        <Tabs defaultActiveKey="1" items={tabItems} />
      </div>
      {product && isModalOpen ? (
        <AddPlanForm
          isModalOpen={isModalOpen}
          handleAdd={addPlan}
          handleCancel={handleCancel}
          product={product}
        />
      ) : null}
      {product && isEditModalOpen ? (
        <UpdateProductForm
          isModalOpen={isEditModalOpen}
          formValues={product}
          handleUpdate={handleUpdate}
          handleCancel={() => setEditModalOpen(false)}
          line={productFamily?.productLine?.name}
        />
      ) : null}
    </div>
  );
};

export default ProductDetails;
