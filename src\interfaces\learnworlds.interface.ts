import { z } from "zod";

// Base Learnworlds course identifiers schema
export const LearnworldsIdentifiersSchema = z.object({
  slug: z.string().nonempty(),
});
export type ILearnworldsIdentifiers = z.infer<typeof LearnworldsIdentifiersSchema>;

// Base Learnworlds course schema
export const LearnworldsCourseBaseSchema = z.object({
  id: z.string().nonempty(),
  title: z.string().nonempty(),
  description: z.any().optional(), // Can be string or rich content object
  categories: z.array(z.string()).default([]),
  courseImage: z.string().url().optional(),
  identifiers: LearnworldsIdentifiersSchema.optional(),
  access: z.string().optional(),
});
export type ILearnworldsCourseBase = z.infer<typeof LearnworldsCourseBaseSchema>;

// Learnworlds API meta information schema
export const LearnworldsMetaSchema = z.object({
  page: z.number().positive(),
  totalPages: z.number().positive(),
  total: z.number().min(0).optional(),
  limit: z.number().positive().optional(),
});
export type ILearnworldsMeta = z.infer<typeof LearnworldsMetaSchema>;

// Get courses query parameters schema
export const GetLearnworldsCoursesParamsSchema = z.object({
  forceRefresh: z.boolean().default(false).optional(),
  page: z.number().positive().optional(),
  limit: z.number().positive().max(100).optional(),
});
export type IGetLearnworldsCoursesParams = z.infer<typeof GetLearnworldsCoursesParamsSchema>;

// Learnworlds courses response schema (for single page from API)
export const LearnworldsCoursesPageResponseSchema = z.object({
  data: z.array(LearnworldsCourseBaseSchema),
  meta: LearnworldsMetaSchema.optional(),
  success: z.boolean(),
  message: z.string().optional(),
});
export type ILearnworldsCoursesPageResponse = z.infer<typeof LearnworldsCoursesPageResponseSchema>;

// Learnworlds courses list response schema (following the pattern of other list responses)
export const LearnworldsCoursesListResponseSchema = z.object({
  data: z.array(LearnworldsCourseBaseSchema),
  page: z.number().optional(),
  total: z.number().optional(),
});
export type ILearnworldsCoursesListResponse = z.infer<typeof LearnworldsCoursesListResponseSchema>;
