import { z } from "zod";
import { PaginationParamsSchema, PXActionResultSchema } from "./common.interface";

// Base Learnworlds course identifiers schema
export const LearnworldsIdentifiersSchema = z.object({
  slug: z.string().nonempty(),
});
export type ILearnworldsIdentifiers = z.infer<typeof LearnworldsIdentifiersSchema>;

// Base Learnworlds course schema
export const LearnworldsCourseBaseSchema = z.object({
  id: z.string().nonempty(),
  title: z.string().nonempty(),
  description: z.any().optional(), // Can be string or rich content object
  categories: z.array(z.string()).default([]),
  courseImage: z.string().url().optional(),
  identifiers: LearnworldsIdentifiersSchema.optional(),
  access: z.string().optional(),
});
export type ILearnworldsCourseBase = z.infer<typeof LearnworldsCourseBaseSchema>;

// Learnworlds API meta information schema
export const LearnworldsMetaSchema = z.object({
  page: z.number().positive(),
  totalPages: z.number().positive(),
  total: z.number().min(0).optional(),
  limit: z.number().positive().optional(),
});
export type ILearnworldsMeta = z.infer<typeof LearnworldsMetaSchema>;

// Get courses query parameters schema
export const GetLearnworldsCoursesParamsSchema = z.object({
  forceRefresh: z.boolean().default(false).optional(),
  page: z.number().positive().optional(),
  limit: z.number().positive().max(100).optional(),
});
export type IGetLearnworldsCoursesParams = z.infer<typeof GetLearnworldsCoursesParamsSchema>;

// Learnworlds courses response schema (for single page)
export const LearnworldsCoursesPageResponseSchema = z.object({
  data: z.array(LearnworldsCourseBaseSchema),
  meta: LearnworldsMetaSchema.optional(),
  success: z.boolean(),
  message: z.string().optional(),
});
export type ILearnworldsCoursesPageResponse = z.infer<typeof LearnworldsCoursesPageResponseSchema>;

// Learnworlds courses response schema (for all courses)
export const LearnworldsCoursesResponseSchema = PXActionResultSchema.extend({
  data: z.array(LearnworldsCourseBaseSchema).optional(),
});
export type ILearnworldsCoursesResponse = z.infer<typeof LearnworldsCoursesResponseSchema>;

// User enrollment schema
export const LearnworldsEnrollmentParamsSchema = z.object({
  email: z.string().email().nonempty(),
  customerFirstName: z.string().nonempty(),
  courseId: z.string().nonempty(),
  comment: z.string().optional(),
  notifyUser: z.boolean().optional().default(false),
});
export type ILearnworldsEnrollmentParams = z.infer<typeof LearnworldsEnrollmentParamsSchema>;

// User unenrollment schema
export const LearnworldsUnenrollmentParamsSchema = z.object({
  email: z.string().email().nonempty(),
  courseId: z.string().nonempty(),
});
export type ILearnworldsUnenrollmentParams = z.infer<typeof LearnworldsUnenrollmentParamsSchema>;

// User tags management schema
export const LearnworldsUserTagsParamsSchema = z.object({
  email: z.string().email().nonempty(),
  tags: z.array(z.string().nonempty()).min(1),
});
export type ILearnworldsUserTagsParams = z.infer<typeof LearnworldsUserTagsParamsSchema>;

// User suspension schema
export const LearnworldsUserSuspensionParamsSchema = z.object({
  email: z.string().email().nonempty(),
});
export type ILearnworldsUserSuspensionParams = z.infer<typeof LearnworldsUserSuspensionParamsSchema>;

// Learnworlds user schema
export const LearnworldsUserBaseSchema = z.object({
  id: z.string().nonempty(),
  email: z.string().email().nonempty(),
  username: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  created: z.string().optional(),
  updated: z.string().optional(),
});
export type ILearnworldsUserBase = z.infer<typeof LearnworldsUserBaseSchema>;

// Learnworlds user response schema
export const LearnworldsUserResponseSchema = PXActionResultSchema.extend({
  data: LearnworldsUserBaseSchema.optional(),
});
export type ILearnworldsUserResponse = z.infer<typeof LearnworldsUserResponseSchema>;

// Create user schema
export const CreateLearnworldsUserParamsSchema = z.object({
  email: z.string().email().nonempty(),
  customerFirstName: z.string().nonempty(),
});
export type ICreateLearnworldsUserParams = z.infer<typeof CreateLearnworldsUserParamsSchema>;

// Get user schema
export const GetLearnworldsUserParamsSchema = z.object({
  email: z.string().email().nonempty(),
});
export type IGetLearnworldsUserParams = z.infer<typeof GetLearnworldsUserParamsSchema>;
