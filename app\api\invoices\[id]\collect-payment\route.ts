import { collectPayment, initChargebee } from "@/server/chargebee";
import { CollectPaymentParams } from "@/types";
import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the POST request for collecting payment for an invoice.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the returned response or an error message.
 */
export async function POST(req: Request) {
  try {
    const body = (await req.json()) as CollectPaymentParams;
    if (!body.id) {
      return NextResponse.json(
        { error: "MISSING_INVOICE_ID" },
        { status: 400 }
      );
    }
    const response = await collectPayment(body);
    return NextResponse.json(response);
  } catch (error: any) {
    const errorMessage = error?.message ?? "UNKNOWN_ERROR";
    return NextResponse.json(
      { error: "CANNOT_COLLECT_PAYMENT", message: errorMessage },
      { status: 400 }
    );
  }
}
