"use client";

import {
  App,
  Typography,
  Modal,
  Divider,
  Skeleton,
  Descriptions,
  Row,
} from "antd";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

import UpdatePlanForm from "@/components/product-catalog/ProductPlans/forms/UpdatePlanForm";
import { useFetchProductPlan } from "@/hooks/product-catalog/productPlans";
import { useUserRole } from "@/hooks/roles";
import PlanDetailsHeader from "./PlanDetailsHeader";
import {
  activatePlan,
  archivePlan,
  duplicateProductPlan,
  updatePlan,
} from "@/lib/services";
import DuplicatePlanForm from "./forms/DuplicatePlanForm";

type PlanIdProps = {
  planId: string;
};

const { Text } = Typography;

const PlanDetails = ({ planId }: PlanIdProps) => {
  if (!planId) {
    throw new Error("Invalid Plan ID");
  }
  const router = useRouter();
  const { canAccess } = useUserRole();
  const { message, modal } = App.useApp();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = useState(false);

  // Queries
  const { data: plan, mutate: refetchPlan } = useFetchProductPlan(planId);
  const price = plan?.prices[0];

  const showModal = () => {
    if (canAccess("product-plans-update")) {
      setIsModalOpen(true);
    }
  };

  const handleCancel = async () => {
    modal.warning({
      title: "Are you sure you want to leave this page?",
      content: "You will lose all unsaved changes",
      afterClose: closeModal,
      okText: "Confirm",
      okButtonProps: { danger: true },
      cancelButtonProps: { ghost: true },
      cancelText: "Cancel",
    });
  };

  const archivePlanHandler = async () => {
    if (canAccess("product-plans-archive")) {
      if (plan?.id && price?.id) {
        await archivePlan({ planId: plan?.id, priceId: price?.id });
        await refetchPlan();
      }
    }
  };
  const activatePlanHandler = async () => {
    if (canAccess("product-plans-activate")) {
      if (plan?.id && price?.id) {
        await activatePlan({
          planId: plan?.id,
          priceId: price?.id,
        });
        await refetchPlan();
      }
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleUpdate = async (values: any) => {
    let res = await updatePlan({
      values,
      planId: plan?.id,
      priceId: price?.id,
    });
    if (res) {
      message.open({
        type: "success",
        content: "Plan updated!",
      });
    }
    await refetchPlan();
    setIsModalOpen(false);
  };

  const handlePlanDuplicate = async (values: any) => {
    if (plan && canAccess("product-plans-duplicate")) {
      let res = await duplicateProductPlan({
        amount: price?.amount,
        amountPerBillingCycle: price?.amountPerBillingCycle,
        billingCycle: "month",
        externalName: plan.externalName,
        fiscalEntity: plan.fiscalEntity,
        internalName: plan.internalName + " (copy)",
        status: plan.status,
        totalBillingCycles: price?.totalBillingCycles,
        productId: plan.product?.id,
        description: price?.description,
        crmSku: values.sku,
      });
      if (res?.plan) {
        message.open({
          type: "success",
          content: "Plan duplicated!",
        });
        router.push(`/dashboard/products/plan/${res.plan?.id}`);
      } else {
        message.open({
          type: "error",
          content: "Failed to duplicate plan",
        });
      }
      await refetchPlan();
    }
  };

  return plan === undefined || price === undefined ? (
    <Skeleton />
  ) : (
    <div>
      <PlanDetailsHeader
        plan={plan}
        onArchivePlan={archivePlanHandler}
        onActivatePlan={activatePlanHandler}
        onDuplicatePlan={() => setIsDuplicateModalOpen(true)}
        onOpenEditModal={showModal}
      />
      <Divider />
      <Descriptions title="Plan Settings" column={1} bordered size="small">
        <Descriptions.Item
          label={
            <>
              <span>Plan internal name</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Name that helps you identify this plan internally.
              </small>
            </>
          }
        >
          <Text copyable>{plan.internalName}</Text>
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Stock Keeping Unit (SKU)</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Used to have a unique identifier per plan. It&apos;s also used
                in Hubspot at the product level for the property of the same
                name.
              </small>
            </>
          }
        >
          <Text copyable>{plan.crmSku}</Text>
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Status</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Switching a product to Draft will disable all the plans &
                offers.
              </small>
            </>
          }
        >
          {plan.status === "active" ? "Active" : "Draft"}
        </Descriptions.Item>
      </Descriptions>
      <Divider />
      <Descriptions title="Price Settings" column={1} bordered size="small">
        <Descriptions.Item
          label={
            <>
              <span>Plan price</span>
              <br />
              <small style={{ color: "darkgray" }}>Price of the plan.</small>
            </>
          }
        >
          {price.amount} €
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <span>Billing cycles / Frequency</span>
              <br />
              <small style={{ color: "darkgray" }}>
                Choose if you’d like this plan to be billed forever, for a fixed
                period or one-time payment.
              </small>
            </>
          }
        >
          {price.periodUnit
            ?.charAt(0)
            .toUpperCase()
            .concat(price.periodUnit.slice(1))}
        </Descriptions.Item>
        {!plan.product?.isForever && (
          <Descriptions.Item
            label={
              <>
                <span># Billing cycles</span>
                <br />
                <small style={{ color: "darkgray" }}>
                  Total number of times the customer will be charged for this
                  plan.
                </small>
              </>
            }
          >
            {price.totalBillingCycles}
          </Descriptions.Item>
        )}
        <Descriptions.Item
          label={
            <>
              <span>Amount per billing cycle</span>
              <br />
              <small style={{ color: "darkgray" }}>
                This field is automatically computed based on your plan
                configuration (Billing cycles & #Billing Cycle).
              </small>
            </>
          }
        >
          {price.amountPerBillingCycle} €
        </Descriptions.Item>
      </Descriptions>
      {isModalOpen && (
        <UpdatePlanForm
          handleCancel={handleCancel}
          handleUpdate={handleUpdate}
          isModalOpen={isModalOpen}
          formValues={plan}
        />
      )}
      {isDuplicateModalOpen && (
        <DuplicatePlanForm
          currentPlan={plan}
          handleCancel={handleCancel}
          handleUpdate={handlePlanDuplicate}
          isModalOpen={isDuplicateModalOpen}
        />
      )}
    </div>
  );
};

export default PlanDetails;
