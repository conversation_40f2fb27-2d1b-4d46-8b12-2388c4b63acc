import { Injectable } from '@nestjs/common';
import { LearnworldsService } from '@services/lms/learnworlds/learnworlds.service';
import { IGetLearnworldsCoursesParams, ILearnworldsCoursesListResponse } from '@px-shared-account/hermes';

@Injectable()
export class LearnworldsUseCases {
  constructor(private readonly learnworldsService: LearnworldsService) { }

  /**
   * Retrieves courses from Learnworlds
   * @param params Query parameters for pagination and filtering
   * @returns Promise with courses data
   */
  async getCourses(params: IGetLearnworldsCoursesParams = { forceRefresh: false }): Promise<ILearnworldsCoursesListResponse> {
    const { forceRefresh = false, page, limit } = params;
    return this.learnworldsService.getCourses(forceRefresh, page, limit);
  }
}


