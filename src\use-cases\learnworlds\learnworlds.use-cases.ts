import { Injectable } from '@nestjs/common';
import { LearnworldsService } from '@services/lms/learnworlds/learnworlds.service';
import { PXActionResult } from '@px-shared-account/hermes';
import { IGetLearnworldsCoursesParams } from '@services/lms/learnworlds/core';

@Injectable()
export class LearnworldsUseCases {
  constructor(private readonly learnworldsService: LearnworldsService) { }

  /**
   * Retrieves courses from Learnworlds
   * @param params Query parameters for pagination and filtering
   * @returns Promise with courses data
   */
  async getCourses(params: IGetLearnworldsCoursesParams = {}): Promise<PXActionResult> {
    const { forceRefresh = false, page, limit } = params;
    return this.learnworldsService.getCourses(forceRefresh, page, limit);
  }
}


