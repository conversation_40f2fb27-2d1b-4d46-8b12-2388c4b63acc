import { Injectable } from '@nestjs/common';
import { LearnworldsService } from '@services/lms/learnworlds/learnworlds.service';
import { PXActionResult } from '@px-shared-account/hermes';

@Injectable()
export class LearnworldsUseCases {
  constructor(private readonly learnworldsService: LearnworldsService) {}

  /**
   * Retrieves courses from Learnworlds
   * @param forceRefresh Whether to bypass cache and fetch fresh data
   * @param page Specific page to fetch (optional, if not provided fetches all pages)
   * @param limit Number of courses per page (optional)
   * @returns Promise with courses data
   */
  async getCourses(forceRefresh = false, page?: number, limit?: number): Promise<PXActionResult> {
    return this.learnworldsService.getCourses(forceRefresh, page, limit);
  }

  /**
   * Gets a user from Learnworlds by email
   * @param email User email
   * @returns Promise with user data
   */
  async getUser(email: string): Promise<PXActionResult> {
    return this.learnworldsService.getUser(email);
  }
}


