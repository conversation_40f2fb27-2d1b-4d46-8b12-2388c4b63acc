"use client";

import { Form, Input, Select, Typography } from "antd";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { useFetchProductLines } from "@/hooks/product-catalog/productLines";
import { ProductLine } from "@/types";

type UpdateProps = {
  isModalOpen: boolean;
  formValues: ProductLine | undefined;
  handleUpdate: Function;
  handleCancel: Function;
};

const { Text, Title } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

const UpdateProductFamilyForm = ({
  isModalOpen,
  formValues,
  handleUpdate,
  handleCancel,
}: UpdateProps) => {
  const [form] = Form.useForm();
  const { data: productLines } = useFetchProductLines();
  form.setFieldsValue(formValues);
  form.setFieldValue("productLine", formValues?.productLine?.id);

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      onCancel={() => handleCancel()}
      onClose={() => handleCancel()}
      onSubmit={() => {
        form.submit();
      }}
      submitText="Update"
      title="Update product family"
    >
      <Title level={5}>Product family settings</Title>
      <Form
        form={form}
        onFinish={(values) => {
          handleUpdate(values);
        }}
        layout="vertical"
        style={{ maxWidth: 800 }}
      >
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          label="Product family name"
          name="name"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="Product family name" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this product family internally.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product family description"
          name="description"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input placeholder="EDEC" />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product family description attached to this product.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Status"
          name="status"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            options={[
              { value: "active", label: "Active" },
              { value: "draft", label: "Draft" },
            ]}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Changing a product family to ‘Draft’ status will not affect products &
          offers. The draft product family will not be usable in product
          creation.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product line"
          name="productLine"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            allowClear
            placeholder="PXL"
            options={productLines?.items.map((val) => {
              return { value: val.id, label: val.name };
            })}
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product line which contains this product family.
        </Text>
        <Form.Item
          label="Chargebee Id"
          name="chargebeeId"
          style={{ marginBottom: "0px" }}
        >
          <Input disabled />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Only used by Tech.
        </Text>
        <br />
        <br />
      </Form>
    </DrawerFormContainer>
  );
};

export default UpdateProductFamilyForm;
