"use client";

import {
  ArrowLeftOutlined,
  CloseOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { App, <PERSON><PERSON>, <PERSON><PERSON>r, Drawer, Row, Space, Typography } from "antd";
import React, { useEffect, useState } from "react";

import { ConfigureOfferProducts } from "./ConfigureOfferProducts";
import { AddProductModal } from "../modals/AddProductModal";
import { AddRecommendedProductModal } from "../modals/AddRecommendedProductModal";
import {
  useFetchProducts,
  useFetchUniqueBillingCycles,
} from "@/hooks/product-catalog/products";
import { OfferConfig, Product, PlanDTO } from "@/types";
import {
  createOfferProducts,
  getProductsForBillingCycles,
  patchOfferProducts,
} from "@/lib/services";

type AddProps = {
  isModalOpen: boolean;
  handleCancel: Function;
  offerID: string;
  initialValues?: OfferConfig;
  refreshOffer: Function;
};

export type FilterOfferProps = {
  availableBC: string[];
  defaultBC: string;
  display: string;
  filteredProducts: Product[];
  quantity: number[];
  recommendedQty: boolean;
  selectedBC: string[];
  selectedProductPlans: Map<number, Map<string, string>>; // productID => billingCycle, planID
  selectedProducts: Product[];
  selectedRecommended?: Product;
  selectedRecommendedPlans: Map<string, string>;
};

const { Text } = Typography;

const initialFilters: FilterOfferProps = {
  availableBC: [],
  defaultBC: "",
  display: "offer",
  filteredProducts: [],
  quantity: [],
  recommendedQty: false,
  selectedBC: [],
  selectedProductPlans: new Map(),
  selectedProducts: [],
  selectedRecommendedPlans: new Map(),
};

const ConfigureOfferProductsForm = ({
  isModalOpen,
  handleCancel,
  offerID,
  initialValues,
  refreshOffer,
}: AddProps) => {
  const { message } = App.useApp();
  const [filters, setFilters] = useState<FilterOfferProps>(initialFilters);

  // Modal states
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [showAddRecommendedModal, setShowAddRecommendedModal] = useState(false);

  // Queries
  const { data: totalBillingCycles } = useFetchUniqueBillingCycles();
  const { data: products } = useFetchProducts({
    filters: {
      page: 1,
      status: "active",
    },
  });

  const billingCyclesChanged = async (values: string[]) => {
    // if no billing cycle is selected, set the first one as default
    // OR if default billing cycle is not in the selected billing cycles, set the first one as default
    if (
      (!filters.defaultBC && values.length > 0) ||
      (filters.defaultBC &&
        !values.includes(filters.defaultBC) &&
        values.length > 0)
    ) {
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        defaultBC: values[0],
      }));
    }
    // if no billing cycle is selected, set the default billing cycle to empty
    if (values.length === 0) {
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        selectedBC: values,
        filteredProducts: [],
        selectedProducts: [],
        selectedRecommended: undefined,
      }));
      return;
    }
    // set the selected billing cycles
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      selectedBC: values.sort((a, b) => Number(a) - Number(b)),
    }));
    // if selected products, add or remove the plan from selectedProductPlans
    if (filters.selectedProducts) {
      const tempMap = new Map(filters.selectedProductPlans);
      filters.selectedProducts.forEach((item) => {
        let planMap = new Map<string, string>();
        let existingPlans = tempMap.get(item.id);
        existingPlans?.forEach((value, key) => {
          if (values.includes(key)) {
            planMap.set(key, value);
          }
        });
        values.forEach((element) => {
          let currentPlan = item.plans.find(
            (x) => element === x.prices[0].totalBillingCycles.toString()
          );
          if (currentPlan?.id && !planMap.has(element)) {
            planMap.set(element, currentPlan.id.toString());
          }
        });

        // Remove any productPlan that is not present in the values
        Array.from(planMap.keys()).forEach((key) => {
          if (!values.includes(key)) {
            planMap.delete(key);
          }
        });
        tempMap.set(item.id, new Map<string, string>(planMap));
      });
      // Update selectedProducts
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        selectedProductPlans: tempMap,
      }));
    }
    // if selected recommended product, add or remove the plan from selectedRecommendedPlans
    if (filters.selectedRecommended) {
      let planMap = new Map<string, string>();
      let existingPlans = filters.selectedRecommendedPlans;
      existingPlans?.forEach((value, key) => {
        if (values.includes(key)) {
          planMap.set(key, value);
        }
      });
      values.forEach((element) => {
        let currentPlan = filters.selectedRecommended?.plans.find(
          (x) => element === x.prices[0].totalBillingCycles.toString()
        );
        if (currentPlan?.id && !planMap.has(element)) {
          planMap.set(element, currentPlan.id.toString());
        }
      });

      // Remove any productPlan that is not present in the values
      Array.from(planMap.keys()).forEach((key) => {
        if (!values.includes(key)) {
          planMap.delete(key);
        }
      });
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        selectedRecommendedPlans: new Map<string, string>(planMap),
      }));
    }
    // Fetch products with billing cycles, and filter the products based on the selected billing cycles
    const res = await getProductsForBillingCycles(values);
    if (res && products?.items.length) {
      const filtered = products.items.filter((item: Product) => {
        return res.includes(item.id) && item.status === "active";
      });
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        filteredProducts: filtered,
      }));
    }
  };

  const submitValues = async () => {
    let payload: any = {
      isForever: false,
      products: {},
      recommended: {},
      quantityInfo: {},
      display: filters.display,
      defaultBillingCycle: filters.defaultBC,
    };
    if (filters.selectedProducts) {
      filters.selectedProducts.forEach((item: Product) => {
        let map = filters.selectedProductPlans.get(item.id);
        payload.products[item.id] = [];
        map?.forEach((value: string, key: string) => {
          let planChargebeeID = item.plans.find(
            (x) => x.id === Number(value)
          )?.chargebeeId;
          if (planChargebeeID) {
            let tempObj = {
              [key]: planChargebeeID,
            };
            payload.products[item.id].push(tempObj);
          }
        });
      });
      if (filters.selectedRecommended) {
        payload.recommended[filters.selectedRecommended.id] = [];

        filters.selectedRecommendedPlans.forEach(
          (value: string, key: string) => {
            let planChargebeeID = (
              filters?.selectedRecommended as Product
            ).plans.find((x) => x.id === Number(value))?.chargebeeId;
            if (planChargebeeID) {
              let tempObj = {
                [key]: planChargebeeID,
              };
              payload.recommended[
                (filters?.selectedRecommended as Product).id
              ].push(tempObj);
            }
          }
        );
        payload.quantityInfo["recommended"] = {
          [filters.selectedRecommended.id]: filters.recommendedQty,
        };
      }

      let prodQuantities = {};
      if (filters.quantity) {
        filters.selectedProducts.forEach((item) => {
          let tempObj = {
            [item.id]: filters.quantity.includes(item.id) ? true : false,
          };
          prodQuantities = { ...prodQuantities, ...tempObj };
        });
      } else {
        filters.selectedProducts.forEach((item) => {
          let tempObj = {
            [item.id]: false,
          };
          prodQuantities = { ...prodQuantities, ...tempObj };
        });
      }
      payload.quantityInfo["products"] = prodQuantities;
      if (initialValues) {
        const res = await patchOfferProducts({
          values: payload,
          id: offerID,
        });
        if (res === 200 || res === 201) {
          message.open({
            type: "success",
            content: "Offer products updated successfully!",
          });
          handleCancel();
          await refreshOffer();
        }
      } else {
        const res = await createOfferProducts({
          values: payload,
          id: offerID,
        });
        if (res === 200 || res === 201) {
          message.open({
            type: "success",
            content: "Offer products added successfully!",
          });
          handleCancel();
          await refreshOffer();
        }
      }
    }
  };

  useEffect(() => {
    if (!totalBillingCycles) return;
    let tempArr: number[] = totalBillingCycles;
    // If selectedProducts is present, filter the billing cycles based on the selected products
    if (filters.selectedProducts && filters.selectedProducts?.length > 0) {
      filters.selectedProducts.forEach((item) => {
        let planBillingCycles: number[] = [];
        item.plans.forEach((plan) => {
          if (plan.prices[0]?.totalBillingCycles)
            planBillingCycles.push(Number(plan.prices[0].totalBillingCycles));
        });
        tempArr = tempArr.filter((x) => planBillingCycles.includes(x));
      });
    }
    // If selectedRecommended is present, filter the billing cycles based on the selected recommended product
    if (filters.selectedRecommended) {
      let planBillingCycles: number[] = [];
      filters.selectedRecommended.plans.forEach((plan) => {
        if (plan.prices[0]?.totalBillingCycles)
          planBillingCycles.push(Number(plan.prices[0].totalBillingCycles));
      });
      tempArr = tempArr.filter((x) => planBillingCycles.includes(x));
    }
    // Set the available billing cycles
    setFilters((prev: FilterOfferProps) => ({
      ...prev,
      availableBC: tempArr.map((item) => item.toString()),
    }));
  }, [
    filters.selectedProducts,
    filters.selectedRecommended,
    totalBillingCycles,
  ]);

  useEffect(() => {
    if (initialValues && initialValues?.products && products?.items.length) {
      const keys = Object.values(initialValues.products)
        .flat()
        .map((obj: any) => {
          return Object.keys(obj)[0];
        });
      billingCyclesChanged(
        keys.filter((item, index) => keys.indexOf(item) === index)
      );
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        display: initialValues.display,
        selectedProducts: products?.items.filter((item) => {
          return Object.keys(initialValues.products).includes(
            item.id.toString()
          );
        }),
      }));
      // Prepare recommended product
      const recommProd = products.items.find((x: Product) =>
        Object.keys(initialValues.recommended).includes(x.id.toString())
      );
      const selectedProducts = products.items.filter((item: Product) => {
        return Object.keys(initialValues.products).includes(item.id.toString());
      });
      console.log({ initialValues, selectedProducts });
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        selectedProducts,
        selectedRecommended: recommProd,
        selectedProductPlans: new Map(
          Object.entries(initialValues.products).map(([key, value]) => {
            let tempMap = new Map<string, string>();
            Object.entries(value).forEach((item) => {
              let billingCycle = Object.keys(item[1])[0];
              let planChargebeeID = Object.values(item[1])[0];
              let prod = products.items.find(
                (x: Product) => x.id === Number(key)
              );
              let planID = prod?.plans.find(
                (x: PlanDTO) => x.chargebeeId === planChargebeeID
              )?.id;
              if (planID) {
                tempMap.set(billingCycle, planID.toString());
              }
            });
            return [Number(key), tempMap];
          })
        ),
      }));
      if (recommProd) {
        let selectedRecommendedProdId = Object.keys(
          initialValues.recommended
        )[0];
        let selectedRecommendedPlanMap = new Map<string, string>();
        Object.entries(
          initialValues.recommended[selectedRecommendedProdId]
        ).forEach((item) => {
          let billingCycle = Object.keys(item[1])[0];
          let planChargebeeID = Object.values(item[1])[0];
          let planID = recommProd?.plans.find(
            (x: PlanDTO) => x.chargebeeId === planChargebeeID
          )?.id;
          if (planID) {
            selectedRecommendedPlanMap.set(billingCycle, planID.toString());
          }
        });
        setFilters((prev: FilterOfferProps) => ({
          ...prev,
          selectedRecommendedPlans: new Map(selectedRecommendedPlanMap),
        }));
      }
      if (initialValues.quantityInfo.recommended) {
        setFilters((prev: FilterOfferProps) => ({
          ...prev,
          recommendedQty: Object.values(
            initialValues.quantityInfo.recommended
          )[0],
        }));
      }
      let tempArr: number[] = [];
      Object.entries(initialValues.quantityInfo.products).forEach((item) => {
        if (item[1]) {
          tempArr.push(Number(item[0]));
        }
      });
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        defaultBC: initialValues.defaultBillingCycle,
        quantity: tempArr,
      }));
    }
  }, [initialValues?.defaultBillingCycle, products?.total]);

  if (!isModalOpen) return;

  return (
    <Drawer
      width={"100vw"}
      open={isModalOpen}
      onClose={() => {
        handleCancel;
      }}
      closeIcon={false}
      destroyOnClose={true}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          paddingBottom: "40px",
        }}
      >
        <div>
          <Row
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              paddingRight: "30px",
              paddingLeft: "30px",
            }}
          >
            <div
              style={{
                display: "flex",
                width: "60%",
                justifyContent: "start",
                alignItems: "center",
              }}
            >
              <Space>
                <Button
                  icon={<ArrowLeftOutlined style={{ color: "#5B5B5D" }} />}
                  onClick={() => {
                    handleCancel();
                  }}
                />
                <Text
                  style={{ fontSize: 24 }}
                >{`Configure offer's products`}</Text>
              </Space>
            </div>
            <Space>
              <Button
                icon={<CloseOutlined />}
                onClick={() => {
                  handleCancel();
                }}
              >
                <Text strong>Dismiss</Text>
              </Button>
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={submitValues}
              >
                <Text style={{ color: "white" }} strong>
                  {initialValues ? "Update" : "Save"}
                </Text>
              </Button>
            </Space>
          </Row>
          <Divider />
          <ConfigureOfferProducts
            billingCyclesChanged={billingCyclesChanged}
            filters={filters}
            totalBillingCycles={totalBillingCycles as number[]}
            setFilters={setFilters}
            setShowAddProductModal={setShowAddProductModal}
            setShowAddRecommendedModal={setShowAddRecommendedModal}
          />
          {showAddProductModal && (
            <AddProductModal
              isOpen={showAddProductModal}
              filters={filters}
              products={products?.items as Product[]}
              setIsOpen={setShowAddProductModal}
              setFilters={setFilters}
            />
          )}
          {showAddRecommendedModal && (
            <AddRecommendedProductModal
              isOpen={showAddRecommendedModal}
              setIsOpen={setShowAddRecommendedModal}
              filters={filters}
              products={products?.items as Product[]}
              setFilters={setFilters}
            />
          )}
        </div>
      </div>
    </Drawer>
  );
};

export default ConfigureOfferProductsForm;
