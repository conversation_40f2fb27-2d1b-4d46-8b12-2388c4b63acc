"use client";

import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Col, Input, Row, Select, Space } from "antd";

import { ProductsFilter } from "@/types";

type ProductsFilterToolProps = {
  filters: ProductsFilter;
  productFamilies: any;
  productLines: any;

  setFilters: Function;
  onCreateProduct: Function;
  onProductFamilyChange: Function;
  onProductLineChange: Function;
};

export default function ProductsFilterTool({
  filters,
  productFamilies,
  productLines,
  onCreateProduct,
  onProductFamilyChange,
  onProductLineChange,
  setFilters,
}: ProductsFilterToolProps) {
  return (
    <Row gutter={[0, 0]} style={{ lineHeight: "10px" }}>
      <Col xs={12} sm={12} md={16} lg={16} xl={17} xxl={19}>
        <Input
          prefix={<SearchOutlined />}
          placeholder="Search in products"
          onChange={(event) =>
            setFilters({ ...filters, search: event.target.value })
          }
          value={filters.search}
        />
        <Space
          style={{
            display: "flex",
            marginTop: "10px",
          }}
        >
          <Select
            allowClear
            onChange={(payload) => onProductLineChange(payload)}
            placeholder="Product line"
            options={productLines?.items.map((item: any) => {
              return { value: item.id, label: <span>{item.name}</span> };
            })}
            style={{ width: "200px" }}
            value={filters.productLine}
          />

          <Select
            allowClear
            onChange={(payload) => onProductFamilyChange(payload)}
            placeholder="Product family"
            style={{ width: "200px" }}
            options={
              productFamilies &&
              productFamilies?.items.map((item: any) => {
                return {
                  value: item.id,
                  label: <span>{item.name}</span>,
                };
              })
            }
            value={filters.productFamily}
          />
          <Select
            value={filters.status}
            allowClear
            style={{ width: "150px" }}
            onChange={(values) => setFilters({ ...filters, status: values })}
            placeholder="Status"
            options={[
              { value: "active", label: <span>Active</span> },
              { value: "draft", label: <span>Draft</span> },
            ]}
          />
          <Button
            type="link"
            onClick={() => {
              setFilters((prev: ProductsFilter) => ({
                ...prev,
                search: "",
                status: null,
                productFamily: null,
                productLine: null,
              }));
            }}
          >
            Clear Filters
          </Button>
        </Space>
      </Col>
      <Col xs={11} sm={11} md={7} lg={7} xl={6} xxl={4} offset={1}>
        <Button
          style={{
            backgroundColor: "#7351EE",
            color: "white",
            width: "100%",
          }}
          icon={<PlusOutlined />}
          onClick={() => onCreateProduct()}
          data-testid="create-product-btn"
        >
          Create a product
        </Button>
      </Col>
    </Row>
  );
}
