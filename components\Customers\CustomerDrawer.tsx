"use client";

import {
  Descriptions,
  Divider,
  Drawer,
  Tabs,
  TabsProps,
  Typography,
} from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { Customer } from "chargebee";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useMemo, useState } from "react";

import CustomerInfo from "./CustomerInfo";
import GoliathsInfo from "./GoliathsInfo";
import { useFetchCustomerAccesses } from "@/hooks/customers";
import CustomerAccesses from "./Accesses/CustomerAccesses";
import { fetchCustomer } from "@/lib/services/chargebee";
import { getCustomerLink } from "@/lib/chargebee";
import LinkChargebee from "../core/LinkChargebee";
import DebugAccesses from "./DevTools/DebugAccesses";

type CustomerDetailsProps = {
  open: boolean;
};

const { Text } = Typography;

export default function CustomerDrawer({ open }: CustomerDetailsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [drawerData, setDrawerData] = useState<{
    customerData: Customer | null;
    isLoading: boolean;
  }>({
    customerData: null,
    isLoading: true,
  });

  const { data: customerAccessesData, mutate: refetchCustomerAccesses } =
    useFetchCustomerAccesses({ email: searchParams.get("email") as string });

  useEffect(() => {
    if (open && searchParams.get("customer")) {
      Promise.all([
        fetchCustomer(searchParams.get("customer") as string),
        refetchCustomerAccesses(),
      ]).then(([res, _]) => {
        setDrawerData({
          customerData: res.customer,
          isLoading: false,
        });
      });
    }
  }, [open]);

  const tabItems: TabsProps["items"] = useMemo(
    () => [
      {
        key: "1",
        label: "Information",
        children: (
          <CustomerInfo
            data={drawerData?.customerData}
            onUpdate={() => {
              const url = new URL(window.location.href);
              url.searchParams.delete("customer");
              window.history.replaceState({}, "", url.toString());
              window.location.reload();
            }}
          />
        ),
      },
      {
        key: "2",
        label: `Accesses`,
        children: (
          <CustomerAccesses
            accesses={customerAccessesData}
            onAccessUpdateSuccess={() => refetchCustomerAccesses()}
          />
        ),
      },
      {
        key: "3",
        label: "Dev tools",
        children: (
          <DebugAccesses email={drawerData?.customerData?.email || ""} />
        ),
      },
      {
        key: "4",
        label: "Goliaths",
        children: (
          <GoliathsInfo discountCode={customerAccessesData?.discountCode} />
        ),
      },
    ],
    [drawerData?.customerData, customerAccessesData]
  );

  return (
    <Suspense>
      <Drawer
        placement="right"
        size="large"
        closable={false}
        onClose={() => router.push(pathname)}
        closeIcon={<CloseOutlined />}
        destroyOnClose={true}
        open={open}
        title={drawerData?.customerData?.email}
        loading={drawerData?.isLoading}
      >
        <Descriptions title="Customer Information" bordered size="small">
          <Descriptions.Item label="Chargebee ID">
            <LinkChargebee
              url={getCustomerLink(drawerData?.customerData?.id || "")}
            >
              {drawerData?.customerData?.id}
            </LinkChargebee>
          </Descriptions.Item>
          <Descriptions.Item label="Email">
            <Text copyable>{drawerData?.customerData?.email}</Text>
          </Descriptions.Item>
        </Descriptions>
        <Divider />
        <Tabs
          items={tabItems}
          defaultActiveKey="1"
          size="small"
          tabPosition="left"
        />
      </Drawer>
    </Suspense>
  );
}
