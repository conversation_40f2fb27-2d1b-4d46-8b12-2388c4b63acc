"use client";

import { Space, Button, Typography, Tag, Descriptions } from "antd";
import { DownloadOutlined } from "@ant-design/icons";

import { formatDateFromUnix } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { Invoice } from "@/types";
import PageHeader from "@/components/core/PageHeader";
import { getCustomerLink } from "@/lib/chargebee";
import Link from "next/link";
import LinkChargebee from "@/components/core/LinkChargebee";

type InvoiceHeaderProps = {
  invoice: Invoice;
  invoiceId: string;
  onBack: () => void;
  onDownloadPDF: () => void;
};

const { Text } = Typography;

const generateStatusTag = (status: string | undefined) => {
  if (!status) {
    return <Tag>Unknown</Tag>;
  }
  switch (status) {
    case "paid":
      return <Tag color="green">Paid</Tag>;
    case "not_paid":
      return <Tag color="orange">Not paid</Tag>;
    case "payment_due":
      return <Tag color="red">Payment due</Tag>;
    default:
      return <Tag>{capitalizeWord(status)}</Tag>;
  }
};

export const InvoiceHeader = ({
  invoice,
  invoiceId,
  onBack,
  onDownloadPDF,
}: InvoiceHeaderProps) => {
  return (
    <PageHeader
      title="Invoice Details"
      actions={
        <Space>
          <Button
            icon={<DownloadOutlined />}
            onClick={onDownloadPDF}
            type="primary"
          >
            Download PDF
          </Button>
        </Space>
      }
    >
      <Descriptions title="" column={3} bordered size="small">
        <Descriptions.Item label="ID">
          <Text copyable={{ text: invoice.chargebeeId }}>
            {invoice.chargebeeId}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="Date">
          {formatDateFromUnix(invoice.date)}
        </Descriptions.Item>
        <Descriptions.Item label="Status">
          {generateStatusTag(invoice.status)}
        </Descriptions.Item>
        <Descriptions.Item label="Customer">
          <LinkChargebee url={getCustomerLink(invoice.customerId as string)}>
            {invoice.customerId}
          </LinkChargebee>
        </Descriptions.Item>
        <Descriptions.Item label="Subscription related to">
          <Link
            passHref={true}
            target="_blank"
            href={`/dashboard/subscriptions/${invoice.subscriptionId}`}
          >
            {invoice.subscriptionId}
          </Link>
        </Descriptions.Item>
      </Descriptions>
    </PageHeader>
  );
};
