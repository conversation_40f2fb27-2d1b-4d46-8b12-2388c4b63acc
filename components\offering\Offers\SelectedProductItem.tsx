"use client";

import { Product } from "@/types";
import { Image, Popover, Typography } from "antd";
import { SelectedPlanItem } from "./SelectedPlanItem";

const { Text } = Typography;

export const SelectedProductItem = (params: {
  product: Product;
  selectedBillingCycles: string[];
  passedPlans: Map<string, string> | undefined;
  handlePlanChanged: Function;
  index?: number;
}) => {
  const handleSelectedPlanChange = (cycle: string, planId: number) => {
    params.handlePlanChanged(params.product.id, cycle, planId.toString());
  };

  return (
    <div
      key={params.product.id}
      style={{
        width: "90%",
        maxWidth: "300px",
        marginLeft: params.index === 0 ? "0px" : "0px",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
          backgroundColor: "white",
          border: "1px solid lightgray",
          padding: "5px",
          borderRadius: "5px",
        }}
      >
        <div
          style={{
            width: "30%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Image src={params.product.image} height={50} />
        </div>
        <div
          style={{
            width: "70%",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            marginLeft: "5px",
            cursor: "default",
          }}
        >
          <Popover title={params.product.externalName}>
            {params.product.externalName}
          </Popover>{" "}
          <br />
          <Text style={{ fontSize: 13, color: "darkgray" }}>
            {params.product.plans.length} Plans
          </Text>
        </div>
      </div>
      <br />
      <br />
      {params.selectedBillingCycles.map((cycle) => {
        let allPlans = params.product.plans.filter((x) => {
          return (
            x.prices[0]?.totalBillingCycles &&
            x.prices[0]?.totalBillingCycles === Number(cycle)
          );
        });
        return (
          <SelectedPlanItem
            plans={allPlans}
            billingCycle={cycle}
            image={params.product.image}
            key={cycle}
            onChange={handleSelectedPlanChange}
            selectedPlan={params.passedPlans?.get(cycle)}
          />
        );
      })}
    </div>
  );
};
