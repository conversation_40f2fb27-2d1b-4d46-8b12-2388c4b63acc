"use client";

import { Modal, Select } from "antd";

import { Course, CourseInfo } from "@/types";
import { useMemo } from "react";

type SelectCoursesProps = {
  isOpen: boolean;
  courses: Course[];
  selectedCourses: CourseInfo[];
  onClose: () => void;
  onSelectCourse: (value: string[]) => void;
};

export default function SelectCoursesModal({
  isOpen,
  courses,
  selectedCourses,
  onClose,
  onSelectCourse,
}: SelectCoursesProps) {
  const options = useMemo(() => {
    return courses.map((item: Course) => {
      return {
        value: item.id,
        label: <span>{item.title}</span>,
      };
    });
  }, [courses]);

  return (
    <Modal
      open={isOpen}
      title="Add course"
      onCancel={onClose}
      onOk={() => {
        onClose();
      }}
    >
      <div style={{ width: "100%" }}>
        <Select
          style={{ width: "100%" }}
          options={options}
          mode="multiple"
          value={selectedCourses.map((course) => course?.id)}
          allowClear
          placeholder="Select all the applicable courses"
          onChange={(value) => {
            onSelectCourse(value);
          }}
        />
      </div>
    </Modal>
  );
}
