"use client";

import {
  DownOutlined,
  FieldTimeOutlined,
  MoneyCollectOutlined,
  RedoOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import {
  App,
  Button,
  Col,
  Divider,
  Input,
  InputNumber,
  Progress,
  Radio,
  Row,
  Image,
  Space,
  Tag,
  Typography,
  Form,
  Table,
  Alert,
  Select,
  DatePicker,
} from "antd";
import { useEffect, useMemo, useState } from "react";

import { useFetchOffers } from "@/hooks/offering/offers";
import { formatCents } from "@/lib/currency";
import { convertDateToUnix, formatDateFromUnix } from "@/lib/date";
import {
  ChangeSubscriptionDisplayProduct,
  DownsellSubscriptionParams,
  Invoice,
  LineItemWithPrice,
  Offer,
  PriceInfo,
  SubscriptionHistoricalData,
} from "@/types";
import {
  downsellSubscription,
  getOfferDetails,
  getSubscriptionOffer,
  getSubscriptionProducts,
} from "@/lib/services";
import { getPlanPrice } from "@/lib/services/chargebee";
import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import ProductInfo from "./ProductInfo";

type DownsellProps = {
  isModalOpen: boolean;
  closeModal: Function;
  subscriptionId: string;
  history: SubscriptionHistoricalData;
  nextBillingDate: string;
  remainingBillingCycles?: number;
};

type Details = {
  amountPaid: number;
  total: number;
  remaining: number;
};

type OfferDetails = {
  configuredBillingCycles: string[];
  defaultBillingCycle: number;
  offer: Offer;
  offerProducts: DownsellProduct[];
};

type DownsellProduct = {
  id: number;
  image: string;
  description: string;
  name: string;
  prices: PriceInfo[];
};

type OriginalOffer = {
  offerName: string;
  billingCycles: string[];
};

const { Text } = Typography;

const Downsell = ({
  isModalOpen,
  closeModal,
  subscriptionId,
  history,
  nextBillingDate,
}: DownsellProps) => {
  const { message } = App.useApp();
  // States
  const [selectedBillingCycle, setSelectedBillingCycle] = useState<number>(0);
  const [configuredBillingCycles, setConfiguredBillingCycles] = useState<
    string[]
  >([]);
  const [oldProdConfig, setOldProdConfig] = useState<
    ChangeSubscriptionDisplayProduct[]
  >([]);
  const [newProdConfig, setNewProdConfig] = useState<
    ChangeSubscriptionDisplayProduct[]
  >([]);
  const [oldBillingCycle, setOldBillingCycle] = useState<number>(0);
  const [details, setDetails] = useState<Details>({
    amountPaid: 0,
    total: 0,
    remaining: 0,
  });
  const [selectedOffer, setSelectedOffer] = useState<OfferDetails>();
  const [originalOffer, setOriginalOffer] = useState<OriginalOffer>();

  const newOderTotal = useMemo(() => {
    return newProdConfig.reduce((acc, item) => {
      if (item.pricePerBillingCycle && item.billingCycles && item.quantity) {
        acc += item.billingCycles * item.pricePerBillingCycle * item.quantity;
      }
      return acc;
    }, 0);
  }, [newProdConfig]);

  // Forms
  const [offerForm] = Form.useForm();
  const [billingCycleForm] = Form.useForm();
  const [newSubscriptionDetailsForm] = Form.useForm();

  // Mutations
  const { data: offers } = useFetchOffers({});

  useEffect(() => {
    getProducts();
    getOriginalOffer();
  }, []);

  const getOriginalOffer = async () => {
    const offer = await getSubscriptionOffer(subscriptionId);
    setOriginalOffer(offer);
  };

  const getProducts = async () => {
    let offerProds = await getSubscriptionProducts(subscriptionId);
    if (offerProds?.products && offerProds?.products.length > 0) {
      let offerProducts: LineItemWithPrice[] = offerProds?.products;
      let remainingBillingCycles =
        offerProducts[0]?.billingCyclesRemaining || 0;
      // need to calculate it this way to cater to the case where the subscription
      // billing cycles have been edited.
      setOldBillingCycle(remainingBillingCycles + history.invoices.length);
      setSelectedBillingCycle(remainingBillingCycles);

      // Calculating the total amounts for the current subscription
      const paid = history.invoices.reduce((acc: number, item: Invoice) => {
        // @ts-ignore
        acc += item?.amountPaid;
        return acc;
      }, 0);
      const linesTotal = offerProds?.products.reduce(
        (acc: number, item: any) => {
          // @ts-ignore
          acc += item?.amountPerBillingCycle - item?.discount_amount;
          return acc;
        },
        0
      );
      const remaining = linesTotal * remainingBillingCycles;
      const total = paid + remaining;
      setDetails({ amountPaid: paid, total, remaining });

      setOldProdConfig(
        offerProducts.map((prod) => {
          return {
            id: prod.entity_id,
            image: prod.image,
            name: prod.name,
            billingCycles: prod?.billingCyclesRemaining,
            pricePerBillingCycle: prod?.amountPerBillingCycle,
            quantity: prod.quantity,
            total: prod?.price?.amount,
          };
        })
      );
      setNewProdConfig(
        offerProducts.map((prod) => {
          return {
            id: prod.entity_id,
            image: prod.image,
            name: prod.name,
            billingCycles: prod?.billingCyclesRemaining,
            pricePerBillingCycle: prod?.amountPerBillingCycle,
            quantity: prod.quantity,
            total: prod?.price?.amount,
          };
        })
      );
    }
  };

  const handlePriceChange = (val: any, record: any) => {
    setNewProdConfig((prev) => {
      let prodConfig = [...prev];
      const index = prodConfig.findIndex((prod) => prod.id === record.id);
      prodConfig[index].pricePerBillingCycle = val * 100;
      const productQuantity = prodConfig[index]?.quantity;
      if (productQuantity && selectedBillingCycle) {
        prodConfig[index].total = val * productQuantity * selectedBillingCycle;
      }
      return prodConfig;
    });
  };

  const handleBillingCycleChange = (val: number) => {
    if (val > 0) {
      billingCycleForm.setFieldValue("billingCycleRadio", val);
      setSelectedBillingCycle(Number(val));
      updateNewProdConfig(Number(val));
    } else {
      billingCycleForm.setFieldValue("billingCycleRadio", "0");
      setSelectedBillingCycle(1);
      updateNewProdConfig(1);
    }
  };

  const handleCustomBillingCycleChanged = (val: any) => {
    setSelectedBillingCycle(val);
    updateNewProdConfig(val);
  };

  const updateNewProdConfig = (billingCycle: number) => {
    if (!selectedOffer) return;
    let newProdConfig: ChangeSubscriptionDisplayProduct[] =
      selectedOffer.offerProducts.map((prod: DownsellProduct) => {
        let price = prod.prices.find((x) => {
          return Number(x.totalBillingCycles) === Number(billingCycle);
        });
        // In case where the user has selected a custom billing cycle
        // for which we don't have price information, we default to the
        // default billing cycle of the offer.
        if (!price) {
          price = prod.prices.find((x) => {
            return (
              Number(x.totalBillingCycles) ===
              Number(selectedOffer.defaultBillingCycle)
            );
          });
          // As we don't have the selected billing cycle configured in this case, we just take
          // the total of the default billing cycle and divide it by the selected billing cycle
          // to get pricePerBillingCycle. This is needed because the order totals vary based on
          // the billing cycles.
          return {
            id: price?.chargebeeId,
            image: prod.image,
            name: prod.name,
            billingCycles: billingCycle,
            pricePerBillingCycle: ((price?.amount || 1) / billingCycle) * 100,
            quantity: 1,
            total: price?.amount,
          };
        }
        // If the user selected billing cycle is configured in the offer, we just take the
        // pricing and total information from the plan price.
        return {
          id: price?.chargebeeId,
          image: prod.image,
          name: prod.name,
          billingCycles: billingCycle,
          pricePerBillingCycle: (price?.amountPerBillingCycle || 0) * 100,
          quantity: 1,
          total: price?.amount,
        };
      });
    setNewProdConfig(newProdConfig);
  };

  const updateSubscription = async () => {
    // to force validation of startDate
    newSubscriptionDetailsForm.validateFields();
    let subStartDate = newSubscriptionDetailsForm.getFieldValue(
      "susbcriptionStartDate"
    );
    if (!subStartDate) {
      message.open({
        type: "error",
        content: "Subscription start date is required",
      });
      return;
    }
    if (selectedOffer && selectedOffer.offer.chargebeeId) {
      // first we need to get the plan (chargebee notation) price to get the id
      let chargebeePlanPrice = await getPlanPrice({
        planId: selectedOffer.offer.chargebeeId,
        currency: selectedOffer?.offer?.currency || "EUR",
      });
      // new subscription details
      let updates: DownsellSubscriptionParams = {
        cf_os_offer_id: selectedOffer.offer.chargebeeId?.toString(),
        subscription_id: subscriptionId,
        billing_cycles: selectedBillingCycle,
        start_date: convertDateToUnix(subStartDate),
        subscription_items: newProdConfig.map(
          (prod: ChangeSubscriptionDisplayProduct) => {
            return {
              item_price_id: prod.id || "0",
              quantity: prod.quantity || 1,
              billing_cycles: selectedBillingCycle,
              unit_price: Number(prod.pricePerBillingCycle?.toFixed(0)) || 0,
            };
          }
        ),
        meta_data: {
          created_with_paradox_os: true,
          is_downsell: true,
          previous_subscription_id: subscriptionId,
          offerName: selectedOffer.offer.name,
        },
      };
      // here we are adding the previously fetched chargebee plan price id
      // chargebee doesns't allow us to create a subscription without a plan
      // so we need to include this with the line items without pricing info.
      updates.subscription_items.push({
        item_price_id: chargebeePlanPrice.id,
      });
      let res = await downsellSubscription(updates);
      if (res) {
        message.open({
          type: "success",
          content: "Subscription updated successfully",
        });
        closeModal();
      }
    }
  };

  const handleOfferChange = async (offerId: string) => {
    let offerDetails: OfferDetails = await getOfferDetails(Number(offerId));
    // Setiting billing cycle info for the current offer
    setConfiguredBillingCycles(offerDetails.configuredBillingCycles);
    setSelectedBillingCycle(offerDetails.defaultBillingCycle);
    setSelectedOffer(offerDetails);
    billingCycleForm.setFieldValue(
      "billingCycleRadio",
      offerDetails.defaultBillingCycle
    );
    let newProdConfig: ChangeSubscriptionDisplayProduct[] =
      offerDetails.offerProducts.map((prod: DownsellProduct) => {
        let price = prod.prices.find((x) => {
          return (
            Number(x.totalBillingCycles) ===
            Number(offerDetails.defaultBillingCycle)
          );
        });
        return {
          id: price?.chargebeeId,
          image: prod.image,
          name: prod.name,
          billingCycles: price?.totalBillingCycles || 0,
          pricePerBillingCycle: (price?.amountPerBillingCycle || 0) * 100,
          quantity: 1,
          total: price?.amount,
        };
      });
    setNewProdConfig(newProdConfig);
  };

  const handleRecalculate = () => {
    const userTotal = newSubscriptionDetailsForm.getFieldValue("userNewTotal");
    if (!userTotal) {
      message.open({
        type: "error",
        content: "Please enter a valid total order amount",
      });
      return;
    }
    // We will base our calculations of total order value on the total
    // amount the user has entered
    const totalToCalculate = userTotal;
    // see if the selected billing cycle is configured in the offer
    // else we default to the default billing cycle of the offer
    let billingCycle = selectedOffer?.configuredBillingCycles.includes(
      selectedBillingCycle.toString()
    )
      ? selectedBillingCycle
      : selectedOffer?.defaultBillingCycle;
    // This will be the total of the original products that were configured
    // in the offer. We use this total to calculate the percentage price of
    // each product in the offer.
    let originalTotal = 0;
    // in order to do the calculations we need to get the original products
    // that were configured in the offer.
    let originalProducts = selectedOffer?.offerProducts.map(
      (prod: DownsellProduct) => {
        let price = prod.prices.find((x) => {
          return Number(x.totalBillingCycles) === Number(billingCycle);
        });
        originalTotal = originalTotal + (Number(price?.amount) || 0);
        return {
          id: price?.chargebeeId,
          image: prod.image,
          name: prod.name,
          billingCycles: billingCycle,
          pricePerBillingCycle: price?.amountPerBillingCycle || 0,
          quantity: 1,
          total: price?.amount,
        };
      }
    );
    // We will update the new product configuration based on the original
    // configuration and the total amount the user has entered.
    setNewProdConfig([
      ...(originalProducts?.map((prod) => {
        if (prod?.total) {
          const percentage = (prod.total / originalTotal) * 100;
          prod.billingCycles = selectedBillingCycle;
          prod.pricePerBillingCycle =
            (totalToCalculate / selectedBillingCycle) * percentage;
          prod.total =
            (prod.pricePerBillingCycle / 100) *
            selectedBillingCycle *
            prod.quantity;
        }
        return prod;
      }) || []),
    ]);
  };

  const columns = [
    {
      title: "Product",
      key: "id",
      render: (record: any) => {
        return (
          <Space>
            <Image
              style={{ borderRadius: "8px" }}
              src={record.image}
              width={50}
              height={50}
            />
            <Text>{record.name}</Text>
          </Space>
        );
      },
    },
    {
      title: "Billing cycles",
      key: "id",
      render: (record: any) => {
        return selectedBillingCycle;
      },
    },
    {
      title: "Price per billing cycle",
      dataIndex: "pricePerBillingCycle",
      key: "id",
      render: (text: number, record: any) => {
        return (
          <Input
            style={{ width: "100px" }}
            value={text / 100}
            onChange={(e) => handlePriceChange(e.target.value, record)}
            addonBefore="€"
          />
        );
      },
    },
    {
      title: "Quantity",
      dataIndex: "quantity",
      key: "id",
    },
    {
      title: "Total",
      dataIndex: "total",
      key: "id",
      render: (val: number) => {
        return Math.round(val);
      },
    },
  ];

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      width="100vw"
      onClose={() => {
        closeModal();
      }}
      onSubmit={() => {
        updateSubscription();
      }}
      submitText="Update Subscription"
      title="Downsell a subscription"
      onCancel={() => {
        closeModal();
      }}
    >
      <Row style={{ padding: 20 }}>
        <Col span={13}>
          <div className="paradox-card-white">
            <div>
              <Text style={{ fontSize: 18, fontWeight: 500 }}>New offer</Text>{" "}
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                Selected offer when the subscription was processed
              </Text>
            </div>
            <br />
            <Form form={offerForm}>
              <Form.Item name="offerName">
                <Select
                  className="paradox-disabled-input"
                  placeholder="Offer name"
                  options={offers?.items
                    ?.filter((x) => x.name !== originalOffer?.offerName)
                    .map((offer: Offer) => {
                      return { label: offer.name, value: offer.id };
                    })}
                  onChange={handleOfferChange}
                />
              </Form.Item>
            </Form>
          </div>
          <br />
          <br />

          <div className="paradox-card-white">
            <div>
              <Text style={{ fontSize: 18, fontWeight: 500 }}>
                Billing cycle
              </Text>{" "}
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                On which number of billing cycles do you agree with the
                customer?
              </Text>
            </div>
            <br />
            <Form form={billingCycleForm}>
              <div style={{ marginBottom: "5px" }}>
                <Form.Item name="billingCycleRadio" noStyle>
                  <Radio.Group
                    onChange={(e) => {
                      handleBillingCycleChange(e.target.value);
                    }}
                  >
                    <Space direction="vertical">
                      {configuredBillingCycles.map((cycle) => {
                        return (
                          <Radio key={cycle} value={cycle}>
                            {cycle} Billing Cycles{" "}
                            {cycle === oldBillingCycle?.toString() ? (
                              <Tag
                                color="#CFCDCA"
                                style={{
                                  color: "black",
                                  fontSize: 11,
                                  marginLeft: "10px",
                                }}
                              >
                                Current
                              </Tag>
                            ) : null}
                          </Radio>
                        );
                      })}
                      <Radio value={"0"}>Custom</Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
              </div>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.billingCycleRadio !==
                  currentValues.billingCycleRadio
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue("billingCycleRadio") === "0" ? (
                    <div style={{ marginLeft: "15px", marginTop: "10px" }}>
                      <InputNumber
                        min={1}
                        defaultValue={1}
                        onChange={(e) => {
                          handleCustomBillingCycleChanged(e);
                        }}
                      />
                    </div>
                  ) : null
                }
              </Form.Item>
            </Form>
          </div>
          <br />
          <br />
          <div className="paradox-card-white">
            <div>
              <Text style={{ fontSize: 18, fontWeight: 500 }}>
                New Subscription Details
              </Text>{" "}
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                Select options that best suit the new subscription
              </Text>
            </div>
            <br />
            <Form form={newSubscriptionDetailsForm}>
              <div style={{ marginBottom: "5px" }}>
                <Alert
                  style={{ marginBottom: "10px" }}
                  type="info"
                  showIcon
                  message="If you have already decided with the customer a total order 
                      amount, enter it here and the system will automatically calculate 
                      the price per billing cycle for each product."
                />
                <Alert
                  style={{ marginBottom: "10px" }}
                  type="warning"
                  showIcon
                  message={`Note: This should not include the amount already
                         paid by the customer for the current subscription (${formatCents(details?.amountPaid)}).`}
                />
                <Space direction="horizontal" style={{ marginBottom: "20px" }}>
                  <Form.Item
                    style={{ marginBottom: "0px" }}
                    name="userNewTotal"
                    label="New order total (optional)"
                  >
                    <InputNumber addonBefore="€" />
                  </Form.Item>
                  <Button
                    style={{ marginLeft: "20px" }}
                    type="primary"
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={handleRecalculate}
                  >
                    Recalculate
                  </Button>
                </Space>
              </div>
              <Form.Item
                name="susbcriptionStartDate"
                label="Subscription start date"
                rules={[{ required: true, message: "Start date is required" }]}
              >
                <DatePicker showTime />
              </Form.Item>
            </Form>
          </div>
          <br />
          <br />
          <div className="paradox-card-white">
            <div>
              <Text style={{ fontSize: 18, fontWeight: 500 }}>
                Product configuration
              </Text>{" "}
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text style={{ fontSize: 13, color: "darkgray" }}>
                Based on the current subscription selection
              </Text>
            </div>
            <div>
              <Table
                dataSource={newProdConfig}
                columns={columns}
                pagination={false}
                rowKey={(record, index) => record.id?.toString() || "sad"}
              />
            </div>
            <br />
          </div>
        </Col>
        <Col span={10} offset={1}>
          <div className="paradox-card-white">
            <div>
              <Text strong style={{ fontSize: 14, fontWeight: 700 }}>
                Order summary
              </Text>{" "}
              &nbsp;&nbsp;
              <Tag
                color="#CFCDCA"
                style={{
                  color: "black",
                  fontSize: 11,
                  marginLeft: "10px",
                }}
              >
                Current
              </Tag>
            </div>
            <br />
            <br />
            <Space style={{ marginBottom: "8px" }}>
              <FieldTimeOutlined />
              <Text>Next billing date: </Text>
              <Text type="secondary">
                {formatDateFromUnix(Number(nextBillingDate))}
              </Text>
            </Space>
            <br />
            <Space style={{ marginBottom: "8px" }}>
              <RedoOutlined />
              <Text># of invoices </Text>
              <Text type="secondary">
                {history.invoices.length}/{oldBillingCycle}
              </Text>
              <Progress
                steps={10}
                percent={(history.invoices.length / oldBillingCycle) * 100}
                showInfo={false}
                strokeColor={"green"}
              />
            </Space>
            <br />
            <Space style={{ marginBottom: "8px" }}>
              <MoneyCollectOutlined />
              <Text>Amount remaining to be invoiced</Text>
              <Text type="secondary">{formatCents(details.remaining)}</Text>
            </Space>
            <br />
            <br />
            <Row>
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Text style={{ fontSize: 14, fontWeight: 400 }}>
                  Total Order
                </Text>
                <Text strong>
                  <Text strong={false} type="secondary">
                    EUR
                  </Text>{" "}
                  {formatCents(details.total)}
                </Text>
              </div>
            </Row>
            <Divider />
            {oldProdConfig.map((prod) => {
              return (
                <ProductInfo
                  key={prod.id}
                  image={prod.image}
                  name={prod.name}
                  pricePerBillingCycle={prod.pricePerBillingCycle}
                  billingCycles={prod?.billingCycles || 0}
                />
              );
            })}
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <div style={{ position: "absolute", marginTop: "30px" }}>
                <DownOutlined style={{ fontSize: 50, color: "#95DE64" }} />
              </div>
              <div style={{ position: "absolute", marginTop: "70px" }}>
                <DownOutlined style={{ fontSize: 50, color: "#52C41A" }} />
              </div>
              <div style={{ position: "absolute", marginTop: "110px" }}>
                <DownOutlined style={{ fontSize: 50, color: "#389E0D" }} />
              </div>
            </div>
          </div>
          <br />
          <br />
          <div className="paradox-card-white">
            <div>
              <Text strong style={{ fontSize: 14, fontWeight: 700 }}>
                Order summary
              </Text>{" "}
              &nbsp;&nbsp;
              <Tag
                color="#A6E8CE"
                style={{
                  color: "#115E3E",
                  fontSize: 11,
                  marginLeft: "10px",
                }}
              >
                New
              </Tag>
            </div>
            <br />
            <br />
            <Space style={{ marginBottom: "8px" }}>
              <RedoOutlined />
              <Text># of invoices </Text>
              <Text type="secondary">
                {history.invoices.length}/
                {selectedBillingCycle + history.invoices.length}
              </Text>
              <Progress
                steps={10}
                percent={
                  (history?.invoices?.length /
                    (history?.invoices?.length + selectedBillingCycle)) *
                  100
                }
                showInfo={false}
                strokeColor={"green"}
              />
            </Space>
            <br />
            <br />
            <Row>
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Text style={{ fontSize: 14, fontWeight: 400 }}>
                  Total Order
                </Text>
                <Text strong>
                  <Text strong={false} type="secondary">
                    EUR
                  </Text>{" "}
                  {formatCents(newOderTotal + details.amountPaid)}
                </Text>
              </div>
              <Text style={{ color: "red" }}>
                Includes amount already paid for current subscription{" "}
                {`(${formatCents(details.amountPaid)})`}
              </Text>
              {newOderTotal + details.amountPaid > details.total ? (
                <Alert
                  style={{ width: "100%" }}
                  message="New order total is higher than the current order total"
                  type="warning"
                  showIcon
                />
              ) : null}
              {newOderTotal + details.amountPaid < details.total ? (
                <Alert
                  style={{ width: "100%" }}
                  message="New order total is less than the current order total"
                  type="warning"
                  showIcon
                />
              ) : null}
            </Row>
            <Divider />
            {newProdConfig.map((prod) => {
              return (
                <ProductInfo
                  key={prod.id}
                  image={prod.image}
                  name={prod.name}
                  pricePerBillingCycle={prod.pricePerBillingCycle}
                  billingCycles={prod.billingCycles}
                />
              );
            })}
          </div>
        </Col>
      </Row>
    </DrawerFormContainer>
  );
};

export default Downsell;
