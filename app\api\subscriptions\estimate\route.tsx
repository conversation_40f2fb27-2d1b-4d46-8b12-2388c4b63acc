import { EstimateSubscriptionParams } from "@/types";
import { estimateSubscription, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the POST request to estimate a subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function POST(req: any) {
  try {
    const body = (await req.json()) as EstimateSubscriptionParams;
    const response = await estimateSubscription(body);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_ESTIMATE_SUBSCRIPTION", details: error },
      { status: 400 }
    );
  }
}
