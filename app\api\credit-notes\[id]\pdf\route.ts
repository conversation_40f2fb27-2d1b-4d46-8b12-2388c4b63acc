import { getCreditNotePDF, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

initChargebee();

/**
 * Handles the POST request for generating a download link for invoice PDF.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved invoice or an error message.
 */
export async function POST(req: Request) {
  try {
    let body = await req.json();
    const { id } = body;
    if (id) {
      const response = await getCreditNotePDF(id);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json({ error: "CANNOT_RETRIEVE_PDF" }, { status: 400 });
  }
}
