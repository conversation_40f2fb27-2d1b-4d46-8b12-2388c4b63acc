"use client";

import { Col, Row, Space, Tag, Typography, Divider, Table } from "antd";
import { useRouter } from "next/navigation";
import React from "react";

import { getCreditNoteColumns } from "./CreditNoteColumns";
import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { CreditNote } from "@/types";

type CreditNoteInfoProps = {
  creditNote: CreditNote | undefined;
  creditNoteInfo: any;
};

const { Text } = Typography;
export default function CreditNoteInfo({
  creditNote,
  creditNoteInfo,
}: CreditNoteInfoProps) {
  const router = useRouter();

  const columns = React.useMemo(
    () => getCreditNoteColumns(creditNote),
    [creditNote]
  );

  return (
    <div style={{ padding: "20px", border: "1px solid #F5F5F5" }}>
      <Row>
        <Col span={20}>
          <Text style={{ fontSize: 17 }}>
            Credit note for{" "}
            <Text strong style={{ fontSize: 17 }}>
              {creditNote?.total &&
                (creditNote?.total / 100).toLocaleString("en-EU", {
                  style: "currency",
                  currency: creditNote?.currencyCode,
                })}
            </Text>{" "}
            {creditNote?.currencyCode}
          </Text>
          <br />
          <Space style={{ marginTop: "5px" }}>
            <Tag style={{ color: "gray", fontSize: 14 }} color="#F5F5F5">
              Credit note {creditNote?.chargebeeId}
            </Tag>
            <Tag
              onClick={() => {}}
              style={{
                color: "gray",
                cursor: "pointer",
                fontSize: 14,
              }}
              color="#F5F5F5"
            >
              Invoice{" "}
              <span
                onClick={() => {
                  router.push(
                    `/dashboard/invoices/${creditNote?.referenceInvoiceId}`
                  );
                }}
                style={{ textDecoration: "underline" }}
              >
                {creditNote?.referenceInvoiceId}
              </span>
            </Tag>
            <Tag style={{ color: "gray", fontSize: 14 }} color="#F5F5F5">
              Business entity {creditNote?.businessEntityId}
            </Tag>
          </Space>
        </Col>
        <Col span={4}>
          <Tag
            color={creditNote?.status === "refunded" ? "red" : "orange"}
            style={{
              float: "right",
            }}
          >
            {capitalizeWord(creditNote?.status)}
          </Tag>
        </Col>
      </Row>
      <br />
      <br />
      <br />
      <div style={{ display: "flex", flexDirection: "column" }}>
        <div>
          <Text style={{ color: "gray" }}>Date </Text>&nbsp;&nbsp;
          <Text>
            {creditNote?.date && formatDateFromUnix(creditNote?.date)}
          </Text>
        </div>
        <div>
          <Text style={{ color: "gray" }}>Amount available </Text>&nbsp;&nbsp;
          <Text>{formatCents(creditNote?.amountAvailable as number)}</Text>{" "}
          <Text>{creditNote?.currencyCode}</Text>
        </div>
        <div>
          <Text style={{ color: "gray" }}>Reason </Text>&nbsp;&nbsp;
          <Text>
            <Text>
              {creditNote?.createReason &&
                capitalizeWord(creditNote?.createReason.split("_").join(" "))}
            </Text>
          </Text>
        </div>
        <div>
          <Text style={{ color: "gray" }}>Channel </Text>&nbsp;&nbsp;
          <Text>
            <Text>
              {creditNote?.channel && capitalizeWord(creditNote?.channel)}
            </Text>
          </Text>
        </div>
      </div>
      <Divider />
      <br />
      <div>
        <Table
          dataSource={creditNote?.lineItems}
          rowKey={"id"}
          columns={columns}
          pagination={false}
        />
      </div>
      <div style={{ width: "100%", height: "120px" }}>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            float: "right",
            textAlign: "right",
            marginRight: "40px",
          }}
        >
          <Space direction="vertical">
            <div style={{ marginTop: "20px" }}>
              <Text style={{ color: "gray" }}>Total Excl. Vat </Text>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text>
                {creditNoteInfo?.totalExclVatAllLineItems && (
                  <Text>
                    {formatCents(creditNoteInfo?.totalExclVatAllLineItems)}
                  </Text>
                )}
              </Text>
            </div>
            <div>
              <Text style={{ color: "gray" }}>VAT </Text>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text>
                <Text>
                  {creditNoteInfo?.allVat && (
                    <Text>{formatCents(creditNoteInfo?.allVat)}</Text>
                  )}
                </Text>
              </Text>
            </div>
            <div>
              <Text style={{ color: "gray" }}>Total Incl. Vat </Text>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text>
                <Text>
                  {creditNoteInfo?.totalInclVat &&
                    formatCents(creditNoteInfo?.totalInclVat)}
                </Text>
              </Text>
            </div>
            <div>
              <Text style={{ color: "gray" }}>
                {creditNote?.status === "refunded" ? "Refund" : "Allocations"}{" "}
              </Text>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Text>
                <Text>
                  {creditNoteInfo?.amountToBeDisplay && (
                    <Text>
                      {formatCents(creditNoteInfo?.amountToBeDisplay)}
                    </Text>
                  )}
                </Text>
              </Text>
            </div>
            <div>
              <Text style={{ color: "gray" }}>Amount Available (EUR) </Text>
              &nbsp;&nbsp;
              <Text>
                <Text strong style={{ fontSize: 18 }}>
                  {formatCents(creditNoteInfo?.amount_available)}
                </Text>
              </Text>
            </div>
          </Space>
        </div>
      </div>
    </div>
  );
}
