"use client";

import { But<PERSON>, Form, Row, Select, Space } from "antd";
import {
  FolderOutlined,
  StepForwardOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { useState } from "react";

import InvoiceDisplay from "./InvoiceDisplay";
import InvoiceDropDownItem from "./InvoiceDropDownItem";
import { formatCents } from "@/lib/currency";
import { Invoice } from "@/types";

type SearchInvoiceProps = {
  filteredInvoices: Invoice[];
  handleSearch: Function;
  attachPaymentToInvoice: Function;
  resetFilteredList: Function;
  handleSkip: Function;
  markPaymentAsIgnored: Function;
};
const SearchInvoice = ({
  filteredInvoices,
  handleSearch,
  attachPaymentToInvoice,
  resetFilteredList,
  handleSkip,
  markPaymentAsIgnored,
}: SearchInvoiceProps) => {
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | undefined>();

  const handleInvoiceSelection = async (invoiceId: string) => {
    setSelectedInvoice(
      filteredInvoices.find((invoice) => invoice.id?.toString() === invoiceId)
    );
    resetFilteredList();
  };

  const [searchForm] = Form.useForm();

  return (
    <div>
      <Row>
        <Form form={searchForm} style={{ width: "100vw" }}>
          <Form.Item name="invoiceSearchSelect">
            <Select
              style={{ width: "100%" }}
              allowClear
              showSearch
              filterOption={false}
              onClear={() => {
                setSelectedInvoice(undefined);
              }}
              onSelect={(value) => {
                handleInvoiceSelection(value);
              }}
              onSearch={(value) => {
                handleSearch(value);
              }}
              placeholder="Search for an invoice"
              options={
                filteredInvoices.map((invoice) => {
                  return {
                    label:
                      "Invoice for " + formatCents(invoice.total as number),
                    value: invoice.id?.toString(),
                  };
                }) || []
              }
              optionRender={(item) => {
                return (
                  <InvoiceDropDownItem
                    invoice={filteredInvoices.find((invoice) => {
                      return invoice.id?.toString() === item.value;
                    })}
                  />
                );
              }}
            />
          </Form.Item>
        </Form>
      </Row>
      <div style={{ overflowY: "scroll", height: "500px" }}>
        {selectedInvoice !== undefined && selectedInvoice.chargebeeId ? (
          <InvoiceDisplay invoiceId={selectedInvoice?.chargebeeId} />
        ) : (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              minHeight: "300px",
            }}
          >
            <Button type="primary" icon={<SyncOutlined />}>
              Search for an invoice
            </Button>
          </div>
        )}
      </div>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
        }}
      >
        <Space style={{ marginTop: "20px" }}>
          <Button
            onClick={() => {
              markPaymentAsIgnored();
            }}
            icon={<FolderOutlined />}
          >
            Mark as ignored
          </Button>
          <Button
            onClick={() => {
              setSelectedInvoice(undefined);
              searchForm.resetFields();
              handleSkip();
            }}
            icon={<StepForwardOutlined />}
          >
            Skip
          </Button>
          <Button
            icon={<SyncOutlined />}
            type="primary"
            disabled={selectedInvoice === undefined}
            onClick={() => {
              if (!selectedInvoice?.chargebeeId) {
                return;
              }
              attachPaymentToInvoice(selectedInvoice.chargebeeId);
              setSelectedInvoice(undefined);
              searchForm.resetFields();
            }}
          >
            Attach bank transfer
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default SearchInvoice;
