"use client";

import { <PERSON><PERSON>, But<PERSON>, Divider, List, Typography } from "antd";

import { TabState } from "./DebugAccesses";

const { Text } = Typography;

type DebugAccessesLWProps = {
  state: TabState;
  handleGetCoursesOfUser: () => void;
};

export default function DebugAccessesLW({
  state,
  handleGetCoursesOfUser,
}: DebugAccessesLWProps) {
  return (
    <>
      <Button
        type="primary"
        onClick={handleGetCoursesOfUser}
        loading={state.lwLoading}
      >
        Verify Access
      </Button>
      <Divider />
      {state.lwDetails.length > 0 ? (
        <List
          dataSource={state.lwDetails}
          renderItem={(item: any) => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.course.courseImage} />}
                title={item.course.title}
                description={
                  <Text color="blue" copyable={{ text: item.course.id }}>
                    ID: {item.course.id}
                  </Text>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <Text>No records found</Text>
      )}
    </>
  );
}
