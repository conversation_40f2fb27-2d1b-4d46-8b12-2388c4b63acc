"use client";

import { Subscription } from "chargebee";
import { Descriptions, Typography } from "antd";

import { useFetchSubscriptionComments } from "@/hooks/offering/subscriptions";
import { formatDateFromUnix } from "@/lib/date";

const { Text } = Typography;

type SubscriptionCommentsProps = {
  subscription: Subscription;
};

export default function SubscriptionComments({
  subscription,
}: SubscriptionCommentsProps) {
  const { data: comments } = useFetchSubscriptionComments(subscription.id);

  return (
    <Descriptions
      title="Comments"
      style={{ width: "100%" }}
      bordered
      column={1}
      size="small"
    >
      {comments && comments?.list && comments?.list.length ? (
        comments.list.map((item, index: number) => (
          <Descriptions.Item key={index} label={item.comment.notes}>
            <Text type="secondary">
              performed by <Text>{item.comment.added_by || "?"}</Text>
            </Text>
            <Text type="secondary">
              &nbsp; on&nbsp;
              {formatDateFromUnix(item.comment.created_at)}
            </Text>
          </Descriptions.Item>
        ))
      ) : (
        <Text>No comments</Text>
      )}
    </Descriptions>
  );
}
