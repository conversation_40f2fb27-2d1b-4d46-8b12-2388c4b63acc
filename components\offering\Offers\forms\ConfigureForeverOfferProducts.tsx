"use client";

import { <PERSON>ton, Col, Input, Row, Select, Space, Typography } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { FilterForeverOfferProps } from "./ConfigureForeverOfferProductsForm";
import SelectedForeverProductItem from "../SelectedForeverProductItem";
import { EmptyPlanItem } from "../type";

const { Text } = Typography;

type Props = {
  filters: FilterForeverOfferProps;
  setFilters: (value: any) => void;
  setShowAddProductModal: (value: boolean) => void;
};

const ConfigureForeverOfferProducts = ({
  filters,
  setFilters,
  setShowAddProductModal,
}: Props) => {
  const handleDisplayChange = (value: any) => {
    setFilters((prev: FilterForeverOfferProps) => ({
      ...prev,
      display: value,
    }));
  };

  const handlePlanChanged = (billingCycle: string, changedPlan: string) => {
    if (billingCycle === "monthly-forever") {
      if (changedPlan === "0") {
        setFilters((prev: FilterForeverOfferProps) => ({
          ...prev,
          selectedMonthlyPlan: EmptyPlanItem,
        }));
      } else {
        setFilters((prev: FilterForeverOfferProps) => ({
          ...prev,
          selectedMonthlyPlan: filters.selectedProduct?.plans.find(
            (x) => x.id && x?.id.toString() === changedPlan
          ),
        }));
      }
    } else if (billingCycle === "yearly-forever") {
      if (changedPlan === "0") {
        setFilters((prev: FilterForeverOfferProps) => ({
          ...prev,
          selectedYearlyPlan: EmptyPlanItem,
        }));
      } else {
        setFilters((prev: FilterForeverOfferProps) => ({
          ...prev,
          selectedYearlyPlan: filters.selectedProduct?.plans.find(
            (x) => x.id && x?.id.toString() === changedPlan
          ),
        }));
      }
    }
  };
  return (
    <div
      style={{
        width: "100vw",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <div className="paradox-card" style={{ width: "80%", padding: "10px" }}>
        <Text strong>Configure the products available in your offer</Text>
        <br />
        <br />
        <Row style={{ width: "100%" }}>
          <Col span={5}>
            <div className="paradox-dotted-card" style={{ height: "100%" }}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-start",
                  flexDirection: "column",
                  width: "100%",
                }}
              >
                <Text style={{ paddingTop: "20px" }}>Periodicity</Text>
                <br />
                <div>
                  <div style={{ width: "100%", marginTop: "100px" }}>
                    <Input
                      value={"Monthly"}
                      disabled
                      style={{ width: "80%" }}
                      className="paradox-disabled-input"
                    />
                  </div>
                  <br />
                  <div
                    style={{
                      width: "100%",
                      marginBottom: "20px",
                      marginTop: "40px",
                    }}
                  >
                    <Input
                      value={"Yearly"}
                      disabled
                      style={{ width: "80%" }}
                      className="paradox-disabled-input"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Col>
          <Col span={14} offset={1}>
            <div className="paradox-dotted-card" style={{ height: "100%" }}>
              <div
                style={{
                  width: "100%",
                  paddingTop: "20px",
                }}
              >
                <Text>Products</Text>
                <Button
                  size="small"
                  icon={<PlusOutlined />}
                  style={{ float: "right" }}
                  onClick={() => setShowAddProductModal(true)}
                >
                  {filters.selectedProduct ? "Edit" : "Add"}
                </Button>
                <br />
                <br />
                <Space
                  style={{
                    overflowX: "scroll",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "start",
                  }}
                >
                  {!filters.selectedProduct && (
                    <div
                      style={{
                        width: "100%",
                        backgroundColor: "#FAFAFA",
                        marginTop: "30px",
                        padding: "10px",
                        borderRadius: "5px",
                        border: "1px solid lightgray",
                      }}
                    >
                      <span style={{ width: "100%" }}>
                        <Text>No product selected</Text>
                      </span>
                    </div>
                  )}
                  {filters.selectedProduct ? (
                    <SelectedForeverProductItem
                      product={filters.selectedProduct}
                      selectedMonthlyPlan={filters.selectedMonthlyPlan}
                      selectedYearlyPlan={filters.selectedYearlyPlan}
                      key={filters.selectedProduct.id}
                      handlePlanChanged={handlePlanChanged}
                    />
                  ) : null}
                </Space>
              </div>
              <hr />
            </div>
          </Col>
        </Row>
      </div>
      <div
        style={{
          width: "82%",
          marginTop: "20px",
        }}
      >
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "start",
          }}
        >
          <div style={{ width: "100%" }} className="paradox-card">
            <Text strong>
              How do you want to display this offer in your checkout page?
            </Text>{" "}
            <br />
            <Text style={{ fontSize: 13, color: "gray" }}>
              Based on your selection, it will change the behavior of the
              checkout page
            </Text>
            <br />
            <br />
            <Text>Product display</Text> <br />
            <Select
              style={{ width: "100%" }}
              value={filters.display}
              onChange={handleDisplayChange}
              options={[
                { label: "Display offer", value: "offer" },
                { label: "Display products", value: "products" },
              ]}
              optionRender={(option) => (
                <div>
                  <Text>
                    {option.value === "offer"
                      ? "Display offer"
                      : "Display products"}
                  </Text>{" "}
                  <br />
                  <Text
                    style={{
                      fontSize: 13,
                      color: "gray",
                    }}
                  >
                    {option.value === "offer"
                      ? `Instead of displaying all the products in 
                            the checkout page, only the offer will be display`
                      : `All the products attached to this offer
                             will be display in the checkout page`}
                  </Text>
                </div>
              )}
            />
            <br />
            <Text style={{ fontSize: 13, color: "gray" }}>
              Based on this configuration, we will display the offer or all the
              products.
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfigureForeverOfferProducts;
