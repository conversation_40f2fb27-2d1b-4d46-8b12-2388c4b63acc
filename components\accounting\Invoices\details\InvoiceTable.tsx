"use client";

import { Col, Row, Table, Typography } from "antd";
import { Invoice as ChargebeeInvoice } from "chargebee";

import { Invoice } from "@/types";
import { ColumnType } from "antd/es/table";
import { formatCents } from "@/lib/currency";

type InvoiceLineItem = ChargebeeInvoice.LineItem;

type InvoiceTableProps = {
  invoice: Invoice;
  invoiceInfo: any;
  columns: ColumnType<InvoiceLineItem>[];
};

const { Text } = Typography;

export const InvoiceTable = ({
  invoice,
  invoiceInfo,
  columns,
}: InvoiceTableProps) => {
  return (
    <>
      <div>
        {invoice.lineItems && (
          <Table
            dataSource={invoice?.lineItems}
            columns={columns}
            pagination={false}
            rowKey={(record: InvoiceLineItem) => (record.id ? record.id : "")}
            size="small"
          />
        )}
      </div>
      <div
        style={{
          width: "100%",
          height: "auto",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            float: "right",
            textAlign: "right",
            width: "200px",
          }}
        >
          <Row style={{ marginTop: "20px" }}>
            <Col span={12}>
              <Text style={{ color: "gray" }}>Total Excl. Vat </Text>
            </Col>
            <Col span={12}>
              <Text>
                {invoiceInfo?.totalExclVatAllLineItems && (
                  <Text>
                    {formatCents(invoiceInfo?.totalExclVatAllLineItems)}
                  </Text>
                )}
              </Text>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Text style={{ color: "gray" }}>VAT </Text>
            </Col>
            <Col span={12}>
              <Text>
                {invoiceInfo?.allVat && (
                  <Text>{formatCents(invoiceInfo?.allVat)}</Text>
                )}
              </Text>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Text style={{ color: "gray" }}>Total Incl. Vat </Text>
            </Col>
            <Col span={12}>
              <Text>
                <Text>
                  {invoiceInfo?.totalInclVat &&
                    formatCents(invoiceInfo?.totalInclVat)}
                </Text>
              </Text>
            </Col>
          </Row>
          {invoice.amountAdjusted !== undefined &&
            invoice.amountAdjusted > 0 && (
              <Row>
                <Col span={12}>
                  <Text style={{ color: "gray" }}>Adjusted </Text>
                </Col>
                <Col span={12}>
                  <Text>
                    <Text>
                      {invoice.amountAdjusted &&
                        formatCents(-invoice.amountAdjusted)}
                    </Text>
                  </Text>
                </Col>
              </Row>
            )}
          <Row>
            <Col span={12}>
              <Text style={{ color: "gray" }}>Amount paid</Text>
            </Col>
            <Col span={12}>
              <Text>
                <Text>
                  {invoice?.amountPaid && formatCents(-invoice?.amountPaid)}
                </Text>
              </Text>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Text style={{ color: "gray" }}>Amount Due</Text>
            </Col>
            <Col span={12}>
              <Text>
                <Text>
                  <Text strong style={{ fontSize: 18 }}>
                    {formatCents(invoice?.amountDue as number)}
                  </Text>
                </Text>
              </Text>
            </Col>
          </Row>
        </div>
      </div>
    </>
  );
};
