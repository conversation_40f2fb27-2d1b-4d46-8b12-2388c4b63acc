"use client"; // Error components must be Client Components

import { Button } from "antd";
import { useRouter } from "next/navigation";
import { Suspense, useEffect } from "react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();

  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <Suspense>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 10,
          marginTop: 100,
          marginLeft: "auto",
          marginRight: "auto",
          width: "50%",
        }}
      >
        <h2>Something went wrong!</h2>
        <Button
          onClick={
            // Attempt to recover by trying to re-render the segment
            () => reset()
          }
        >
          Try again
        </Button>
        <Button onClick={() => router.push("/")}>Go to Home</Button>
      </div>
    </Suspense>
  );
}
