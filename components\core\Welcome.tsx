"use client";

import { SignOutButton } from "@clerk/nextjs";
import { <PERSON><PERSON>, <PERSON>, Col, Divider, Row, Statistic, Typography } from "antd";
import Link from "next/link";

import { useStats } from "@/hooks/stats";
import { EuroCircleOutlined } from "@ant-design/icons";

type WelcomeProps = {
  firstName: string;
};

const { Title } = Typography;

export default function Welcome({ firstName }: WelcomeProps) {
  const { data: stats } = useStats();

  console.log("ℹ️", { stats });

  return (
    <div>
      <h1>Welcome {firstName},</h1>
      <Row>
        <Col span={6}>
          <Title level={4}>Sales by status</Title>
          {stats?.subscriptionsCount.map((subscription) => (
            <Card variant="outlined" key={subscription.orderStatus}>
              <Statistic
                title={`${subscription.orderStatus} (${subscription.subscriptions_count})`}
                value={subscription.total_transaction_amount}
                suffix={<EuroCircleOutlined />}
                precision={2}
              />
              <Link
                href={`/dashboard/sales?status=${subscription.orderStatus}`}
              >
                View all
              </Link>
            </Card>
          ))}
        </Col>
      </Row>
      <Divider />
      <SignOutButton>
        <Button type="primary">Sign out</Button>
      </SignOutButton>
    </div>
  );
}
