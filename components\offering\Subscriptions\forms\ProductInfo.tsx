"use client";

import { Image, Space, Typography } from "antd";

import { formatCents } from "@/lib/currency";
import { ChangeSubscriptionDisplayProduct } from "@/types";

const { Text } = Typography;

export default function ProductInfo(params: ChangeSubscriptionDisplayProduct) {
  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Space>
        {params.image ? (
          <Image
            src={params.image}
            width={50}
            height={50}
            style={{ borderRadius: "6px" }}
          />
        ) : null}
        <Text style={{ fontSize: 14 }}>{params.name}</Text>
      </Space>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <Text style={{ fontSize: 16, color: "#096DD9" }}>
          {params?.pricePerBillingCycle &&
            formatCents(params.pricePerBillingCycle)}{" "}
          <span style={{ color: "black" }}>per month</span>
        </Text>
        <Text type="secondary">for the next {params.billingCycles} months</Text>
      </div>
    </div>
  );
}
