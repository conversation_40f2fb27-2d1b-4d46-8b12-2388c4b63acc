"use client";

import { PlanDTO, Product } from "@/types";
import { Modal, Select, Typography } from "antd";
import { ProductDropDownSelect } from "./ProductDropdownSelect";
import { FilterOfferProps } from "../forms/ConfigureOfferProductsForm";

const { Text } = Typography;

type AddRecommendedProductModalProps = {
  products: Product[];
  filters: FilterOfferProps;
  isOpen: boolean;
  setFilters: (p: any) => void;
  setIsOpen: (s: boolean) => void;
};
export const AddRecommendedProductModal = ({
  filters,
  isOpen,
  products,
  setFilters,
  setIsOpen,
}: AddRecommendedProductModalProps) => {
  const handleRecommendedSelection = (value: number | undefined) => {
    if (value) {
      let prod = products.find((x: Product) => x.id === value);
      if (prod) {
        let map: Map<string, string> = new Map();
        filters.selectedBC.forEach((element) => {
          if (prod) {
            let currentPlan = prod.plans.find(
              (x: PlanDTO) =>
                element === x.prices[0].totalBillingCycles.toString()
            );
            currentPlan?.id && map.set(element, currentPlan?.id?.toString());
          }
        });
        setFilters((prev: FilterOfferProps) => ({
          ...prev,
          selectedRecommendedPlans: map,
          selectedRecommended: prod,
        }));

        setIsOpen(false);
      }
    } else {
      setFilters((prev: FilterOfferProps) => ({
        ...prev,
        selectedRecommended: undefined,
      }));
    }
  };

  return (
    <Modal
      onCancel={() => setIsOpen(false)}
      open={isOpen}
      width="50%"
      closable={false}
      footer={[]}
    >
      <br />
      {!filters.filteredProducts && (
        <Text>Please select a billing cycle first</Text>
      )}
      {filters.filteredProducts && products && (
        <Select
          style={{ width: "100%" }}
          showSearch
          onSearch={(value) => {}}
          allowClear
          value={filters.selectedRecommended?.id}
          placeholder="Search recommended product ..."
          onChange={handleRecommendedSelection}
          onClear={() => {
            handleRecommendedSelection(undefined);
          }}
          options={products.map((item: Product) => {
            return {
              label: item.externalName,
              value: item.id,
              disabled:
                filters.selectedProducts?.includes(item) ||
                !filters.filteredProducts.includes(item),
            };
          })}
          optionRender={(option) => {
            let prod = products.find((x: Product) => x.id === option.key);
            let isAlreadySelected =
              prod && filters.selectedProducts?.includes(prod);
            return (
              <ProductDropDownSelect
                selectedBillingCycles={filters.selectedBC}
                product={prod}
                showDisabledTooltip={option.data.disabled}
                alreadySelected={isAlreadySelected}
              />
            );
          }}
        />
      )}
    </Modal>
  );
};
