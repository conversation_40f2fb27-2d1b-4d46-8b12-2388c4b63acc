"use client";

import { Descriptions, Row, Tag, Typography } from "antd";
import { Invoice as ChargebeeInvoice, Transaction } from "chargebee";
import { useRouter } from "next/navigation";

import { formatCents } from "@/lib/currency";
import { formatDateFromUnix } from "@/lib/date";
import { capitalizeWord } from "@/lib/text";
import { Invoice } from "@/types";

const { Text } = Typography;

type InvoiceLinkedPayment = ChargebeeInvoice.LinkedPayment;

type InvoicePaymentProps = {
  invoice: Invoice;
  payments: Transaction[] | undefined;
};

const InvoicePayments = ({ invoice, payments }: InvoicePaymentProps) => {
  const router = useRouter();
  const lp = invoice.linkedPayments;
  return (
    <Descriptions title="Payments" column={1} bordered size="small">
      {lp &&
        lp.length > 0 &&
        lp.map((record: InvoiceLinkedPayment) => {
          let currentTransaction: Transaction | undefined =
            payments && payments.find((p: any) => p.id === record.txn_id);
          return (
            <Descriptions.Item
              key={record.txn_id}
              label={
                <div style={{ display: "flex", alignItems: "center" }}>
                  <a
                    onClick={() => {
                      router.push(`/dashboard/transactions/${record.txn_id}`);
                    }}
                  >
                    {record.txn_id}
                  </a>
                  &nbsp;
                  <Tag
                    color={record.txn_status === "success" ? "green" : "red"}
                  >
                    {capitalizeWord(record.txn_status)}
                  </Tag>
                </div>
              }
            >
              <Row key={record.txn_id} style={{ marginBottom: "10px" }}>
                <Text>
                  <Text strong>
                    {record.applied_amount &&
                      formatCents(record.applied_amount)}
                  </Text>
                  {` paid by `}
                  <b>{capitalizeWord(currentTransaction?.payment_method)}</b>
                  {` on ${formatDateFromUnix(record.txn_date)}`}
                </Text>
              </Row>
            </Descriptions.Item>
          );
        })}
      {lp?.length === 0 && (
        <Descriptions.Item>
          <Text style={{ color: "gray" }}>
            No payment attached to this invoice
          </Text>
        </Descriptions.Item>
      )}
    </Descriptions>
  );
};

export default InvoicePayments;
