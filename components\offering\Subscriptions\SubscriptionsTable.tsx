"use client";

import { Form, Table, Typography } from "antd";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

import ManualSubscriptionWorkflow from "./forms/ManualSubscriptionWorkflow";
import ExportSubscriptionModal from "./modals/ExportSubscriptionModal";
import { getSubscriptionsTableColumns } from "./SubscriptionsTableColumns";
import SubscriptionsFilterTool from "./SubscriptionsFilterTool";
import { useSubscriptionsTable } from "@/hooks/offering/subscriptions";
import { useProtectPage } from "@/hooks/roles";
import { createSubscription } from "@/lib/services/chargebee";
import { CreateSubscriptionParams } from "@/types";
import ImportSubscriptionDataModal from "./modals/ImportSubscriptionDataModal";

const { Title } = Typography;

const defaultPageSize = 25;

type SubscriptionTableProps = {
  displayForever: boolean;
};

const SubscriptionsTable = ({ displayForever }: SubscriptionTableProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const [filterForm] = Form.useForm();
  useProtectPage("subscriptions-list");

  const {
    pageState,
    isLoading,
    products,
    offers,
    subscriptions,
    clearFilters,
    refetchSubscriptions,
    setPageState,
  } = useSubscriptionsTable({ displayForever, filterForm });

  const columns = getSubscriptionsTableColumns({
    onCustomerClick: (customerId: string) => {
      router.push(pathname + "?customer=" + customerId);
    },
  });

  const handleRowClick = (e: any, data: any) => {
    if (e.metaKey === true) {
      window.open(`/dashboard/subscriptions/${data.chargebeeId}`, "_blank");
      return;
    }
    router.push(`/dashboard/subscriptions/${data.chargebeeId}`);
  };

  const handleSubscriptionAdd = async (values: CreateSubscriptionParams) => {
    const res = await createSubscription(values);
    if (res.subscription) {
      window.location.href = `/dashboard/subscriptions/${res.subscription.id}`;
    } else {
      console.error("Error creating subscription: ", res);
    }
    return res;
  };

  const handleFilterChange = async (updatedValue: any, allValues: any) => {
    if (updatedValue?.searchTerm || updatedValue?.searchTerm === "") {
      setPageState({
        ...pageState,
        search: updatedValue.searchTerm,
      });
    } else {
      let formVals = filterForm.getFieldsValue();
      setPageState({
        ...pageState,
        filters: {
          ...formVals,
          isForever: displayForever,
        },
      });
    }
  };

  useEffect(() => {
    refetchSubscriptions();
  }, [
    pageState.filters.product,
    pageState.filters.offer,
    pageState.filters.orderStatus,
    pageState.filters.name,
  ]);

  return (
    <div style={{ maxWidth: "100%", overflowX: "hidden" }}>
      <Title level={3}>{displayForever ? "Subscriptions" : "Sales"}</Title>
      <SubscriptionsFilterTool
        filterForm={filterForm}
        pageState={pageState}
        products={products}
        offers={offers}
        handleFilterChange={handleFilterChange}
        setPageState={setPageState}
        onClearFilters={() => {
          clearFilters();
        }}
      />
      <Table
        dataSource={subscriptions?.items}
        columns={columns}
        rowKey={(record) => record.id}
        scroll={{ x: 2000 }}
        size="middle"
        loading={isLoading}
        pagination={{
          current: pageState.page,
          defaultPageSize: defaultPageSize,
          total: subscriptions?.total,
          showSizeChanger: false,
          onChange: async (page) => {
            setPageState({
              ...pageState,
              page: page,
            });
          },
        }}
        rowClassName={() => {
          return "paradox-table-row";
        }}
        onRow={(data) => {
          return {
            onClick: (e) => {
              e.preventDefault();
              handleRowClick(e, data);
            },
          };
        }}
      />
      {pageState.manualAddModal && (
        <ManualSubscriptionWorkflow
          isModalOpen={pageState.manualAddModal}
          handleCancel={() =>
            setPageState({
              ...pageState,
              manualAddModal: false,
            })
          }
          handleAdd={handleSubscriptionAdd}
        />
      )}
      {pageState.exportModal && (
        <ExportSubscriptionModal
          isOpen={pageState.exportModal}
          handleClose={(value: boolean) => {
            setPageState({
              ...pageState,
              exportModal: value,
            });
          }}
        />
      )}
      {pageState.importJsonModal && (
        <ImportSubscriptionDataModal
          isOpen={pageState.importJsonModal}
          handleClose={(value: boolean) => {
            setPageState({
              ...pageState,
              importJsonModal: value,
            });
          }}
        />
      )}
    </div>
  );
};

export default SubscriptionsTable;
