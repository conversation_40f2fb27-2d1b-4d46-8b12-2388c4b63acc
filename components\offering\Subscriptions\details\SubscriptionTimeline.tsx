"use client";

import { CalendarOutlined } from "@ant-design/icons";
import { Space, Timeline, Typography } from "antd";
import { formatDateFromUnix } from "@/lib/date";
import { useMemo } from "react";

import { PX_Subscription, SubscriptionHistoricalData } from "@/types";

type SubscriptionTimelineProps = {
  subscriptionPX: PX_Subscription;
  historicalDataPX: SubscriptionHistoricalData;
};

type TimelineItem = {
  label: string;
  children: string;
  color?: string;
};

const { Title } = Typography;

export default function SubscriptionTimeline({
  subscriptionPX,
  historicalDataPX,
}: SubscriptionTimelineProps) {
  const createInvoiceItems = (historicalDataPX: SubscriptionHistoricalData) => {
    if (!historicalDataPX.invoices?.length) {
      return [];
    }
    let items: TimelineItem[] = [];
    // Create new item for each invoice paid
    historicalDataPX.invoices.forEach((invoice, index) => {
      items.push({
        label: `Invoice #${index + 1} -> ${invoice.status}`,
        children: formatDateFromUnix(invoice.date),
        color:
          invoice.status === "paid"
            ? "green"
            : invoice.status === "pending"
              ? "blue"
              : "red",
      });
    });
    return items;
  };
  const createCreditNoteItems = (
    historicalDataPX: SubscriptionHistoricalData
  ) => {
    let items: TimelineItem[] = [];
    if (historicalDataPX.creditNotes?.length > 0) {
      // Create new item for each credit note issued
      historicalDataPX.creditNotes.forEach((creditNote, index) => {
        items.push({
          label: `Credit note #${index + 1} issued`,
          children: formatDateFromUnix(creditNote.date),
        });
      });
    }
    return items;
  };
  const buildItems = (data: PX_Subscription) => {
    const nextBillingAt = data?.nextBillingAt
      ? formatDateFromUnix(data?.nextBillingAt)
      : null;
    const cancelScheduleCreatedAt = data?.cancelScheduleCreatedAt
      ? formatDateFromUnix(data?.cancelScheduleCreatedAt)
      : null;
    let items: TimelineItem[] = [];

    if (data?.createdAt === data?.activatedAt) {
      items.push({
        label: "Created and activated",
        children: formatDateFromUnix(data?.createdAt),
        color: "green",
      });
    } else {
      if (data?.createdAt) {
        items.push({
          label: "Created",
          children: formatDateFromUnix(data?.createdAt),
          color: "green",
        });
      }
      if (data?.activatedAt) {
        items.push({
          label: "Started",
          children: formatDateFromUnix(data?.activatedAt),
          color: "green",
        });
      }
    }
    // Add invoice and credit note items
    items.push(...createInvoiceItems(historicalDataPX));
    items.push(...createCreditNoteItems(historicalDataPX));
    // Add next billing and cancel scheduled items
    if (nextBillingAt) {
      items.push({
        label: "Next billing",
        children: nextBillingAt,
        color: "blue",
      });
    }
    if (cancelScheduleCreatedAt) {
      items.push({
        label: "Cancel scheduled",
        children: cancelScheduleCreatedAt,
        color: "red",
      });
    }
    return items;
  };

  const items = useMemo(() => buildItems(subscriptionPX), [subscriptionPX]);

  return (
    <Space direction="vertical">
      <Title level={5}>
        <CalendarOutlined /> Timeline
      </Title>
      <Timeline items={items} mode="left" />
    </Space>
  );
}
