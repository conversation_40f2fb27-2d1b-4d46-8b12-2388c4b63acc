"use client";

import useS<PERSON>, { SWRConfiguration } from "swr";

import {
  getAllProducts,
  getUniqueBillingCycles,
  getProduct,
} from "@/lib/services";
import {
  GetAllProductsParams,
  Product,
  ProductsFilter,
  ProductsListResponse,
} from "@/types";
import { getCommunityInfo } from "@/lib/services/product-delivery/circle";
import { getCourses } from "@/lib/services/product-delivery";
import { useEffect, useState } from "react";
import { Form } from "antd";

type UseFetchProductsParams = {
  options?: SWRConfiguration;
  filters?: ProductsFilter;
};

function prepareFetchProducts(filters?: ProductsFilter) {
  let options: GetAllProductsParams = {};

  if (filters) {
    options.searchQuery = (filters.search as string) || "";
    if (filters.page) options.page = (filters.page as number) || 1;
    if (filters.entity) options.entity = filters.entity as string;
    if (filters.productLine) options.line = filters.productLine as string;
    if (filters.productFamily) options.family = filters.productFamily as string;
    if (filters.status) options.status = (filters.status as string) || "active";
    if (filters.isForever) options.isForever = filters.isForever as boolean;
  }
  return options;
}

export const useFetchProducts = (params?: UseFetchProductsParams) => {
  const options = prepareFetchProducts(params?.filters);

  return useSWR<ProductsListResponse, Error>(
    "useFetchProducts",
    () => getAllProducts(options),
    params?.options
  );
};

export const useFetchProduct = (id: string) => {
  return useSWR<Product>("useFetchProduct", () => getProduct(id));
};

export const useFetchUniqueBillingCycles = () => {
  return useSWR<number[]>("unique-billing-cycles", () =>
    getUniqueBillingCycles()
  );
};

/**
 * Hook to handle the product form
 * @returns
 */
export const useProductForm = () => {
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<any>({
    courses: [],
    filteredSpaces: [],
    filteredSpaceGroups: [],
    spaceGroups: [],
    spaces: [],
  });

  const updateCourses = async () => {
    const tempCourses = await getCourses(true);
    setFormData({
      ...formData,
      courses: tempCourses,
    });
  };

  const updateCommunity = async () => {
    const tempComm = await getCommunityInfo(true);
    setFormData({
      ...formData,
      spaceGroups: tempComm.spaceGroups,
      spaces: tempComm.spaces,
    });
  };

  const filterCommunity = (value: any) => {
    setFormData({
      ...formData,
      filteredSpaceGroups: [],
      filteredSpaces: [],
    });
    form.setFieldsValue({
      spaceGroup: undefined,
      spaces: undefined,
    });
    if (process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT === "staging") {
      setFormData({
        ...formData,
        filteredSpaceGroups: formData.spaceGroups,
        filteredSpaces: formData.spaces,
      });
    } else {
      if (value === "PXL") {
        const filteredSpaceGroups = formData.spaceGroups.filter(
          (x: any) =>
            x.community_id.toString() ===
            process.env.NEXT_PUBLIC_PXL_COMMUNITY_ID
        );
        setFormData({
          ...formData,
          filteredSpaceGroups: filteredSpaceGroups,
        });
      }
      if (value === "PXS") {
        const filteredSpaceGroups = formData.spaceGroups.filter(
          (x: any) =>
            x.community_id.toString() ===
            process.env.NEXT_PUBLIC_PXS_COMMUNITY_ID
        );
        setFormData({
          ...formData,
          filteredSpaceGroups: filteredSpaceGroups,
        });
      }
    }
  };

  const filterSpaceGroups = (values: any) => {
    const newSpaces = formData.spaces.filter((x: any) =>
      values.includes(x.space_group_id)
    );
    setFormData({
      ...formData,
      filteredSpaces: newSpaces,
    });
  };

  const fetchData = async () => {
    const [comm, courseResult] = await Promise.all([
      getCommunityInfo(false),
      getCourses(false),
    ]);
    setFormData({
      ...formData,
      courses: courseResult,
      spaceGroups: comm.spaceGroups,
      spaces: comm.spaces,
    });
  };

  useEffect(() => {
    fetchData();
  }, []);

  return {
    form,
    formData,
    filterCommunity,
    filterSpaceGroups,
    setFormData,
    updateCourses,
    updateCommunity,
  };
};
