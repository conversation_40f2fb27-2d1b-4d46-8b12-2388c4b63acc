"use server";

import { osRequest } from "./utils";
import { WebhooksFilter, WebhooksListResponse } from "@/types";

const baseUrl = `${process.env.API_URL}/webhooks`;

/**
 * Retrieves webhooks from the Paradox API.
 * @param params - The parameters for the webhooks.
 * @returns A Promise that resolves to the webhooks.
 */
export const getWebhooks = async (
  params: WebhooksFilter
): Promise<WebhooksListResponse> => {
  let requestQuery = new URL(`${baseUrl}?`);
  const query = new URLSearchParams();
  query.append("sender", params.sender);
  query.append("limit", String(params.limit) || "25");
  query.append("page", String(params.page) || "1");
  if (params.status) {
    query.append("status", params.status);
  }
  query.append("orderBy", "DESC");
  requestQuery.search = query.toString();
  const res = await osRequest(requestQuery.toString(), "GET");

  return res;
};
