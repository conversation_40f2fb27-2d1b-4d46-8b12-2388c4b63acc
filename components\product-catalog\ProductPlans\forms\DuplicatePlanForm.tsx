"use client";

import { Col, Form, Input, Row, Typography } from "antd";
import { useState } from "react";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { PlanDTO } from "@/types";

const { Text } = Typography;

type DuplicatePlanFormProps = {
  currentPlan: PlanDTO;
  isModalOpen: boolean;
  handleCancel: () => void;
  handleUpdate: (values: any) => void;
};

export default function DuplicatePlanForm({
  currentPlan,
  isModalOpen,
  handleCancel,
  handleUpdate,
}: DuplicatePlanFormProps) {
  const [duplicatePlanForm] = Form.useForm();
  const [isFormValid, setIsFormValid] = useState(false);

  const validateSku = (_: any, value: any, callback: any) => {
    if (!value) {
      callback(new Error("SKU is required"));
    } else if (value === currentPlan.crmSku) {
      callback(new Error("SKU cannot be the same as the current one"));
    } else {
      callback();
    }
  };

  return (
    <DrawerFormContainer
      isOpen={isModalOpen}
      onCancel={() => handleCancel()}
      onClose={() => handleCancel()}
      onSubmit={() =>
        handleUpdate({ sku: duplicatePlanForm.getFieldValue("sku") })
      }
      submitText="Duplicate"
      title="Duplicate Plan"
      submitButtonProps={{
        disabled: !isFormValid,
      }}
    >
      <Row>
        {/* Display current SKU and explain we need a new sku to duplicate */}
        <Col span={24}>
          <Text>
            Current SKU: <pre>{currentPlan.crmSku}</pre>
          </Text>
          <Text>
            We need a new SKU to duplicate this plan. Please enter a new SKU
            below.
          </Text>
        </Col>
      </Row>
      <Form
        form={duplicatePlanForm}
        layout="vertical"
        initialValues={{ sku: "" }}
        onFieldsChange={() => {
          setIsFormValid(
            duplicatePlanForm.getFieldValue("sku").length > 0 &&
              duplicatePlanForm.getFieldValue("sku") !== currentPlan.crmSku
          );
        }}
      >
        <Form.Item
          name="sku"
          label="SKU"
          rules={[{ required: true, validator: validateSku }]}
        >
          <Input />
        </Form.Item>
      </Form>
    </DrawerFormContainer>
  );
}
