"use client";

import { App, Form, Input, Select, Typography } from "antd";

import DrawerFormContainer from "@/components/core/DrawerFormContainer";
import { useFetchProductLines } from "@/hooks/product-catalog/productLines";

const { Title, Text } = Typography;
const rules = [{ required: true, message: "This value is required!" }];

type CreateProductFamilyFormProps = {
  isOpen: boolean;
  onAddNewProduct: (values: any) => void;
  onDismiss: Function;
};

export default function CreateProductFamilyForm({
  isOpen,
  onAddNewProduct,
  onDismiss,
}: CreateProductFamilyFormProps) {
  const [form] = Form.useForm();
  const { data: productLines } = useFetchProductLines();
  const { modal } = App.useApp();

  const handleCancel = async () => {
    modal.warning({
      title: "Are you sure you want to leave this page?",
      content: "You will lose all unsaved changes",
      onOk: () => {
        onDismiss();
        form.resetFields();
      },
      okText: "Confirm",
      okButtonProps: { danger: true },
      cancelButtonProps: { ghost: true },
      cancelText: "Cancel",
    });
  };

  return (
    <DrawerFormContainer
      isOpen={isOpen}
      onCancel={handleCancel}
      onClose={handleCancel}
      onSubmit={() => {
        form.submit();
      }}
      submitText="Create"
      title="Create a product family"
      submitButtonProps={{
        "data-testid": "create-product-family-form-btn",
      }}
    >
      <Title level={5}>Product family settings</Title>
      <Form
        form={form}
        layout="vertical"
        style={{ maxWidth: 800 }}
        onFinish={onAddNewProduct}
      >
        <Form.Item
          label="Product family name"
          name="name"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input
            placeholder="Product family name"
            data-testid="create-product-family-name-input"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          Name that helps you identify this product family internally.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product family description"
          name="description"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Input
            placeholder="EDEC"
            data-testid="create-product-family-description-input"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product family description attached to this product.
        </Text>
        <br />
        <br />
        <Form.Item
          label="Product line"
          name="productLine"
          style={{ marginBottom: "0px" }}
          rules={rules}
        >
          <Select
            allowClear
            placeholder="PXL"
            options={productLines?.items?.map((val) => {
              return { value: val.id, label: val.name };
            })}
            data-testid="create-product-family-product-line-select"
          />
        </Form.Item>
        <Text style={{ fontSize: 13, color: "darkgray" }}>
          The product line which contains this product family.
        </Text>
        <br />
        <br />
      </Form>
    </DrawerFormContainer>
  );
}
