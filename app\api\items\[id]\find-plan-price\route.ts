import { initChargebee, findRelatedItemPrices } from "@/server/chargebee";
import { NextResponse } from "next/server";

import { FindPlanPriceParams } from "@/types";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the POST request to get the plan price for a chargebee plan.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved plan price.
 */
export async function POST(req: any) {
  try {
    const body = (await req.json()) as FindPlanPriceParams;
    const response = await findRelatedItemPrices(body.planId, body.currency);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_SEARCH_ITEM_PRICES", details: error },
      { status: 400 }
    );
  }
}
