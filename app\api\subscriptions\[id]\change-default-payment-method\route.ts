import { ChangeDefaultPaymentMethodParams } from "@/types";
import { changeDefaultPaymentMethod, initChargebee } from "@/server/chargebee";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

initChargebee();

/**
 * Handles the POST request to change the default payment method of a subscription.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns A JSON response containing the retrieved subscriptions or an error message.
 */
export async function POST(req: any) {
  try {
    const body = (await req.json()) as ChangeDefaultPaymentMethodParams;
    const response = await changeDefaultPaymentMethod(body);
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      { error: "CANNOT_CHANGE_DEFAULT_PAYMENT_METHOD", details: error },
      { status: 400 }
    );
  }
}
