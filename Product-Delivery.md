# SSO

TODO - wait until we have the correct access on LW + Circle

# Learnworlds

1. When the subscription is created for a customer, we assign a tag to the LW user corresponding to our `offer_id` (using: https://www.learnworlds.dev/docs/api/b9034eb1ecfaf-update-user-tags
   )

2.

- For each access to be assigned, we'll enroll the LW user in a course (they're all free) with : https://www.learnworlds.dev/docs/api/3d5e79f96b44a-enroll-user-to-product

https://www.learnworlds.dev/docs/api/5cc9374300b99-webhooks#when-a-user-enrolls-in-a-free-course

-

# Circle
