"use server";

import {
  GetAllProductsParams,
  Product,
  ProductParams,
  ProductUpdateParams,
  ProductsListResponse,
} from "@/types";
import { osRequest } from "./utils";

const baseUrl = `${process.env.API_URL}/products`;

/**
 * Activates a product by updating its status to "active".
 * @param id - The ID of the product to activate.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export const activateProduct = async (id: number): Promise<any> => {
  try {
    let val = {
      status: "active",
    };
    const res = await osRequest(`${baseUrl}/${id}`, "PATCH", val);

    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};

/**
 * Archives a product by updating its status to "draft".
 * @param id - The ID of the product to be archived.
 * @returns A Promise that resolves to the JSON response from the server.
 */
export const archiveProduct = async (id: number): Promise<any> => {
  try {
    let val = {
      status: "draft",
    };
    const res = await osRequest(`${baseUrl}/${id}`, "PATCH", val);

    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};

/**
 * Creates a new product.
 * @param params - The parameters for creating the product.
 * @returns A Promise that resolves to the response data from the server.
 */
export const createProduct = async (params: ProductParams): Promise<any> => {
  try {
    const res = await osRequest(baseUrl, "POST", params);
    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};

export const getAllProducts = async (
  params: GetAllProductsParams
): Promise<ProductsListResponse> => {
  let requestQuery = new URL(`${baseUrl}`);
  const query = new URLSearchParams();

  const paramMapping: Record<keyof GetAllProductsParams, (val: any) => string> =
    {
      limit: String,
      page: String,
      searchQuery: String,
      line: String,
      family: String,
      status: String,
      entity: String,
      isForever: String,
    };

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      query.append(key, paramMapping[key as keyof GetAllProductsParams](value));
    }
  });

  requestQuery.search = query.toString();

  const res = await osRequest(requestQuery.href, "GET");

  return res;
};

/**
 * Retrieves product information from the backend API.
 * @param code - The product code.
 * @returns A Promise that resolves to the product information.
 */
export const getProduct = async (code: string): Promise<Product> => {
  try {
    const res = await osRequest(`${baseUrl}/${code}`, "GET");
    return res;
  } catch (err) {
    console.error("request result: ", err);
    throw new Error("Failed to fetch product");
  }
};

/**
 * Retrieves product ids for given billing cycles
 *
 * @param ids - An array of string IDs representing the billing cycles.
 * @returns A Promise that resolves to the response data from the server.
 */
export const getProductsForBillingCycles = async (
  ids: string[]
): Promise<any> => {
  try {
    let requestQuery = new URL(`${baseUrl}/getIdsForBillingCycles?`);
    const query = new URLSearchParams();
    ids.forEach((item) => {
      query.append("cycles", item);
    });
    requestQuery.search = query.toString();
    const res = await osRequest(requestQuery.href, "GET");
    return res;
  } catch (err) {
    console.error("cannot getProductsForBillingCycles: ", err);
  }
};

/**
 * Updates a product.
 *
 * @param params - The parameters for updating the product.
 * @returns A Promise that resolves to the updated product.
 */
export const updateProduct = async (
  params: ProductUpdateParams
): Promise<any> => {
  try {
    const res = await osRequest(`${baseUrl}/${params.id}`, "PATCH", params);
    return res;
  } catch (err) {
    console.error("request result: ", err);
  }
};
