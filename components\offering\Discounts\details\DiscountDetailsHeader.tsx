"use client";

import { Button, Descriptions, Space, Tag, Typography } from "antd";
import { BorderBottomOutlined, EditOutlined } from "@ant-design/icons";

import PageHeader from "@/components/core/PageHeader";
import { Discount } from "@/types";

const { Text } = Typography;

type DiscountDetailsHeaderProps = {
  discount: Discount | undefined;
  onArchive: () => void;
  onActivate: () => void;
  onEdit: () => void;
};

export default function DiscountDetailsHeader({
  discount,
  onArchive,
  onActivate,
  onEdit,
}: DiscountDetailsHeaderProps) {
  return (
    <PageHeader
      title="Discount Details"
      actions={
        <Space size="small" direction="vertical">
          <Button type="primary" icon={<EditOutlined />} onClick={onEdit}>
            Edit Discount
          </Button>
          {discount?.status === "active" ? (
            <Button onClick={onArchive} icon={<BorderBottomOutlined />}>
              Switch to disable
            </Button>
          ) : (
            <Button onClick={onActivate} icon={<BorderBottomOutlined />}>
              Activate
            </Button>
          )}
        </Space>
      }
    >
      <Descriptions title="" column={3} bordered size="small">
        <Descriptions.Item label="ID">
          <Text copyable={{ text: discount?.code }}>{discount?.code}</Text>
        </Descriptions.Item>
        <Descriptions.Item label="Status">
          <Tag
            color={
              discount?.status === "active"
                ? "green"
                : discount?.status === "disabled"
                  ? "orange"
                  : "red"
            }
          >
            {discount?.status === "active"
              ? "Active"
              : discount?.status === "disabled"
                ? "Disabled"
                : "Expired"}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Last modified">
          {discount?.updatedAt}
        </Descriptions.Item>
      </Descriptions>
    </PageHeader>
  );
}
